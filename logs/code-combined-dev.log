2025-06-28 00:29:48 [main] INFO  c.c.CodeCombinedApplication - Starting CodeCombinedApplication using Java 17.0.11 with PID 68110 (/Users/<USER>/Desktop/codes/java/code-list/code-combined-backend/target/classes started by liaozj in /Users/<USER>/Desktop/codes/java/code-list)
2025-06-28 00:29:48 [main] DEBUG c.c.CodeCombinedApplication - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-28 00:29:48 [main] INFO  c.c.CodeCombinedApplication - The following 1 profile is active: "dev"
2025-06-28 00:29:48 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.codecombined.CodeCombinedApplication]
2025-06-28 00:29:48 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.codecombined.CodeCombinedApplication]
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:178)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:416)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:289)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:349)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:118)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:775)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:597)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:455)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:323)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1342)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1331)
	at com.codecombined.CodeCombinedApplication.main(CodeCombinedApplication.java:20)
Caused by: org.springframework.context.annotation.ConflictingBeanDefinitionException: Annotation-specified bean name 'jwtUtil' for bean class [com.codecombined.util.IpUtil.JwtUtil] conflicts with existing, non-compatible bean definition of same name and class [com.codecombined.util.JwtUtil]
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.checkCandidate(ClassPathBeanDefinitionScanner.java:361)
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.doScan(ClassPathBeanDefinitionScanner.java:288)
	at org.springframework.context.annotation.ComponentScanAnnotationParser.parse(ComponentScanAnnotationParser.java:128)
	at org.springframework.context.annotation.ConfigurationClassParser.doProcessConfigurationClass(ConfigurationClassParser.java:296)
	at org.springframework.context.annotation.ConfigurationClassParser.processConfigurationClass(ConfigurationClassParser.java:245)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:196)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:164)
	... 13 common frames omitted
2025-06-28 00:31:00 [main] INFO  c.c.CodeCombinedApplication - Starting CodeCombinedApplication using Java 17.0.11 with PID 68299 (/Users/<USER>/Desktop/codes/java/code-list/code-combined-backend/target/classes started by liaozj in /Users/<USER>/Desktop/codes/java/code-list)
2025-06-28 00:31:00 [main] DEBUG c.c.CodeCombinedApplication - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-28 00:31:00 [main] INFO  c.c.CodeCombinedApplication - The following 1 profile is active: "dev"
2025-06-28 00:31:01 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-28 00:31:01 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-28 00:31:01 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 Redis repository interfaces.
2025-06-28 00:31:01 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-06-28 00:31:01 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-28 00:31:01 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-06-28 00:31:01 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-28 00:31:01 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 615 ms
2025-06-28 00:31:01 [main] DEBUG c.c.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-06-28 00:31:01 [main] DEBUG o.s.s.c.a.a.c.AuthenticationConfiguration$DefaultPasswordEncoderAuthenticationManagerBuilder - No authenticationProviders and no parentAuthenticationManager defined. Returning null.
2025-06-28 00:31:01 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@41026e5c, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@12421766, org.springframework.security.web.context.SecurityContextHolderFilter@199806aa, org.springframework.security.web.header.HeaderWriterFilter@4e647f39, org.springframework.web.filter.CorsFilter@2e44cb34, org.springframework.security.web.authentication.logout.LogoutFilter@63d0e8d, com.codecombined.security.JwtAuthenticationFilter@7c588adc, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@fa11fda, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@24e1e90a, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@731e0bff, org.springframework.security.web.session.SessionManagementFilter@1b465fa9, org.springframework.security.web.access.ExceptionTranslationFilter@adc3344, org.springframework.security.web.access.intercept.AuthorizationFilter@5600a278]
2025-06-28 00:31:02 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 47 mappings in 'requestMappingHandlerMapping'
2025-06-28 00:31:02 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/, /swagger-ui.html] in 'viewControllerHandlerMapping'
2025-06-28 00:31:02 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /swagger-ui/**, /doc.html, /favicon.ico, /static/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-06-28 00:31:02 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-28 00:31:02 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-28 00:31:02 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path ''
2025-06-28 00:31:02 [main] INFO  c.c.CodeCombinedApplication - Started CodeCombinedApplication in 1.616 seconds (process running for 1.808)

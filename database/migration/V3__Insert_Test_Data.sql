-- 插入测试数据
-- 版本: V3
-- 描述: 为开发和测试环境插入初始用户数据

-- 插入管理员用户
INSERT INTO `user` (
    `username`, `email`, `password`, `nickname`, `avatar`, `role`, `status`, 
    `email_verified`, `points`, `bio`, `location`, `company`, `github`, `website`,
    `create_time`, `update_time`, `deleted`
) VALUES (
    'admin', 
    '<EMAIL>', 
    '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGkqLoGYCOEYzjy6UYtKfVdaa', -- 密码: admin123
    '系统管理员',
    'https://api.dicebear.com/7.x/avataaars/svg?seed=admin',
    'ADMIN',
    1,
    1,
    1000,
    '系统管理员账户，负责平台管理和维护',
    '北京',
    'CodeCombined',
    'https://github.com/admin',
    'https://codecombined.com',
    NOW(),
    NOW(),
    0
);

-- 插入测试用户
INSERT INTO `user` (
    `username`, `email`, `password`, `nickname`, `avatar`, `role`, `status`, 
    `email_verified`, `points`, `bio`, `location`, `company`, `github`, `website`,
    `create_time`, `update_time`, `deleted`
) VALUES 
(
    'testuser1', 
    '<EMAIL>', 
    '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGkqLoGYCOEYzjy6UYtKfVdaa', -- 密码: admin123
    '测试用户1',
    'https://api.dicebear.com/7.x/avataaars/svg?seed=testuser1',
    'USER',
    1,
    1,
    500,
    '这是一个测试用户账户',
    '上海',
    '测试公司A',
    'https://github.com/testuser1',
    NULL,
    NOW(),
    NOW(),
    0
),
(
    'testuser2', 
    '<EMAIL>', 
    '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGkqLoGYCOEYzjy6UYtKfVdaa', -- 密码: admin123
    '测试用户2',
    'https://api.dicebear.com/7.x/avataaars/svg?seed=testuser2',
    'USER',
    1,
    1,
    750,
    '热爱编程的开发者',
    '深圳',
    '测试公司B',
    'https://github.com/testuser2',
    'https://testuser2.dev',
    NOW(),
    NOW(),
    0
),
(
    'testuser3', 
    '<EMAIL>', 
    '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGkqLoGYCOEYzjy6UYtKfVdaa', -- 密码: admin123
    '测试用户3',
    'https://api.dicebear.com/7.x/avataaars/svg?seed=testuser3',
    'USER',
    0,
    0,
    200,
    '新手程序员，正在学习中',
    '杭州',
    '测试公司C',
    NULL,
    NULL,
    NOW(),
    NOW(),
    0
),
(
    'testuser4', 
    '<EMAIL>', 
    '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGkqLoGYCOEYzjy6UYtKfVdaa', -- 密码: admin123
    '测试用户4',
    'https://api.dicebear.com/7.x/avataaars/svg?seed=testuser4',
    'USER',
    1,
    1,
    1200,
    '全栈开发工程师',
    '成都',
    '测试公司D',
    'https://github.com/testuser4',
    'https://testuser4.com',
    NOW(),
    NOW(),
    0
),
(
    'testuser5', 
    '<EMAIL>', 
    '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGkqLoGYCOEYzjy6UYtKfVdaa', -- 密码: admin123
    '测试用户5',
    'https://api.dicebear.com/7.x/avataaars/svg?seed=testuser5',
    'USER',
    1,
    1,
    300,
    '算法爱好者',
    '武汉',
    '测试公司E',
    'https://github.com/testuser5',
    NULL,
    NOW(),
    NOW(),
    0
),
(
    'manager1', 
    '<EMAIL>', 
    '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGkqLoGYCOEYzjy6UYtKfVdaa', -- 密码: admin123
    '管理员1',
    'https://api.dicebear.com/7.x/avataaars/svg?seed=manager1',
    'ADMIN',
    1,
    1,
    800,
    '平台管理员',
    '广州',
    'CodeCombined',
    'https://github.com/manager1',
    'https://manager1.dev',
    NOW(),
    NOW(),
    0
),
(
    'developer1', 
    '<EMAIL>', 
    '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGkqLoGYCOEYzjy6UYtKfVdaa', -- 密码: admin123
    '开发者1',
    'https://api.dicebear.com/7.x/avataaars/svg?seed=developer1',
    'USER',
    1,
    1,
    950,
    '资深Java开发工程师',
    '西安',
    '科技公司F',
    'https://github.com/developer1',
    'https://developer1.tech',
    NOW(),
    NOW(),
    0
),
(
    'student1', 
    '<EMAIL>', 
    '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGkqLoGYCOEYzjy6UYtKfVdaa', -- 密码: admin123
    '学生1',
    'https://api.dicebear.com/7.x/avataaars/svg?seed=student1',
    'USER',
    1,
    1,
    150,
    '计算机科学专业学生',
    '南京',
    '某某大学',
    'https://github.com/student1',
    NULL,
    NOW(),
    NOW(),
    0
),
(
    'inactive_user', 
    '<EMAIL>', 
    '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGkqLoGYCOEYzjy6UYtKfVdaa', -- 密码: admin123
    '禁用用户',
    'https://api.dicebear.com/7.x/avataaars/svg?seed=inactive',
    'USER',
    0,
    0,
    50,
    '这是一个被禁用的用户账户',
    '其他',
    NULL,
    NULL,
    NULL,
    NOW(),
    NOW(),
    0
);

-- 更新用户的最后登录时间（模拟用户活动）
UPDATE `user` SET 
    `last_login_time` = DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 30) DAY),
    `last_login_ip` = CONCAT('192.168.1.', FLOOR(RAND() * 255))
WHERE `username` IN ('admin', 'testuser1', 'testuser2', 'testuser4', 'testuser5', 'manager1', 'developer1', 'student1');

-- 为一些用户设置更早的注册时间
UPDATE `user` SET 
    `create_time` = DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 365) DAY)
WHERE `username` IN ('testuser1', 'testuser2', 'manager1', 'developer1');

-- 为最近注册的用户设置时间（本周内）
UPDATE `user` SET 
    `create_time` = DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 7) DAY)
WHERE `username` IN ('testuser5', 'student1', 'inactive_user');

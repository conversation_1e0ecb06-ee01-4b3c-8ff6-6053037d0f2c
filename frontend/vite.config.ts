import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import { visualizer } from 'rollup-plugin-visualizer'
import viteCompression from 'vite-plugin-compression'

// https://vitejs.dev/config/
export default defineConfig(() => {
  return {
    plugins: [
      vue({
        script: {
          defineModel: true,
          propsDestructure: true
        }
      }),
      visualizer({
        open: true, // 在默认浏览器中打开报告
        gzipSize: true, // 显示 Gzip 压缩大小
        brotliSize: true // 显示 Brotli 压缩大小
      }),
      viteCompression({
        verbose: true,
        disable: false,
        threshold: 10240, // 大于 10kb 的文件才会被压缩
        algorithm: 'gzip',
        ext: '.gz'
      }),
      viteCompression({
        verbose: true,
        disable: false,
        threshold: 10240,
        algorithm: 'brotliCompress',
        ext: '.br'
      })
    ],
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
      },
    },
    server: {
      port: 3000,
      open: true,
      // 禁用代理，使用 mock 数据
      // proxy: {
      //   '/api': {
      //     target: 'http://localhost:8080',
      //     changeOrigin: true,
      //     rewrite: (path: string) => path.replace(/^\/api/, '/api')
      //   }
      // }
    },
    build: {
      outDir: 'dist',
      assetsDir: 'assets',
      sourcemap: false,
      rollupOptions: {
        output: {
          chunkFileNames: 'assets/js/[name]-[hash].js',
          entryFileNames: 'assets/js/[name]-[hash].js',
          assetFileNames: 'assets/[ext]/[name]-[hash].[ext]',
          manualChunks (id: string) {
            if (id.includes('node_modules')) {
              // 将 node_modules 中的模块打包到 vendor chunk
              return 'vendor'
            }
          }
        }
      }
    }
  }
})

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导航功能测试</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: #f8f9fa; 
        }
        .container { max-width: 1000px; margin: 0 auto; }
        .header { 
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); 
            color: white; 
            padding: 20px; 
            border-radius: 10px; 
            margin-bottom: 20px; 
            text-align: center;
        }
        .test-section { 
            background: white; 
            padding: 20px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
            margin-bottom: 20px;
        }
        .test-section h3 { 
            margin-top: 0; 
            color: #333; 
            border-bottom: 2px solid #007bff; 
            padding-bottom: 10px; 
        }
        .nav-link { 
            display: inline-block; 
            padding: 10px 20px; 
            margin: 5px; 
            background: #007bff; 
            color: white; 
            text-decoration: none; 
            border-radius: 5px; 
            transition: background 0.3s;
        }
        .nav-link:hover { background: #0056b3; color: white; }
        .nav-link.auth-required { background: #28a745; }
        .nav-link.admin-required { background: #dc3545; }
        .status { 
            padding: 10px; 
            border-radius: 5px; 
            margin: 10px 0; 
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.warning { background: #fff3cd; color: #856404; }
        .status.error { background: #f8d7da; color: #721c24; }
        .route-info { 
            background: #e9ecef; 
            padding: 15px; 
            border-radius: 5px; 
            margin: 10px 0; 
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧭 前端导航功能测试</h1>
            <p>测试所有导航链接的路由跳转功能</p>
        </div>

        <div class="test-section">
            <h3>📍 当前路由信息</h3>
            <div class="route-info">
                <div>当前 URL: <span id="current-url"></span></div>
                <div>Base URL: <span id="base-url"></span></div>
                <div>测试时间: <span id="test-time"></span></div>
            </div>
        </div>

        <div class="test-section">
            <h3>🏠 主要导航链接</h3>
            <p>这些是头部导航栏中的主要链接：</p>
            <a href="/" class="nav-link">首页 (/)</a>
            <a href="/problems" class="nav-link">题目列表 (/problems)</a>
            <a href="/problemsets" class="nav-link">题集列表 (/problemsets)</a>
            <a href="/dashboard" class="nav-link auth-required">学习中心 (/dashboard)</a>
        </div>

        <div class="test-section">
            <h3>🔐 认证相关链接</h3>
            <p>用户认证和账户管理相关页面：</p>
            <a href="/login" class="nav-link">登录 (/login)</a>
            <a href="/register" class="nav-link">注册 (/register)</a>
            <a href="/forgot-password" class="nav-link">忘记密码 (/forgot-password)</a>
            <a href="/profile" class="nav-link auth-required">个人资料 (/profile)</a>
        </div>

        <div class="test-section">
            <h3>➕ 创建功能链接</h3>
            <p>需要登录后才能访问的创建功能：</p>
            <a href="/problems/create" class="nav-link auth-required">创建题目 (/problems/create)</a>
            <a href="/problemsets/create" class="nav-link auth-required">创建题集 (/problemsets/create)</a>
        </div>

        <div class="test-section">
            <h3>👑 管理员功能链接</h3>
            <p>需要管理员权限才能访问：</p>
            <a href="/admin" class="nav-link admin-required">管理控制台 (/admin)</a>
            <a href="/admin/email" class="nav-link admin-required">邮件管理 (/admin/email)</a>
        </div>

        <div class="test-section">
            <h3>📄 详情页面示例</h3>
            <p>带参数的动态路由：</p>
            <a href="/problems/1" class="nav-link">题目详情示例 (/problems/1)</a>
            <a href="/problems/15" class="nav-link">三数之和 (/problems/15)</a>
            <a href="/problemsets/1" class="nav-link">题集详情示例 (/problemsets/1)</a>
            <a href="/problemsets/10" class="nav-link">高频面试题 (/problemsets/10)</a>
        </div>

        <div class="test-section">
            <h3>❌ 错误页面测试</h3>
            <p>测试 404 页面和错误处理：</p>
            <a href="/nonexistent" class="nav-link">不存在的页面 (/nonexistent)</a>
            <a href="/problems/999" class="nav-link">不存在的题目 (/problems/999)</a>
        </div>

        <div class="test-section">
            <h3>🧪 自动化测试</h3>
            <button onclick="runNavigationTests()" class="nav-link" style="border: none; cursor: pointer;">
                运行导航测试
            </button>
            <div id="test-results"></div>
        </div>

        <div class="test-section">
            <h3>📋 测试说明</h3>
            <div class="status warning">
                <strong>颜色说明：</strong><br>
                🔵 蓝色：公开页面，无需登录<br>
                🟢 绿色：需要登录的页面<br>
                🔴 红色：需要管理员权限的页面
            </div>
            <div class="status success">
                <strong>测试方法：</strong><br>
                1. 点击上方链接测试页面跳转<br>
                2. 检查页面是否正确加载<br>
                3. 验证路由守卫是否正常工作<br>
                4. 测试不同用户状态下的访问权限
            </div>
        </div>
    </div>

    <script>
        // 更新当前路由信息
        function updateRouteInfo() {
            document.getElementById('current-url').textContent = window.location.href;
            document.getElementById('base-url').textContent = window.location.origin;
            document.getElementById('test-time').textContent = new Date().toLocaleString();
        }

        // 自动化导航测试
        async function runNavigationTests() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<div class="status warning">正在运行测试...</div>';

            const testRoutes = [
                { path: '/', name: '首页', shouldWork: true },
                { path: '/problems', name: '题目列表', shouldWork: true },
                { path: '/problemsets', name: '题集列表', shouldWork: true },
                { path: '/login', name: '登录页面', shouldWork: true },
                { path: '/register', name: '注册页面', shouldWork: true },
                { path: '/problems/1', name: '题目详情', shouldWork: true },
                { path: '/problemsets/1', name: '题集详情', shouldWork: true },
                { path: '/nonexistent', name: '404测试', shouldWork: false }
            ];

            let results = [];
            
            for (const route of testRoutes) {
                try {
                    const response = await fetch(route.path, { method: 'HEAD' });
                    const status = response.status;
                    const success = route.shouldWork ? (status === 200) : (status === 404);
                    
                    results.push({
                        ...route,
                        status,
                        success,
                        message: success ? '✅ 通过' : '❌ 失败'
                    });
                } catch (error) {
                    results.push({
                        ...route,
                        status: 'ERROR',
                        success: false,
                        message: '❌ 网络错误'
                    });
                }
            }

            // 显示测试结果
            const passedTests = results.filter(r => r.success).length;
            const totalTests = results.length;
            
            let html = `
                <div class="status ${passedTests === totalTests ? 'success' : 'error'}">
                    <strong>测试完成：${passedTests}/${totalTests} 通过</strong>
                </div>
                <div class="route-info">
            `;
            
            results.forEach(result => {
                html += `
                    <div>
                        ${result.message} ${result.name} (${result.path}) - 状态: ${result.status}
                    </div>
                `;
            });
            
            html += '</div>';
            resultsDiv.innerHTML = html;
        }

        // 页面加载时更新信息
        window.onload = function() {
            updateRouteInfo();
            console.log('导航测试页面已加载');
            console.log('当前 URL:', window.location.href);
        };

        // 监听路由变化（如果是 SPA）
        window.addEventListener('popstate', updateRouteInfo);
    </script>
</body>
</html>

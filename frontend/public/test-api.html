<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API 测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-result { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 3px; white-space: pre-wrap; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        button { padding: 8px 16px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>前端 Mock API 测试</h1>
    <p>这个页面用于测试前端的 mock 数据是否正常工作</p>
    
    <div class="test-section">
        <h3>基础测试</h3>
        <button onclick="testBasicAPI()">测试基础 API</button>
        <div id="basic-results"></div>
    </div>

    <div class="test-section">
        <h3>题目接口测试</h3>
        <button onclick="testProblems()">获取题目列表</button>
        <button onclick="testProblemDetail()">获取题目详情</button>
        <div id="problem-results"></div>
    </div>

    <div class="test-section">
        <h3>题集接口测试</h3>
        <button onclick="testProblemSets()">获取题集列表</button>
        <button onclick="testProblemSetDetail()">获取题集详情</button>
        <div id="problemset-results"></div>
    </div>

    <script type="module">
        // 导入 axios 和 mock 配置
        import axios from '/node_modules/axios/dist/esm/axios.js';
        import MockAdapter from '/node_modules/axios-mock-adapter/dist/index.js';

        // 手动设置 mock
        const mock = new MockAdapter(axios, { delayResponse: 500 });

        // 简单的 mock 配置
        mock.onGet('/api/test').reply(200, {
            code: 200,
            message: 'Mock 测试成功',
            data: { timestamp: new Date().toISOString() }
        });

        mock.onGet('/api/problems').reply(200, {
            code: 200,
            message: 'success',
            data: {
                records: [
                    { id: 1, title: '两数之和', difficulty: 'EASY' },
                    { id: 2, title: '两数相加', difficulty: 'MEDIUM' }
                ],
                total: 2,
                current: 1,
                size: 10,
                pages: 1
            }
        });

        mock.onGet('/api/problemsets').reply(200, {
            code: 200,
            message: 'success',
            data: {
                records: [
                    { id: 1, name: '动态规划专题', problemCount: 10 },
                    { id: 2, name: '数组专题', problemCount: 15 }
                ],
                total: 2,
                current: 1,
                size: 10,
                pages: 1
            }
        });

        // 全局函数
        window.testBasicAPI = async function() {
            try {
                const response = await axios.get('/api/test');
                displayResult('basic-results', '基础 API 测试', { success: true, data: response.data });
            } catch (error) {
                displayResult('basic-results', '基础 API 测试', { success: false, error: error.message });
            }
        };

        window.testProblems = async function() {
            try {
                const response = await axios.get('/api/problems');
                displayResult('problem-results', '题目列表', { success: true, data: response.data });
            } catch (error) {
                displayResult('problem-results', '题目列表', { success: false, error: error.message });
            }
        };

        window.testProblemDetail = async function() {
            try {
                const response = await axios.get('/api/problems/1');
                displayResult('problem-results', '题目详情', { success: true, data: response.data });
            } catch (error) {
                displayResult('problem-results', '题目详情', { success: false, error: error.message });
            }
        };

        window.testProblemSets = async function() {
            try {
                const response = await axios.get('/api/problemsets');
                displayResult('problemset-results', '题集列表', { success: true, data: response.data });
            } catch (error) {
                displayResult('problemset-results', '题集列表', { success: false, error: error.message });
            }
        };

        window.testProblemSetDetail = async function() {
            try {
                const response = await axios.get('/api/problemsets/1');
                displayResult('problemset-results', '题集详情', { success: true, data: response.data });
            } catch (error) {
                displayResult('problemset-results', '题集详情', { success: false, error: error.message });
            }
        };

        function displayResult(containerId, title, result) {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${result.success ? 'success' : 'error'}`;
            div.innerHTML = `
                <strong>${title}</strong>
                ${result.success ? 
                    `✅ 成功\n${JSON.stringify(result.data, null, 2)}` : 
                    `❌ 失败: ${result.error}`
                }
            `;
            container.appendChild(div);
        }

        window.displayResult = displayResult;

        console.log('Mock API 测试页面已加载');
    </script>
</body>
</html>

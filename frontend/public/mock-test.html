<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mock API 完整测试</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: #f5f5f5; 
        }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            padding: 20px; 
            border-radius: 10px; 
            margin-bottom: 20px; 
            text-align: center;
        }
        .test-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); 
            gap: 20px; 
        }
        .test-section { 
            background: white; 
            padding: 20px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .test-section h3 { 
            margin-top: 0; 
            color: #333; 
            border-bottom: 2px solid #667eea; 
            padding-bottom: 10px; 
        }
        .test-result { 
            margin: 10px 0; 
            padding: 15px; 
            border-radius: 5px; 
            font-family: 'Courier New', monospace; 
            font-size: 12px; 
            white-space: pre-wrap; 
            max-height: 200px; 
            overflow-y: auto; 
        }
        .success { background: #d4edda; color: #155724; border-left: 4px solid #28a745; }
        .error { background: #f8d7da; color: #721c24; border-left: 4px solid #dc3545; }
        .loading { background: #fff3cd; color: #856404; border-left: 4px solid #ffc107; }
        button { 
            padding: 10px 15px; 
            margin: 5px; 
            border: none; 
            border-radius: 5px; 
            cursor: pointer; 
            background: #667eea; 
            color: white; 
            font-size: 14px; 
            transition: background 0.3s;
        }
        button:hover { background: #5a6fd8; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        .status-bar { 
            background: #e9ecef; 
            padding: 10px; 
            border-radius: 5px; 
            margin-bottom: 20px; 
            text-align: center; 
        }
        .status-indicator { 
            display: inline-block; 
            width: 12px; 
            height: 12px; 
            border-radius: 50%; 
            margin-right: 8px; 
        }
        .status-active { background: #28a745; }
        .status-inactive { background: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎭 Mock API 完整功能测试</h1>
            <p>测试前端应用的完整 Mock 数据配置</p>
        </div>

        <div class="status-bar">
            <span class="status-indicator status-active"></span>
            <strong>Mock 模式已激活</strong> - 所有 API 请求使用模拟数据
        </div>

        <div class="test-grid">
            <!-- 认证模块测试 -->
            <div class="test-section">
                <h3>🔐 认证模块</h3>
                <button onclick="testLogin()">测试登录</button>
                <button onclick="testRegister()">测试注册</button>
                <button onclick="testUserInfo()">获取用户信息</button>
                <div id="auth-results"></div>
            </div>

            <!-- 题目模块测试 -->
            <div class="test-section">
                <h3>🧩 题目模块</h3>
                <button onclick="testProblems()">获取题目列表</button>
                <button onclick="testProblemDetail()">获取题目详情</button>
                <button onclick="testProblemSearch()">搜索题目</button>
                <button onclick="testCreateProblem()">创建题目</button>
                <div id="problem-results"></div>
            </div>

            <!-- 题集模块测试 -->
            <div class="test-section">
                <h3>📚 题集模块</h3>
                <button onclick="testProblemSets()">获取题集列表</button>
                <button onclick="testProblemSetDetail()">获取题集详情</button>
                <button onclick="testProblemSetProblems()">获取题集题目</button>
                <button onclick="testCreateProblemSet()">创建题集</button>
                <div id="problemset-results"></div>
            </div>

            <!-- 提交模块测试 -->
            <div class="test-section">
                <h3>📝 提交模块</h3>
                <button onclick="testSubmissions()">获取提交列表</button>
                <button onclick="testSubmissionDetail()">获取提交详情</button>
                <button onclick="testSubmitCode()">提交代码</button>
                <button onclick="testSubmissionStats()">提交统计</button>
                <div id="submission-results"></div>
            </div>

            <!-- 用户模块测试 -->
            <div class="test-section">
                <h3>👤 用户模块</h3>
                <button onclick="testUserProfile()">获取用户配置</button>
                <button onclick="testUserStats()">获取用户统计</button>
                <button onclick="testUserSubmissions()">用户提交历史</button>
                <button onclick="testUserFavorites()">用户收藏</button>
                <div id="user-results"></div>
            </div>

            <!-- 统计模块测试 -->
            <div class="test-section">
                <h3>📊 统计模块</h3>
                <button onclick="testPublicStats()">公开统计</button>
                <button onclick="testPlatformStats()">平台统计</button>
                <button onclick="testProblemStats()">题目统计</button>
                <div id="stats-results"></div>
            </div>

            <!-- 管理员模块测试 -->
            <div class="test-section">
                <h3>👑 管理员模块</h3>
                <button onclick="testAdminUsers()">用户管理</button>
                <button onclick="testAdminEmail()">邮件发送</button>
                <button onclick="testAdminStats()">系统统计</button>
                <div id="admin-results"></div>
            </div>

            <!-- 综合测试 -->
            <div class="test-section">
                <h3>🎯 综合测试</h3>
                <button onclick="runAllTests()">运行所有测试</button>
                <button onclick="clearResults()">清空结果</button>
                <div id="comprehensive-results"></div>
            </div>
        </div>
    </div>

    <script>
        // API 基础配置
        const API_BASE = '/api';
        let authToken = null;

        // 通用请求函数
        async function makeRequest(url, options = {}) {
            const headers = {
                'Content-Type': 'application/json',
                ...options.headers
            };

            if (authToken) {
                headers['Authorization'] = `Bearer ${authToken}`;
            }

            try {
                const response = await fetch(API_BASE + url, {
                    headers,
                    ...options
                });
                
                const data = await response.json();
                return { 
                    success: response.ok, 
                    data, 
                    status: response.status,
                    url: API_BASE + url,
                    method: options.method || 'GET'
                };
            } catch (error) {
                return { 
                    success: false, 
                    error: error.message,
                    url: API_BASE + url,
                    method: options.method || 'GET'
                };
            }
        }

        // 显示结果函数
        function displayResult(containerId, title, result) {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${result.success ? 'success' : 'error'}`;
            
            const timestamp = new Date().toLocaleTimeString();
            const statusIcon = result.success ? '✅' : '❌';
            
            div.innerHTML = `[${timestamp}] ${statusIcon} ${title}
${result.success ? 
    `状态: ${result.status}
URL: ${result.method} ${result.url}
响应: ${JSON.stringify(result.data, null, 2)}` : 
    `错误: ${result.error}
URL: ${result.method} ${result.url}`
}`;
            
            container.appendChild(div);
            container.scrollTop = container.scrollHeight;
        }

        // 显示加载状态
        function showLoading(containerId, title) {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = 'test-result loading';
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ⏳ ${title} - 请求中...`;
            container.appendChild(div);
            return div;
        }

        // 认证模块测试
        async function testLogin() {
            const loadingDiv = showLoading('auth-results', '用户登录');
            const result = await makeRequest('/auth/login', {
                method: 'POST',
                body: JSON.stringify({
                    email: '<EMAIL>',
                    password: 'admin123'
                })
            });
            
            if (result.success && result.data.data && result.data.data.token) {
                authToken = result.data.data.token;
            }
            
            loadingDiv.remove();
            displayResult('auth-results', '用户登录', result);
        }

        async function testRegister() {
            const loadingDiv = showLoading('auth-results', '用户注册');
            const result = await makeRequest('/auth/register', {
                method: 'POST',
                body: JSON.stringify({
                    username: 'newuser',
                    email: '<EMAIL>',
                    password: 'password123',
                    nickname: '新用户'
                })
            });
            loadingDiv.remove();
            displayResult('auth-results', '用户注册', result);
        }

        async function testUserInfo() {
            const loadingDiv = showLoading('auth-results', '获取用户信息');
            const result = await makeRequest('/users/me');
            loadingDiv.remove();
            displayResult('auth-results', '获取用户信息', result);
        }

        // 题目模块测试
        async function testProblems() {
            const loadingDiv = showLoading('problem-results', '获取题目列表');
            const result = await makeRequest('/problems?current=1&size=5');
            loadingDiv.remove();
            displayResult('problem-results', '获取题目列表', result);
        }

        async function testProblemDetail() {
            const loadingDiv = showLoading('problem-results', '获取题目详情');
            const result = await makeRequest('/problems/1');
            loadingDiv.remove();
            displayResult('problem-results', '获取题目详情', result);
        }

        async function testProblemSearch() {
            const loadingDiv = showLoading('problem-results', '搜索题目');
            const result = await makeRequest('/problems/search?keyword=两数');
            loadingDiv.remove();
            displayResult('problem-results', '搜索题目', result);
        }

        async function testCreateProblem() {
            const loadingDiv = showLoading('problem-results', '创建题目');
            const result = await makeRequest('/problems', {
                method: 'POST',
                body: JSON.stringify({
                    title: '测试题目',
                    description: '这是一个测试题目',
                    difficulty: 'EASY',
                    tags: '测试,算法'
                })
            });
            loadingDiv.remove();
            displayResult('problem-results', '创建题目', result);
        }

        // 题集模块测试
        async function testProblemSets() {
            const loadingDiv = showLoading('problemset-results', '获取题集列表');
            const result = await makeRequest('/problemsets?current=1&size=5');
            loadingDiv.remove();
            displayResult('problemset-results', '获取题集列表', result);
        }

        async function testProblemSetDetail() {
            const loadingDiv = showLoading('problemset-results', '获取题集详情');
            const result = await makeRequest('/problemsets/1');
            loadingDiv.remove();
            displayResult('problemset-results', '获取题集详情', result);
        }

        async function testProblemSetProblems() {
            const loadingDiv = showLoading('problemset-results', '获取题集题目');
            const result = await makeRequest('/problemsets/1/problems');
            loadingDiv.remove();
            displayResult('problemset-results', '获取题集题目', result);
        }

        async function testCreateProblemSet() {
            const loadingDiv = showLoading('problemset-results', '创建题集');
            const result = await makeRequest('/problemsets', {
                method: 'POST',
                body: JSON.stringify({
                    name: '测试题集',
                    description: '这是一个测试题集',
                    isPublic: true
                })
            });
            loadingDiv.remove();
            displayResult('problemset-results', '创建题集', result);
        }

        // 提交模块测试
        async function testSubmissions() {
            const loadingDiv = showLoading('submission-results', '获取提交列表');
            const result = await makeRequest('/submissions?current=1&size=5');
            loadingDiv.remove();
            displayResult('submission-results', '获取提交列表', result);
        }

        async function testSubmissionDetail() {
            const loadingDiv = showLoading('submission-results', '获取提交详情');
            const result = await makeRequest('/submissions/1');
            loadingDiv.remove();
            displayResult('submission-results', '获取提交详情', result);
        }

        async function testSubmitCode() {
            const loadingDiv = showLoading('submission-results', '提交代码');
            const result = await makeRequest('/submissions', {
                method: 'POST',
                body: JSON.stringify({
                    problemId: 1,
                    language: 'Java',
                    code: 'public class Solution { public int[] twoSum(int[] nums, int target) { return new int[]{0, 1}; } }'
                })
            });
            loadingDiv.remove();
            displayResult('submission-results', '提交代码', result);
        }

        async function testSubmissionStats() {
            const loadingDiv = showLoading('submission-results', '提交统计');
            const result = await makeRequest('/submissions/stats?userId=1');
            loadingDiv.remove();
            displayResult('submission-results', '提交统计', result);
        }

        // 用户模块测试
        async function testUserProfile() {
            const loadingDiv = showLoading('user-results', '获取用户配置');
            const result = await makeRequest('/users/1/profile');
            loadingDiv.remove();
            displayResult('user-results', '获取用户配置', result);
        }

        async function testUserStats() {
            const loadingDiv = showLoading('user-results', '获取用户统计');
            const result = await makeRequest('/users/1/stats');
            loadingDiv.remove();
            displayResult('user-results', '获取用户统计', result);
        }

        async function testUserSubmissions() {
            const loadingDiv = showLoading('user-results', '用户提交历史');
            const result = await makeRequest('/users/1/submissions?current=1&size=5');
            loadingDiv.remove();
            displayResult('user-results', '用户提交历史', result);
        }

        async function testUserFavorites() {
            const loadingDiv = showLoading('user-results', '用户收藏');
            const result = await makeRequest('/users/1/favorites?current=1&size=5');
            loadingDiv.remove();
            displayResult('user-results', '用户收藏', result);
        }

        // 统计模块测试
        async function testPublicStats() {
            const loadingDiv = showLoading('stats-results', '公开统计');
            const result = await makeRequest('/stats/public/users');
            loadingDiv.remove();
            displayResult('stats-results', '公开统计', result);
        }

        async function testPlatformStats() {
            const loadingDiv = showLoading('stats-results', '平台统计');
            const result = await makeRequest('/stats/public/platform');
            loadingDiv.remove();
            displayResult('stats-results', '平台统计', result);
        }

        async function testProblemStats() {
            const loadingDiv = showLoading('stats-results', '题目统计');
            const result = await makeRequest('/stats/problems/1');
            loadingDiv.remove();
            displayResult('stats-results', '题目统计', result);
        }

        // 管理员模块测试
        async function testAdminUsers() {
            const loadingDiv = showLoading('admin-results', '用户管理');
            const result = await makeRequest('/admin/users?current=1&size=5');
            loadingDiv.remove();
            displayResult('admin-results', '用户管理', result);
        }

        async function testAdminEmail() {
            const loadingDiv = showLoading('admin-results', '邮件发送');
            const result = await makeRequest('/admin/email/send', {
                method: 'POST',
                body: JSON.stringify({
                    recipients: ['<EMAIL>'],
                    subject: '测试邮件',
                    content: '这是一封测试邮件',
                    type: 'SYSTEM_NOTICE'
                })
            });
            loadingDiv.remove();
            displayResult('admin-results', '邮件发送', result);
        }

        async function testAdminStats() {
            const loadingDiv = showLoading('admin-results', '系统统计');
            const result = await makeRequest('/admin/stats/overview');
            loadingDiv.remove();
            displayResult('admin-results', '系统统计', result);
        }

        // 综合测试
        async function runAllTests() {
            const loadingDiv = showLoading('comprehensive-results', '运行所有测试');
            
            const tests = [
                { name: '登录测试', func: testLogin },
                { name: '题目列表', func: testProblems },
                { name: '题集列表', func: testProblemSets },
                { name: '提交列表', func: testSubmissions },
                { name: '用户配置', func: testUserProfile },
                { name: '公开统计', func: testPublicStats }
            ];
            
            let passed = 0;
            let total = tests.length;
            
            for (const test of tests) {
                try {
                    await test.func();
                    passed++;
                } catch (error) {
                    console.error(`测试失败: ${test.name}`, error);
                }
                await new Promise(resolve => setTimeout(resolve, 500)); // 延迟500ms
            }
            
            loadingDiv.remove();
            displayResult('comprehensive-results', '综合测试完成', {
                success: true,
                data: { 
                    message: `测试完成: ${passed}/${total} 通过`,
                    passRate: `${(passed/total*100).toFixed(1)}%`
                }
            });
        }

        // 清空结果
        function clearResults() {
            const resultContainers = document.querySelectorAll('[id$="-results"]');
            resultContainers.forEach(container => {
                container.innerHTML = '';
            });
        }

        // 页面加载完成后的初始化
        window.onload = function() {
            console.log('🎭 Mock API 测试页面已加载');
            console.log('📍 API 基础地址:', API_BASE);
            console.log('🔧 开始测试前端 Mock 数据配置...');
            
            // 自动运行基础测试
            setTimeout(() => {
                testLogin();
            }, 1000);
        };
    </script>
</body>
</html>

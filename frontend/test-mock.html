<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mock API 测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-result { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        button { padding: 8px 16px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Mock API 测试页面</h1>
    
    <div class="test-section">
        <h3>题目相关接口测试</h3>
        <button onclick="testProblems()">测试获取题目列表</button>
        <button onclick="testProblemDetail()">测试获取题目详情</button>
        <button onclick="testProblemSearch()">测试题目搜索</button>
        <div id="problem-results"></div>
    </div>

    <div class="test-section">
        <h3>题集相关接口测试</h3>
        <button onclick="testProblemSets()">测试获取题集列表</button>
        <button onclick="testProblemSetDetail()">测试获取题集详情</button>
        <button onclick="testProblemSetProblems()">测试获取题集题目</button>
        <div id="problemset-results"></div>
    </div>

    <div class="test-section">
        <h3>统计相关接口测试</h3>
        <button onclick="testStats()">测试获取统计数据</button>
        <div id="stats-results"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001/api';

        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(API_BASE + url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                const data = await response.json();
                return { success: true, data, status: response.status };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function displayResult(containerId, title, result) {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${result.success ? 'success' : 'error'}`;
            div.innerHTML = `
                <strong>${title}</strong><br>
                ${result.success ? 
                    `✅ 成功 (${result.status})<br><pre>${JSON.stringify(result.data, null, 2)}</pre>` : 
                    `❌ 失败: ${result.error}`
                }
            `;
            container.appendChild(div);
        }

        async function testProblems() {
            const result = await makeRequest('/problems?current=1&size=5');
            displayResult('problem-results', '获取题目列表', result);
        }

        async function testProblemDetail() {
            const result = await makeRequest('/problems/1');
            displayResult('problem-results', '获取题目详情', result);
        }

        async function testProblemSearch() {
            const result = await makeRequest('/problems/search?keyword=两数');
            displayResult('problem-results', '题目搜索', result);
        }

        async function testProblemSets() {
            const result = await makeRequest('/problemsets?current=1&size=5');
            displayResult('problemset-results', '获取题集列表', result);
        }

        async function testProblemSetDetail() {
            const result = await makeRequest('/problemsets/1');
            displayResult('problemset-results', '获取题集详情', result);
        }

        async function testProblemSetProblems() {
            const result = await makeRequest('/problemsets/1/problems');
            displayResult('problemset-results', '获取题集题目', result);
        }

        async function testStats() {
            const result1 = await makeRequest('/stats/public/users');
            displayResult('stats-results', '获取用户统计', result1);
            
            const result2 = await makeRequest('/stats/public/platform');
            displayResult('stats-results', '获取平台统计', result2);
        }

        // 页面加载时自动运行一些基础测试
        window.onload = function() {
            console.log('Mock API 测试页面已加载');
            console.log('前端服务地址:', API_BASE);
        };
    </script>
</body>
</html>

<template>
  <div class="register-apple-bg">
    <div class="register-apple-card">
      <div class="text-center mb-4">
        <img src="/logo.svg" alt="logo" class="register-logo mb-3" />
        <h2 class="apple-title mb-1">登录 Code-Combined</h2>
        <p class="apple-subtitle">欢迎回来，请使用邮箱和密码登录</p>
      </div>
      <form @submit.prevent="handleLogin">
        <div class="form-row">
          <label for="email" class="form-label">邮箱</label>
          <input
            id="email"
            v-model="form.email"
            type="email"
            class="apple-input"
            :class="{ 'is-invalid': errors.email }"
            placeholder="邮箱"
            autocomplete="username"
            required
          >
        </div>
        <div v-if="errors.email" class="apple-error">{{ errors.email }}</div>
        <div class="form-row">
          <label for="password" class="form-label">密码</label>
          <div class="apple-password-row">
            <input
              id="password"
              v-model="form.password"
              :type="showPassword ? 'text' : 'password'"
              class="apple-input"
              :class="{ 'is-invalid': errors.password }"
              placeholder="密码"
              autocomplete="current-password"
              required
            >
            <button type="button" class="apple-eye-btn" @click="showPassword = !showPassword">
              <i :class="showPassword ? 'bi bi-eye-slash' : 'bi bi-eye'" />
            </button>
          </div>
        </div>
        <div v-if="errors.password" class="apple-error">{{ errors.password }}</div>
        <div class="form-row align-items-center" style="margin-bottom: 0.8rem;">
          <input id="rememberMe" v-model="form.rememberMe" type="checkbox" class="form-check-input me-1" style="margin-left:2px;" />
          <label for="rememberMe" class="form-check-label small" style="font-weight:400;">记住我</label>
        </div>
        <button type="submit" class="apple-btn" :disabled="loading">
          <span v-if="loading" class="spinner-border spinner-border-sm me-2"></span>
          登录
        </button>
        <div class="apple-bottom-link">
          <router-link to="/register">没有账号？注册</router-link>
        </div>
        <div v-if="isDevelopment" class="dev-quick-login mt-3">
          <div class="text-muted small mb-2">开发环境快速登录</div>
          <div class="d-flex gap-2">
            <button type="button" class="apple-code-btn flex-fill" @click="quickLoginAsAdmin" :disabled="loading">
              管理员
            </button>
            <button type="button" class="apple-code-btn flex-fill" @click="quickLoginAsUser" :disabled="loading">
              普通用户
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { showSuccess, showError } from '@/utils/message'
// 类型文件缺失时兜底
type LoginRequest = any
type User = any

const store = useStore()
const router = useRouter()

const form = reactive<LoginRequest>({
  email: '',
  password: '',
  rememberMe: false
})
const errors = reactive<{ [key: string]: string }>({})
const showPassword = ref(false)
const loading = ref(false)
const isDevelopment = import.meta.env.DEV

const validateForm = (): boolean => {
  Object.keys(errors).forEach(key => delete errors[key])
  if (!form.email) {
    errors.email = '请输入邮箱地址'
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email)) {
    errors.email = '邮箱格式不正确'
  }
  if (!form.password) {
    errors.password = '请输入密码'
  } else if (form.password.length < 6) {
    errors.password = '密码长度至少6位'
  }
  return Object.keys(errors).length === 0
}

const handleLogin = async () => {
  if (!validateForm()) return
  loading.value = true
  try {
    await store.dispatch('auth/login', form)
    const redirect = router.currentRoute.value.query.redirect as string || '/dashboard'
    router.push(redirect)
    showSuccess('登录成功！')
  } catch (error: any) {
    showError(error.message || '登录失败，请检查邮箱和密码')
  } finally {
    loading.value = false
  }
}

const quickLogin = async (user: Partial<User>, token: string) => {
  loading.value = true
  try {
    await store.dispatch('auth/setAuth', { token, user })
    router.push('/dashboard')
    showSuccess(`已作为 ${user.role} 身份登录！`)
  } catch (error: any) {
    showError('快速登录失败: ' + error.message)
  } finally {
    loading.value = false
  }
}
const quickLoginAsAdmin = () => {
  const adminUser: Partial<User> = {
    id: '1',
    username: 'admin',
    email: '<EMAIL>',
    role: 'ADMIN',
    status: 'ACTIVE',
  }
  const fakeToken = 'fake-admin-jwt-token'
  quickLogin(adminUser, fakeToken)
}
const quickLoginAsUser = () => {
  const normalUser: Partial<User> = {
    id: '2',
    username: 'testuser',
    email: '<EMAIL>',
    role: 'USER',
    status: 'ACTIVE',
  }
  const fakeToken = 'fake-user-jwt-token'
  quickLogin(normalUser, fakeToken)
}
</script>

<style scoped>
.register-apple-bg {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8fafc 0%, #f1f3f6 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}
.register-apple-card {
  width: 66vw;
  max-width: 520px;
  min-width: 320px;
  background: #fff;
  border-radius: 28px;
  box-shadow: 0 8px 32px 0 rgba(60,60,60,0.07);
  padding: 44px 38px 32px 38px;
  margin: 48px 0;
  display: flex;
  flex-direction: column;
  align-items: stretch;
}
.register-logo {
  width: 54px;
  height: 54px;
  margin-bottom: 12px;
}
.apple-title {
  font-size: 2rem;
  font-weight: 700;
  color: #111;
  letter-spacing: 0.01em;
}
.apple-subtitle {
  color: #888;
  font-size: 1.08rem;
  margin-bottom: 0.5rem;
}
.form-row {
  display: flex;
  align-items: center;
  gap: 18px;
  margin-bottom: 1.2rem;
}
.form-label {
  min-width: 80px;
  text-align: right;
  font-weight: 500;
  color: #222;
  font-size: 1rem;
  letter-spacing: 0.01em;
}
.apple-input {
  flex: 1 1 0%;
  background: #f6f7fa;
  border: none;
  border-radius: 16px;
  padding: 13px 16px;
  font-size: 1.08rem;
  color: #222;
  outline: none;
  box-shadow: 0 1px 4px 0 rgba(60,60,60,0.04);
  transition: box-shadow 0.18s, background 0.18s;
}
.apple-input:focus {
  background: #fff;
  box-shadow: 0 0 0 2px #007aff33;
}
.apple-input::placeholder {
  color: #b0b3b8;
  font-weight: 400;
}
.is-invalid {
  box-shadow: 0 0 0 2px #ff3b30 !important;
  background: #fff0f0 !important;
}
.apple-error {
  color: #ff3b30;
  font-size: 14px;
  margin: -0.8rem 0 0.7rem 98px;
  min-height: 18px;
}
.apple-password-row {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
}
.apple-eye-btn {
  border: none;
  background: transparent;
  color: #b0b3b8;
  font-size: 1.2rem;
  padding: 0 6px;
  border-radius: 50%;
  transition: background 0.18s;
  cursor: pointer;
}
.apple-eye-btn:hover {
  background: #f1f2f6;
  color: #007aff;
}
.apple-btn {
  width: 100%;
  background: #111;
  color: #fff;
  border: none;
  border-radius: 18px;
  font-size: 1.13rem;
  font-weight: 700;
  padding: 14px 0;
  margin-top: 18px;
  margin-bottom: 8px;
  box-shadow: 0 2px 8px 0 rgba(60,60,60,0.07);
  transition: background 0.18s, box-shadow 0.18s;
  cursor: pointer;
}
.apple-btn:disabled {
  background: #d1d5db;
  color: #fff;
  cursor: not-allowed;
}
.apple-btn:not(:disabled):hover {
  background: #222;
}
.apple-bottom-link {
  text-align: center;
  margin-top: 8px;
  margin-bottom: 8px;
}
.apple-bottom-link a {
  color: #007aff;
  font-weight: 500;
  text-decoration: none;
  transition: color 0.18s;
}
.apple-bottom-link a:hover {
  color: #0051a8;
  text-decoration: underline;
}
.dev-quick-login .apple-code-btn {
  font-size: 1rem;
  padding: 10px 0;
}
@media (max-width: 700px) {
  .register-apple-card {
    max-width: 98vw;
    padding: 24px 8vw 18px 8vw;
  }
  .form-row {
    flex-direction: column;
    align-items: stretch;
    gap: 4px;
  }
  .form-label {
    text-align: left;
    min-width: 0;
    margin-bottom: 2px;
  }
  .apple-error {
    margin-left: 0;
  }
}
</style>

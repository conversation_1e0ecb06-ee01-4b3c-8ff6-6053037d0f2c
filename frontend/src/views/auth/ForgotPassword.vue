<template>
  <div class="forgot-password">
    <div class="container">
      <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
          <div class="card shadow">
            <div class="card-body p-4">
              <!-- 标题 -->
              <div class="text-center mb-4">
                <h3 class="card-title">
                  <i class="bi bi-key me-2 text-primary"></i>
                  重置密码
                </h3>
                <p class="text-muted">
                  {{ currentStep === 1 ? '请输入您的邮箱地址' : '请输入验证码和新密码' }}
                </p>
              </div>

              <!-- 步骤指示器 -->
              <div class="step-indicator mb-4">
                <div class="d-flex justify-content-between">
                  <div class="step" :class="{ active: currentStep >= 1, completed: currentStep > 1 }">
                    <div class="step-number">1</div>
                    <div class="step-label">验证邮箱</div>
                  </div>
                  <div class="step-line" :class="{ active: currentStep > 1 }"></div>
                  <div class="step" :class="{ active: currentStep >= 2 }">
                    <div class="step-number">2</div>
                    <div class="step-label">重置密码</div>
                  </div>
                </div>
              </div>

              <!-- 步骤1：验证邮箱 -->
              <div v-if="currentStep === 1" class="step-content">
                <form @submit.prevent="sendResetCode">
                  <div class="mb-3">
                    <label class="form-label">邮箱地址</label>
                    <input
                      v-model="email"
                      type="email"
                      class="form-control"
                      :class="{ 'is-invalid': errors.email }"
                      placeholder="请输入您的邮箱地址"
                      required
                      :disabled="loading"
                    />
                    <div v-if="errors.email" class="invalid-feedback">
                      {{ errors.email }}
                    </div>
                  </div>

                  <div class="d-grid gap-2">
                    <button
                      type="submit"
                      class="btn btn-primary"
                      :disabled="loading || !email"
                    >
                      <span v-if="loading" class="spinner-border spinner-border-sm me-2"></span>
                      发送重置邮件
                    </button>
                    <router-link to="/login" class="btn btn-outline-secondary">
                      返回登录
                    </router-link>
                  </div>
                </form>
              </div>

              <!-- 步骤2：重置密码 -->
              <div v-if="currentStep === 2" class="step-content">
                <form @submit.prevent="resetPassword">
                  <div class="mb-3">
                    <label class="form-label">邮箱地址</label>
                    <input
                      v-model="email"
                      type="email"
                      class="form-control"
                      disabled
                    />
                  </div>

                  <div class="mb-3">
                    <label class="form-label">验证码</label>
                    <div class="input-group">
                      <input
                        v-model="verificationCode"
                        type="text"
                        class="form-control"
                        :class="{ 'is-invalid': errors.verificationCode }"
                        placeholder="请输入6位验证码"
                        maxlength="6"
                        required
                        :disabled="loading"
                      />
                      <button
                        class="btn btn-outline-primary"
                        type="button"
                        :disabled="!canResendCode || resending"
                        @click="resendCode"
                      >
                        <span v-if="resending" class="spinner-border spinner-border-sm me-1"></span>
                        {{ resendButtonText }}
                      </button>
                    </div>
                    <div v-if="errors.verificationCode" class="invalid-feedback d-block">
                      {{ errors.verificationCode }}
                    </div>
                  </div>

                  <div class="mb-3">
                    <label class="form-label">新密码</label>
                    <div class="input-group">
                      <input
                        v-model="newPassword"
                        :type="showPassword ? 'text' : 'password'"
                        class="form-control"
                        :class="{ 'is-invalid': errors.newPassword }"
                        placeholder="请输入新密码"
                        required
                        :disabled="loading"
                      />
                      <button
                        class="btn btn-outline-secondary"
                        type="button"
                        @click="showPassword = !showPassword"
                      >
                        <i :class="showPassword ? 'bi bi-eye-slash' : 'bi bi-eye'"></i>
                      </button>
                    </div>
                    <div v-if="errors.newPassword" class="invalid-feedback d-block">
                      {{ errors.newPassword }}
                    </div>
                    <div class="form-text">
                      密码长度至少6位，建议包含字母、数字和特殊字符
                    </div>
                  </div>

                  <div class="mb-3">
                    <label class="form-label">确认新密码</label>
                    <input
                      v-model="confirmPassword"
                      :type="showPassword ? 'text' : 'password'"
                      class="form-control"
                      :class="{ 'is-invalid': errors.confirmPassword }"
                      placeholder="请再次输入新密码"
                      required
                      :disabled="loading"
                    />
                    <div v-if="errors.confirmPassword" class="invalid-feedback">
                      {{ errors.confirmPassword }}
                    </div>
                  </div>

                  <div class="d-grid gap-2">
                    <button
                      type="submit"
                      class="btn btn-primary"
                      :disabled="loading || !isFormValid"
                    >
                      <span v-if="loading" class="spinner-border spinner-border-sm me-2"></span>
                      重置密码
                    </button>
                    <button
                      type="button"
                      class="btn btn-outline-secondary"
                      @click="goBackToStep1"
                    >
                      重新发送邮件
                    </button>
                  </div>
                </form>
              </div>

              <!-- 成功提示 -->
              <div v-if="currentStep === 3" class="step-content text-center">
                <div class="success-icon mb-3">
                  <i class="bi bi-check-circle-fill text-success" style="font-size: 3rem;"></i>
                </div>
                <h4 class="text-success mb-3">密码重置成功！</h4>
                <p class="text-muted mb-4">
                  您的密码已成功重置，请使用新密码登录。
                </p>
                <div class="d-grid">
                  <router-link to="/login" class="btn btn-primary">
                    立即登录
                  </router-link>
                </div>
              </div>
            </div>
          </div>

          <!-- 帮助信息 -->
          <div class="text-center mt-3">
            <small class="text-muted">
              没有收到邮件？请检查垃圾邮件文件夹，或
              <a href="#" @click.prevent="contactSupport" class="text-decoration-none">联系客服</a>
            </small>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { showSuccess, showError } from '@/utils/message'

// 定义组件名称
defineOptions({
  name: 'ForgotPassword'
})

const store = useStore()
const router = useRouter()

// 响应式数据
const currentStep = ref<number>(1)
const email = ref<string>('')
const verificationCode = ref<string>('')
const newPassword = ref<string>('')
const confirmPassword = ref<string>('')
const showPassword = ref<boolean>(false)
const loading = ref<boolean>(false)
const resending = ref<boolean>(false)
const countdown = ref<number>(0)

// 错误信息
const errors = reactive<Record<string, string>>({})

// 计算属性
const canResendCode = computed((): boolean => {
  return countdown.value === 0 && !resending.value
})

const resendButtonText = computed((): string => {
  if (resending.value) return '发送中...'
  if (countdown.value > 0) return `${countdown.value}s后重发`
  return '重新发送'
})

const isFormValid = computed((): boolean => {
  return verificationCode.value.length === 6 &&
         newPassword.value.length >= 6 &&
         confirmPassword.value === newPassword.value
})

// 方法
const validateEmail = (): boolean => {
  errors.email = ''
  if (!email.value) {
    errors.email = '请输入邮箱地址'
    return false
  }
  if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email.value)) {
    errors.email = '邮箱格式不正确'
    return false
  }
  return true
}

const validateForm = (): boolean => {
  Object.keys(errors).forEach(key => delete errors[key])

  if (!verificationCode.value) {
    errors.verificationCode = '请输入验证码'
  } else if (verificationCode.value.length !== 6) {
    errors.verificationCode = '验证码必须是6位数字'
  }

  if (!newPassword.value) {
    errors.newPassword = '请输入新密码'
  } else if (newPassword.value.length < 6) {
    errors.newPassword = '密码长度至少6位'
  }

  if (!confirmPassword.value) {
    errors.confirmPassword = '请确认新密码'
  } else if (newPassword.value !== confirmPassword.value) {
    errors.confirmPassword = '两次输入的密码不一致'
  }

  return Object.keys(errors).length === 0
}

const sendResetCode = async (): Promise<void> => {
  if (!validateEmail()) return

  loading.value = true
  try {
    await store.dispatch('auth/sendEmailCode', {
      email: email.value,
      type: 'reset'
    })

    showSuccess('重置邮件已发送，请查收')
    currentStep.value = 2
    startCountdown()
  } catch (error) {
    errors.email = (error as Error).message || '发送失败'
    showError('发送重置邮件失败')
  } finally {
    loading.value = false
  }
}

const resendCode = async (): Promise<void> => {
  if (!canResendCode.value) return

  resending.value = true
  try {
    await store.dispatch('auth/sendEmailCode', {
      email: email.value,
      type: 'reset'
    })

    showSuccess('验证码已重新发送')
    startCountdown()
  } catch (error) {
    showError('重新发送失败')
  } finally {
    resending.value = false
  }
}

const resetPassword = async (): Promise<void> => {
  if (!validateForm()) return

  loading.value = true
  try {
    await store.dispatch('auth/resetPassword', {
      email: email.value,
      code: verificationCode.value,
      newPassword: newPassword.value
    })

    showSuccess('密码重置成功')
    currentStep.value = 3
  } catch (error) {
    errors.verificationCode = (error as Error).message || '重置失败'
    showError('密码重置失败')
  } finally {
    loading.value = false
  }
}

const startCountdown = (): void => {
  countdown.value = 60
  const timer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(timer)
    }
  }, 1000)
}

const goBackToStep1 = (): void => {
  currentStep.value = 1
  verificationCode.value = ''
  newPassword.value = ''
  confirmPassword.value = ''
  Object.keys(errors).forEach(key => delete errors[key])
}

const contactSupport = (): void => {
  // 可以打开客服对话框或跳转到帮助页面
  showSuccess('客服功能开发中，请稍后再试')
}
</script>

<style scoped>
.forgot-password {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  padding: 2rem 0;
}

.card {
  border: none;
  border-radius: 1rem;
}

.step-indicator {
  position: relative;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
}

.step-number {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background: #e9ecef;
  color: #6c757d;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-bottom: 0.5rem;
  transition: all 0.3s ease;
}

.step.active .step-number {
  background: #007bff;
  color: white;
}

.step.completed .step-number {
  background: #28a745;
  color: white;
}

.step-label {
  font-size: 0.875rem;
  color: #6c757d;
  font-weight: 500;
}

.step.active .step-label {
  color: #007bff;
}

.step.completed .step-label {
  color: #28a745;
}

.step-line {
  position: absolute;
  top: 1.25rem;
  left: 50%;
  right: 50%;
  height: 2px;
  background: #e9ecef;
  transform: translateY(-50%);
  z-index: 1;
  transition: all 0.3s ease;
}

.step-line.active {
  background: #28a745;
}

.step-content {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.success-icon {
  animation: bounceIn 0.6s ease-out;
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.input-group .btn {
  border-left: none;
}

.form-control:focus + .btn {
  border-color: #86b7fe;
}
</style>

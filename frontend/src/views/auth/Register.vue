<template>
  <div class="register-apple-bg">
    <div class="register-apple-card">
      <div class="text-center mb-4">
        <img src="/logo.svg" alt="logo" class="register-logo mb-3" />
        <h2 class="apple-title mb-1">创建您的 Code-Combined 账号</h2>
        <p class="apple-subtitle">体验极致简洁的注册流程</p>
      </div>
      <form @submit.prevent="handleRegister">
        <div class="form-row">
          <label for="username" class="form-label">用户名</label>
          <input
            id="username"
            v-model="form.username"
            type="text"
            class="apple-input"
            :class="{ 'is-invalid': errors.username }"
            placeholder="用户名"
            required
            @blur="validateField('username')"
            autocomplete="username"
          >
        </div>
        <div v-if="errors.username" class="apple-error">{{ errors.username }}</div>
        <div class="form-row">
          <label for="email" class="form-label">邮箱</label>
          <input
            id="email"
            v-model="form.email"
            type="email"
            class="apple-input"
            :class="{ 'is-invalid': errors.email }"
            placeholder="邮箱"
            required
            @blur="validateField('email')"
            autocomplete="email"
          >
        </div>
        <div v-if="errors.email" class="apple-error">{{ errors.email }}</div>
        <div class="form-row">
          <label for="emailCode" class="form-label">验证码</label>
          <div class="apple-code-row">
            <input
              id="emailCode"
              v-model="form.emailCode"
              type="text"
              class="apple-input"
              :class="{ 'is-invalid': errors.emailCode }"
              placeholder="验证码"
              required
              @blur="validateField('emailCode')"
              autocomplete="one-time-code"
            >
            <button
              type="button"
              class="apple-code-btn"
              :disabled="!canSendCode || sendingCode"
              @click="sendEmailCode"
            >
              <span v-if="sendingCode" class="spinner-border spinner-border-sm me-1"></span>
              {{ codeButtonText }}
            </button>
          </div>
        </div>
        <div v-if="errors.emailCode" class="apple-error">{{ errors.emailCode }}</div>
        <div class="form-row">
          <label for="password" class="form-label">密码</label>
          <div class="apple-password-row">
            <input
              id="password"
              v-model="form.password"
              :type="showPassword ? 'text' : 'password'"
              class="apple-input"
              :class="{ 'is-invalid': errors.password }"
              placeholder="密码"
              required
              @blur="validateField('password')"
              autocomplete="new-password"
            >
            <button type="button" class="apple-eye-btn" @click="showPassword = !showPassword">
              <i :class="showPassword ? 'bi bi-eye-slash' : 'bi bi-eye'" />
            </button>
          </div>
        </div>
        <div v-if="errors.password" class="apple-error">{{ errors.password }}</div>
        <div class="form-row">
          <label for="confirmPassword" class="form-label">确认</label>
          <div class="apple-password-row">
            <input
              id="confirmPassword"
              v-model="form.confirmPassword"
              :type="showConfirmPassword ? 'text' : 'password'"
              class="apple-input"
              :class="{ 'is-invalid': errors.confirmPassword }"
              placeholder="确认密码"
              required
              @blur="validateField('confirmPassword')"
              autocomplete="new-password"
            >
            <button type="button" class="apple-eye-btn" @click="showConfirmPassword = !showConfirmPassword">
              <i :class="showConfirmPassword ? 'bi bi-eye-slash' : 'bi bi-eye'" />
            </button>
          </div>
        </div>
        <div v-if="errors.confirmPassword" class="apple-error">{{ errors.confirmPassword }}</div>
        <div class="form-row">
          <label for="nickname" class="form-label">昵称</label>
          <input
            id="nickname"
            v-model="form.nickname"
            type="text"
            class="apple-input"
            placeholder="昵称（可选）"
          >
        </div>
        <button type="submit" class="apple-btn" :disabled="loading">
          <span v-if="loading" class="spinner-border spinner-border-sm me-2"></span>
          创建账号
        </button>
        <div class="apple-bottom-link">
          <router-link to="/login">已有账号？登录</router-link>
        </div>
        <div class="apple-tip">Mock环境验证码固定为：123456</div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, computed } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { showSuccess, showError } from '@/utils/message'

const store = useStore()
const router = useRouter()

const form = reactive({
  username: '',
  email: '',
  emailCode: '',
  password: '',
  confirmPassword: '',
  nickname: ''
})
const errors = reactive<Record<string, string>>({})
const showPassword = ref(false)
const showConfirmPassword = ref(false)
const sendingCode = ref(false)
const countdown = ref(0)
const loading = ref(false)

const isEmailValid = computed(() => {
  return form.email && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email)
})
const canSendCode = computed(() => {
  return isEmailValid.value && countdown.value === 0 && !sendingCode.value
})
const codeButtonText = computed(() => {
  if (sendingCode.value) return '发送中...'
  if (countdown.value > 0) return `${countdown.value}s后重发`
  return '发送验证码'
})

const validateField = (field: keyof typeof form): boolean => {
  errors[field] = ''
  if (field === 'username') {
    if (!form.username) errors.username = '请输入用户名'
    else if (form.username.length < 2) errors.username = '用户名至少2位'
  }
  if (field === 'email') {
    if (!form.email) errors.email = '请输入邮箱地址'
    else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email)) errors.email = '邮箱格式不正确'
  }
  if (field === 'emailCode') {
    if (!form.emailCode) errors.emailCode = '请输入验证码'
    else if (form.emailCode !== '123456') errors.emailCode = '验证码错误（mock环境为123456）'
  }
  if (field === 'password') {
    if (!form.password) errors.password = '请输入密码'
    else if (form.password.length < 6) errors.password = '密码至少6位'
  }
  if (field === 'confirmPassword') {
    if (!form.confirmPassword) errors.confirmPassword = '请确认密码'
    else if (form.confirmPassword !== form.password) errors.confirmPassword = '两次输入的密码不一致'
  }
  return !errors[field]
}

const validateForm = (): boolean => {
  let valid = true
  Object.keys(form).forEach(key => {
    if (!validateField(key as keyof typeof form)) valid = false
  })
  return valid
}

const sendEmailCode = async () => {
  if (!isEmailValid.value) return
  sendingCode.value = true
  setTimeout(() => {
    sendingCode.value = false
    countdown.value = 60
    const timer = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) clearInterval(timer)
    }, 1000)
  }, 1000)
}

const handleRegister = async () => {
  if (!validateForm()) return
  loading.value = true
  try {
    await store.dispatch('auth/register', form)
    showSuccess('注册成功，请登录！')
    router.push('/login')
  } catch (error: any) {
    showError(error.message || '注册失败，请检查信息')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.register-apple-bg {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8fafc 0%, #f1f3f6 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}
.register-apple-card {
  width: 66vw;
  max-width: 520px;
  min-width: 320px;
  background: #fff;
  border-radius: 28px;
  box-shadow: 0 8px 32px 0 rgba(60,60,60,0.07);
  padding: 44px 38px 32px 38px;
  margin: 48px 0;
  display: flex;
  flex-direction: column;
  align-items: stretch;
}
.register-logo {
  width: 54px;
  height: 54px;
  margin-bottom: 12px;
}
.apple-title {
  font-size: 2rem;
  font-weight: 700;
  color: #111;
  letter-spacing: 0.01em;
}
.apple-subtitle {
  color: #888;
  font-size: 1.08rem;
  margin-bottom: 0.5rem;
}
.form-row {
  display: flex;
  align-items: center;
  gap: 18px;
  margin-bottom: 1.2rem;
}
.form-label {
  min-width: 80px;
  text-align: right;
  font-weight: 500;
  color: #222;
  font-size: 1rem;
  letter-spacing: 0.01em;
}
.apple-input {
  flex: 1 1 0%;
  background: #f6f7fa;
  border: none;
  border-radius: 16px;
  padding: 13px 16px;
  font-size: 1.08rem;
  color: #222;
  outline: none;
  box-shadow: 0 1px 4px 0 rgba(60,60,60,0.04);
  transition: box-shadow 0.18s, background 0.18s;
}
.apple-input:focus {
  background: #fff;
  box-shadow: 0 0 0 2px #007aff33;
}
.apple-input::placeholder {
  color: #b0b3b8;
  font-weight: 400;
}
.is-invalid {
  box-shadow: 0 0 0 2px #ff3b30 !important;
  background: #fff0f0 !important;
}
.apple-error {
  color: #ff3b30;
  font-size: 14px;
  margin: -0.8rem 0 0.7rem 98px;
  min-height: 18px;
}
.apple-code-row {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
}
.apple-code-btn {
  border: none;
  background: #f1f2f6;
  color: #007aff;
  font-weight: 600;
  border-radius: 16px;
  padding: 10px 18px;
  font-size: 1rem;
  transition: background 0.18s, color 0.18s;
  cursor: pointer;
}
.apple-code-btn:disabled {
  color: #b0b3b8;
  background: #f6f7fa;
  cursor: not-allowed;
}
.apple-code-btn:not(:disabled):hover {
  background: #e5e9f2;
  color: #0051a8;
}
.apple-password-row {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
}
.apple-eye-btn {
  border: none;
  background: transparent;
  color: #b0b3b8;
  font-size: 1.2rem;
  padding: 0 6px;
  border-radius: 50%;
  transition: background 0.18s;
  cursor: pointer;
}
.apple-eye-btn:hover {
  background: #f1f2f6;
  color: #007aff;
}
.apple-btn {
  width: 100%;
  background: #111;
  color: #fff;
  border: none;
  border-radius: 18px;
  font-size: 1.13rem;
  font-weight: 700;
  padding: 14px 0;
  margin-top: 18px;
  margin-bottom: 8px;
  box-shadow: 0 2px 8px 0 rgba(60,60,60,0.07);
  transition: background 0.18s, box-shadow 0.18s;
  cursor: pointer;
}
.apple-btn:disabled {
  background: #d1d5db;
  color: #fff;
  cursor: not-allowed;
}
.apple-btn:not(:disabled):hover {
  background: #222;
}
.apple-bottom-link {
  text-align: center;
  margin-top: 8px;
  margin-bottom: 8px;
}
.apple-bottom-link a {
  color: #007aff;
  font-weight: 500;
  text-decoration: none;
  transition: color 0.18s;
}
.apple-bottom-link a:hover {
  color: #0051a8;
  text-decoration: underline;
}
.apple-tip {
  text-align: right;
  color: #b0b3b8;
  font-size: 13px;
  margin-top: 8px;
}
@media (max-width: 700px) {
  .register-apple-card {
    max-width: 98vw;
    padding: 24px 8vw 18px 8vw;
  }
  .form-row {
    flex-direction: column;
    align-items: stretch;
    gap: 4px;
  }
  .form-label {
    text-align: left;
    min-width: 0;
    margin-bottom: 2px;
  }
  .apple-error {
    margin-left: 0;
  }
}
</style>

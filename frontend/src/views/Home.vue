<template>
  <div class="home-apple-container">
    <header class="home-apple-header">
      <div class="home-logo-row">
        <img src="/logo.svg" alt="logo" class="home-logo" />
        <span class="home-logo-title">Code-Combined</span>
      </div>
      <h1 class="home-apple-title">欢迎来到 Code-Combined</h1>
      <p class="home-apple-subtitle">一个专注于整理、收藏和管理各类题集的开放平台。</p>
    </header>

    <div v-if="loading" class="home-apple-loading">
      <div class="spinner-border" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
    </div>

    <div v-else class="home-apple-content">
      <main class="home-apple-main">
        <div class="home-apple-card">
          <div class="home-apple-card-header">
            <span>精选题集</span>
            <router-link to="/problemsets" class="home-apple-link">查看全部</router-link>
          </div>
          <ul class="home-apple-list">
            <li v-for="set in problemSets.slice(0, 5)" :key="set.id" class="home-apple-list-item" @click="goToProblemSet(set.id)">
              <div class="home-apple-list-row">
                <i class="bi bi-collection-fill home-apple-list-icon"></i>
                <div class="home-apple-list-info">
                  <h6 class="home-apple-list-title">{{ set.title }}</h6>
                  <p class="home-apple-list-desc">{{ set.description }}</p>
                </div>
                <span class="home-apple-badge">{{ set.problemCount }} 题</span>
              </div>
            </li>
          </ul>
        </div>
      </main>

      <aside class="home-apple-sidebar">
        <div class="home-apple-card">
          <div class="home-apple-card-header">平台导览</div>
          <div class="home-apple-card-body">
            <h6 class="home-apple-card-title">整理你的题集</h6>
            <p class="home-apple-card-text">创建、收藏、管理你喜欢的题集，系统化梳理各类题目资源。</p>
            <router-link to="/problemsets" class="home-apple-btn home-apple-btn-primary">探索题集</router-link>
            <router-link to="/problemsets" class="home-apple-btn home-apple-btn-secondary">管理我的题集</router-link>
          </div>
        </div>
      </aside>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import type { ProblemSet } from '@/types';

const store = useStore();
const router = useRouter();

const loading = ref(true);

const problemSets = computed<ProblemSet[]>(() => store.getters['problemset/allProblemSets']);

onMounted(async () => {
  loading.value = true;
  try {
    await store.dispatch('problemset/fetchProblemSets');
  } catch (error) {
    console.error('Failed to fetch problem sets:', error);
  } finally {
    loading.value = false;
  }
});

const goToProblemSet = (id: number) => {
  router.push(`/problemsets/${id}`);
};
</script>

<style scoped>
.home-apple-container {
  max-width: 1100px;
  margin: 0 auto;
  padding: 36px 18px 24px 18px;
}
.home-apple-header {
  text-align: center;
  margin-bottom: 38px;
  padding-bottom: 10px;
}
.home-apple-title {
  font-size: 2.4rem;
  font-weight: 800;
  color: #111;
  letter-spacing: 0.01em;
  margin-bottom: 0.5rem;
}
.home-apple-subtitle {
  font-size: 1.18rem;
  color: #888;
  margin-bottom: 0.2rem;
}
.home-apple-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 220px;
}
.home-apple-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2.2rem;
}
@media (min-width: 768px) {
  .home-apple-content {
    grid-template-columns: 2.2fr 1fr;
  }
}
.home-apple-main {
  min-width: 0;
}
.home-apple-card {
  background: #fff;
  border-radius: 28px;
  box-shadow: 0 4px 24px 0 rgba(60,60,60,0.07);
  padding: 0 0 0 0;
  margin-bottom: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.home-apple-card-header {
  font-size: 1.13rem;
  font-weight: 700;
  color: #111;
  padding: 22px 28px 12px 28px;
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #f1f2f6;
}
.home-apple-link {
  color: #007aff;
  font-weight: 500;
  text-decoration: none;
  font-size: 1rem;
  transition: color 0.18s;
}
.home-apple-link:hover {
  color: #0051a8;
  text-decoration: underline;
}
.home-apple-list {
  list-style: none;
  margin: 0;
  padding: 0;
}
.home-apple-list-item {
  cursor: pointer;
  transition: background 0.16s;
  padding: 0;
}
.home-apple-list-item:hover {
  background: #f6f7fa;
}
.home-apple-list-row {
  display: flex;
  align-items: center;
  gap: 18px;
  padding: 18px 28px 18px 28px;
}
.home-apple-list-icon {
  color: #007aff;
  font-size: 1.5rem;
  flex-shrink: 0;
}
.home-apple-list-info {
  flex: 1 1 0%;
  min-width: 0;
}
.home-apple-list-title {
  font-size: 1.08rem;
  font-weight: 600;
  color: #111;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.home-apple-list-desc {
  color: #888;
  font-size: 0.98rem;
  margin-bottom: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.home-apple-badge {
  background: #f1f2f6;
  color: #007aff;
  border-radius: 14px;
  font-size: 0.98rem;
  font-weight: 600;
  padding: 5px 16px;
  min-width: 48px;
  text-align: center;
  border: none;
}
.home-apple-sidebar {
  min-width: 0;
}
.home-apple-card-body {
  padding: 22px 28px 24px 28px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.home-apple-card-title {
  font-size: 1.08rem;
  font-weight: 700;
  color: #111;
  margin-bottom: 2px;
}
.home-apple-card-text {
  color: #888;
  font-size: 0.98rem;
  margin-bottom: 8px;
}
.home-apple-btn {
  display: block;
  width: 100%;
  border: none;
  border-radius: 18px;
  font-size: 1.08rem;
  font-weight: 700;
  padding: 13px 0;
  margin-bottom: 8px;
  box-shadow: 0 1px 4px 0 rgba(60,60,60,0.04);
  transition: background 0.18s, color 0.18s;
  text-align: center;
  cursor: pointer;
  text-decoration: none;
}
.home-apple-btn-primary {
  background: #007aff;
  color: #fff;
}
.home-apple-btn-primary:hover {
  background: #0051a8;
}
.home-apple-btn-secondary {
  background: #f1f2f6;
  color: #007aff;
}
.home-apple-btn-secondary:hover {
  background: #e5e9f2;
  color: #0051a8;
}
@media (max-width: 700px) {
  .home-apple-container {
    padding: 18px 2vw 12px 2vw;
  }
  .home-apple-header {
    margin-bottom: 22px;
  }
  .home-apple-content {
    gap: 1.2rem;
  }
  .home-apple-card-header, .home-apple-card-body, .home-apple-list-row {
    padding-left: 12px;
    padding-right: 12px;
  }
}
.home-logo-row {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-bottom: 10px;
}
.home-logo {
  width: 54px;
  height: 54px;
  border-radius: 14px;
}
.home-logo-title {
  font-size: 2rem;
  font-weight: 700;
  color: #111;
  letter-spacing: 0.01em;
}
</style>

<template>
  <div class="dashboard">
    <div class="container py-4">
      <!-- 欢迎信息 -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="card border-0 bg-primary text-white">
            <div class="card-body">
              <div class="row align-items-center">
                <div class="col-md-8">
                  <h3 class="card-title mb-2">
                    <i class="bi bi-speedometer2 me-2"></i>
                    欢迎回来，{{ user?.nickname || user?.username }}！
                  </h3>
                  <p class="card-text mb-0">
                    今天是 {{ currentDate }}，开始你的算法学习之旅吧！
                  </p>
                </div>
                <div class="col-md-4 text-md-end">
                  <div class="d-flex gap-2 justify-content-md-end flex-wrap">
                    <!-- 管理员专用入口 -->
                    <router-link
                      v-if="user?.role === 'admin'"
                      to="/admin"
                      class="btn btn-warning"
                    >
                      <i class="bi bi-shield-check me-1"></i>
                      管理控制台
                    </router-link>
                    <router-link to="/problemsets/create" class="btn btn-light">
                      <i class="bi bi-plus-circle me-1"></i>
                      新建题集
                    </router-link>
                    <router-link to="/problems/create" class="btn btn-outline-light">
                      <i class="bi bi-plus-circle me-1"></i>
                      新建题目
                    </router-link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 管理员专用提示卡片 -->
      <div v-if="user?.role === 'admin'" class="row mb-4">
        <div class="col-12">
          <div class="card border-danger shadow-sm">
            <div class="card-body">
              <div class="d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                  <div class="me-3">
                    <div class="bg-danger text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                      <i class="bi bi-shield-check fs-4"></i>
                    </div>
                  </div>
                  <div>
                    <h5 class="mb-1 text-danger">系统管理员权限</h5>
                    <p class="mb-0 text-muted">您拥有系统管理员权限，可以管理用户、题目、题集等系统资源</p>
                  </div>
                </div>
                <div>
                  <router-link to="/admin" class="btn btn-danger">
                    <i class="bi bi-gear me-1"></i>
                    进入管理控制台
                  </router-link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 统计卡片 -->
      <div class="row g-4 mb-4">
        <div class="col-md-3">
          <div class="card border-0 shadow-sm h-100">
            <div class="card-body text-center">
              <div class="stat-icon bg-primary text-white rounded-circle mx-auto mb-3">
                <i class="bi bi-people fs-3"></i>
              </div>
              <h4 class="fw-bold text-primary">{{ stats.onlineUsers || 0 }}</h4>
              <p class="text-muted mb-0">在线用户</p>
            </div>
          </div>
        </div>
        
        <div class="col-md-3">
          <div class="card border-0 shadow-sm h-100">
            <div class="card-body text-center">
              <div class="stat-icon bg-success text-white rounded-circle mx-auto mb-3">
                <i class="bi bi-person-check fs-3"></i>
              </div>
              <h4 class="fw-bold text-success">{{ stats.totalUsers || 0 }}</h4>
              <p class="text-muted mb-0">总用户数</p>
            </div>
          </div>
        </div>
        
        <div class="col-md-3">
          <div class="card border-0 shadow-sm h-100">
            <div class="card-body text-center">
              <div class="stat-icon bg-info text-white rounded-circle mx-auto mb-3">
                <i class="bi bi-collection fs-3"></i>
              </div>
              <h4 class="fw-bold text-info">{{ stats.totalProblemSets || 0 }}</h4>
              <p class="text-muted mb-0">题集总数</p>
            </div>
          </div>
        </div>
        
        <div class="col-md-3">
          <div class="card border-0 shadow-sm h-100">
            <div class="card-body text-center">
              <div class="stat-icon bg-warning text-white rounded-circle mx-auto mb-3">
                <i class="bi bi-puzzle fs-3"></i>
              </div>
              <h4 class="fw-bold text-warning">{{ stats.totalProblems || 0 }}</h4>
              <p class="text-muted mb-0">题目总数</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 快速操作 -->
      <div class="row g-4 mb-4">
        <div class="col-md-6">
          <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-transparent border-0">
              <h5 class="card-title mb-0">
                <i class="bi bi-lightning me-2"></i>
                快速操作
              </h5>
            </div>
            <div class="card-body">
              <div class="row g-3">
                <!-- 管理员专用管理入口 -->
                <div v-if="user?.role === 'admin'" class="col-12">
                  <router-link to="/admin" class="btn btn-danger w-100 mb-3">
                    <i class="bi bi-shield-check d-block fs-4 mb-2"></i>
                    <strong>系统管理控制台</strong>
                    <small class="d-block">管理用户、题目、题集等系统资源</small>
                  </router-link>
                </div>

                <div class="col-6">
                  <router-link to="/problemsets" class="btn btn-outline-primary w-100">
                    <i class="bi bi-collection d-block fs-4 mb-2"></i>
                    浏览题集
                  </router-link>
                </div>
                <div class="col-6">
                  <router-link to="/problems" class="btn btn-outline-success w-100">
                    <i class="bi bi-puzzle d-block fs-4 mb-2"></i>
                    浏览题目
                  </router-link>
                </div>
                <div class="col-6">
                  <router-link to="/problemsets/create" class="btn btn-outline-info w-100">
                    <i class="bi bi-plus-circle d-block fs-4 mb-2"></i>
                    创建题集
                  </router-link>
                </div>
                <div class="col-6">
                  <router-link to="/problems/create" class="btn btn-outline-warning w-100">
                    <i class="bi bi-plus-square d-block fs-4 mb-2"></i>
                    创建题目
                  </router-link>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="col-md-6">
          <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-transparent border-0">
              <h5 class="card-title mb-0">
                <i class="bi bi-person-circle me-2"></i>
                个人信息
              </h5>
            </div>
            <div class="card-body">
              <div class="d-flex align-items-center mb-3">
                <div class="avatar-placeholder bg-primary text-white rounded-circle me-3">
                  <i class="bi bi-person fs-4"></i>
                </div>
                <div>
                  <h6 class="mb-1">{{ user?.nickname || user?.username }}</h6>
                  <small class="text-muted">{{ user?.email }}</small>
                </div>
              </div>
              <div class="row g-2">
                <div class="col-12">
                  <div class="d-flex justify-content-between">
                    <span class="text-muted">角色：</span>
                    <span
                      class="badge"
                      :class="user?.role === 'admin' ? 'bg-danger' : 'bg-primary'"
                    >
                      {{ user?.role === 'admin' ? '系统管理员' : '普通用户' }}
                    </span>
                  </div>
                </div>
                <div class="col-12">
                  <div class="d-flex justify-content-between">
                    <span class="text-muted">注册时间：</span>
                    <span>{{ formatDate(user?.createdTime) }}</span>
                  </div>
                </div>
              </div>
              <div class="mt-3">
                <div class="d-flex gap-2 flex-wrap">
                  <router-link to="/profile" class="btn btn-outline-primary btn-sm">
                    <i class="bi bi-gear me-1"></i>
                    编辑资料
                  </router-link>
                  <!-- 管理员专用按钮 -->
                  <router-link
                    v-if="user?.role === 'admin'"
                    to="/admin"
                    class="btn btn-danger btn-sm"
                  >
                    <i class="bi bi-shield-check me-1"></i>
                    管理控制台
                  </router-link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 最近活动 -->
      <div class="row">
        <div class="col-12">
          <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0">
              <h5 class="card-title mb-0">
                <i class="bi bi-clock-history me-2"></i>
                最近活动
              </h5>
            </div>
            <div class="card-body">
              <div class="text-center py-4">
                <i class="bi bi-inbox text-muted fs-1"></i>
                <p class="text-muted mt-2">暂无最近活动</p>
                <small class="text-muted">开始创建题集或题目来记录你的活动吧！</small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed, onMounted, ref } from 'vue'
import { useStore } from 'vuex'

export default {
  name: 'Dashboard',
  setup() {
    const store = useStore()
    
    const user = computed(() => store.getters['auth/user'])
    const stats = computed(() => store.state.stats)
    const currentDate = ref('')

    const formatDate = (dateString) => {
      if (!dateString) return '-'
      return new Date(dateString).toLocaleDateString('zh-CN')
    }

    const updateCurrentDate = () => {
      const now = new Date()
      currentDate.value = now.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long'
      })
    }

    onMounted(async () => {
      updateCurrentDate()
      
      // 获取用户信息
      if (!user.value) {
        try {
          await store.dispatch('auth/getUserInfo')
        } catch (error) {
          console.error('获取用户信息失败:', error)
        }
      }
      
      // 获取统计数据
      store.dispatch('stats/fetchUserStats')
      store.dispatch('stats/fetchPlatformStats')
    })

    return {
      user,
      stats,
      currentDate,
      formatDate
    }
  }
}
</script>

<style scoped>
.stat-icon {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-placeholder {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card {
  transition: transform 0.2s ease-in-out;
}

.card:hover {
  transform: translateY(-2px);
}

.btn {
  transition: all 0.2s ease-in-out;
}

@media (max-width: 768px) {
  .stat-icon {
    width: 50px;
    height: 50px;
  }
  
  .avatar-placeholder {
    width: 40px;
    height: 40px;
  }
}
</style>

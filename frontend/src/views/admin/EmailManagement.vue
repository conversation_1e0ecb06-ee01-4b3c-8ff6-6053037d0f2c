<template>
  <div class="email-management">
    <div class="container-fluid">
      <!-- 页面标题 -->
      <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h2 class="h4 mb-1">
            <i class="bi bi-envelope-gear me-2"></i>
            邮件管理
          </h2>
          <p class="text-muted mb-0">管理邮件模板、发送记录和邮件配置</p>
        </div>
        <div class="btn-group">
          <button class="btn btn-primary" @click="showTemplateModal = true">
            <i class="bi bi-plus-lg me-1"></i>
            新建模板
          </button>
          <button class="btn btn-outline-primary" @click="refreshData">
            <i class="bi bi-arrow-clockwise me-1"></i>
            刷新
          </button>
        </div>
      </div>

      <!-- 统计卡片 -->
      <div class="row mb-4">
        <div class="col-md-3">
          <div class="card text-center">
            <div class="card-body">
              <div class="display-6 text-primary mb-2">
                <i class="bi bi-envelope-check"></i>
              </div>
              <h5 class="card-title">{{ stats.totalSent }}</h5>
              <p class="card-text text-muted">总发送量</p>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card text-center">
            <div class="card-body">
              <div class="display-6 text-success mb-2">
                <i class="bi bi-check-circle"></i>
              </div>
              <h5 class="card-title">{{ stats.successRate }}%</h5>
              <p class="card-text text-muted">成功率</p>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card text-center">
            <div class="card-body">
              <div class="display-6 text-info mb-2">
                <i class="bi bi-file-earmark-text"></i>
              </div>
              <h5 class="card-title">{{ stats.templateCount }}</h5>
              <p class="card-text text-muted">邮件模板</p>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card text-center">
            <div class="card-body">
              <div class="display-6 text-warning mb-2">
                <i class="bi bi-clock"></i>
              </div>
              <h5 class="card-title">{{ stats.todaySent }}</h5>
              <p class="card-text text-muted">今日发送</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 标签页 -->
      <ul class="nav nav-tabs mb-4" role="tablist">
        <li class="nav-item" role="presentation">
          <button
            class="nav-link"
            :class="{ active: activeTab === 'templates' }"
            @click="activeTab = 'templates'"
          >
            <i class="bi bi-file-earmark-text me-1"></i>
            邮件模板
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button
            class="nav-link"
            :class="{ active: activeTab === 'history' }"
            @click="activeTab = 'history'"
          >
            <i class="bi bi-clock-history me-1"></i>
            发送记录
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button
            class="nav-link"
            :class="{ active: activeTab === 'settings' }"
            @click="activeTab = 'settings'"
          >
            <i class="bi bi-gear me-1"></i>
            邮件配置
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button
            class="nav-link"
            :class="{ active: activeTab === 'test' }"
            @click="activeTab = 'test'"
          >
            <i class="bi bi-send me-1"></i>
            测试发送
          </button>
        </li>
      </ul>

      <!-- 标签页内容 -->
      <div class="tab-content">
        <!-- 邮件模板 -->
        <div v-if="activeTab === 'templates'" class="tab-pane active">
          <div class="row">
            <div
              v-for="template in emailTemplates"
              :key="template.id"
              class="col-md-6 col-lg-4 mb-3"
            >
              <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                  <h6 class="mb-0">{{ template.name }}</h6>
                  <div class="dropdown">
                    <button
                      class="btn btn-sm btn-outline-secondary dropdown-toggle"
                      type="button"
                      :data-bs-toggle="`dropdown-${template.id}`"
                    >
                      操作
                    </button>
                    <ul class="dropdown-menu">
                      <li>
                        <a class="dropdown-item" href="#" @click="editTemplate(template)">
                          <i class="bi bi-pencil me-1"></i>编辑
                        </a>
                      </li>
                      <li>
                        <a class="dropdown-item" href="#" @click="previewTemplate(template)">
                          <i class="bi bi-eye me-1"></i>预览
                        </a>
                      </li>
                      <li>
                        <a class="dropdown-item" href="#" @click="duplicateTemplate(template)">
                          <i class="bi bi-files me-1"></i>复制
                        </a>
                      </li>
                      <li><hr class="dropdown-divider"></li>
                      <li>
                        <a class="dropdown-item text-danger" href="#" @click="deleteTemplate(template)">
                          <i class="bi bi-trash me-1"></i>删除
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
                <div class="card-body">
                  <p class="card-text text-muted small">{{ template.description }}</p>
                  <div class="d-flex justify-content-between align-items-center">
                    <span class="badge bg-primary">{{ template.type }}</span>
                    <small class="text-muted">{{ formatTime(template.updatedTime) }}</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 发送记录 -->
        <div v-if="activeTab === 'history'" class="tab-pane active">
          <EmailService type="history" />
        </div>

        <!-- 邮件配置 -->
        <div v-if="activeTab === 'settings'" class="tab-pane active">
          <div class="row">
            <div class="col-md-8">
              <div class="card">
                <div class="card-header">
                  <h6 class="mb-0">SMTP 配置</h6>
                </div>
                <div class="card-body">
                  <form @submit.prevent="saveEmailSettings">
                    <div class="row">
                      <div class="col-md-6 mb-3">
                        <label class="form-label">SMTP 服务器</label>
                        <input
                          v-model="emailSettings.smtpHost"
                          type="text"
                          class="form-control"
                          placeholder="smtp.example.com"
                        />
                      </div>
                      <div class="col-md-6 mb-3">
                        <label class="form-label">端口</label>
                        <input
                          v-model="emailSettings.smtpPort"
                          type="number"
                          class="form-control"
                          placeholder="587"
                        />
                      </div>
                    </div>
                    <div class="row">
                      <div class="col-md-6 mb-3">
                        <label class="form-label">用户名</label>
                        <input
                          v-model="emailSettings.smtpUsername"
                          type="text"
                          class="form-control"
                          placeholder="<EMAIL>"
                        />
                      </div>
                      <div class="col-md-6 mb-3">
                        <label class="form-label">密码</label>
                        <input
                          v-model="emailSettings.smtpPassword"
                          type="password"
                          class="form-control"
                          placeholder="your-password"
                        />
                      </div>
                    </div>
                    <div class="row">
                      <div class="col-md-6 mb-3">
                        <label class="form-label">发件人名称</label>
                        <input
                          v-model="emailSettings.fromName"
                          type="text"
                          class="form-control"
                          placeholder="Code-Combined"
                        />
                      </div>
                      <div class="col-md-6 mb-3">
                        <label class="form-label">发件人邮箱</label>
                        <input
                          v-model="emailSettings.fromEmail"
                          type="email"
                          class="form-control"
                          placeholder="<EMAIL>"
                        />
                      </div>
                    </div>
                    <div class="mb-3">
                      <div class="form-check">
                        <input
                          v-model="emailSettings.enableSsl"
                          class="form-check-input"
                          type="checkbox"
                          id="enableSsl"
                        />
                        <label class="form-check-label" for="enableSsl">
                          启用 SSL/TLS
                        </label>
                      </div>
                    </div>
                    <div class="d-flex gap-2">
                      <button type="submit" class="btn btn-primary" :disabled="savingSettings">
                        <span v-if="savingSettings" class="spinner-border spinner-border-sm me-1"></span>
                        保存配置
                      </button>
                      <button type="button" class="btn btn-outline-secondary" @click="testEmailConnection">
                        测试连接
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 测试发送 -->
        <div v-if="activeTab === 'test'" class="tab-pane active">
          <div class="row">
            <div class="col-md-8">
              <div class="card">
                <div class="card-header">
                  <h6 class="mb-0">发送测试邮件</h6>
                </div>
                <div class="card-body">
                  <EmailService type="preview" :template="testEmailTemplate" @test-sent="handleTestSent" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 模板编辑模态框 -->
    <div v-if="showTemplateModal" class="modal fade show d-block" tabindex="-1">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">
              {{ editingTemplate ? '编辑模板' : '新建模板' }}
            </h5>
            <button type="button" class="btn-close" @click="closeTemplateModal"></button>
          </div>
          <div class="modal-body">
            <form @submit.prevent="saveTemplate">
              <div class="mb-3">
                <label class="form-label">模板名称</label>
                <input
                  v-model="templateForm.name"
                  type="text"
                  class="form-control"
                  required
                />
              </div>
              <div class="mb-3">
                <label class="form-label">模板类型</label>
                <select v-model="templateForm.type" class="form-select" required>
                  <option value="verification">验证码邮件</option>
                  <option value="notification">通知邮件</option>
                  <option value="welcome">欢迎邮件</option>
                  <option value="reset">密码重置</option>
                </select>
              </div>
              <div class="mb-3">
                <label class="form-label">描述</label>
                <textarea
                  v-model="templateForm.description"
                  class="form-control"
                  rows="2"
                ></textarea>
              </div>
              <div class="mb-3">
                <label class="form-label">邮件内容</label>
                <textarea
                  v-model="templateForm.content"
                  class="form-control"
                  rows="10"
                  placeholder="请输入邮件HTML内容..."
                ></textarea>
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" @click="closeTemplateModal">
              取消
            </button>
            <button type="button" class="btn btn-primary" @click="saveTemplate" :disabled="savingTemplate">
              <span v-if="savingTemplate" class="spinner-border spinner-border-sm me-1"></span>
              保存
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useStore } from 'vuex'
import { showSuccess, showError, confirm } from '@/utils/message'
import EmailService from '@/components/common/EmailService.vue'

// 定义组件名称
defineOptions({
  name: 'EmailManagement'
})

// 邮件模板接口
interface EmailTemplate {
  id: number
  name: string
  type: string
  description: string
  content: string
  updatedTime: string
}

// 邮件设置接口
interface EmailSettings {
  smtpHost: string
  smtpPort: number
  smtpUsername: string
  smtpPassword: string
  fromName: string
  fromEmail: string
  enableSsl: boolean
}

// 统计数据接口
interface EmailStats {
  totalSent: number
  successRate: number
  templateCount: number
  todaySent: number
}

const store = useStore()

// 响应式数据
const activeTab = ref<string>('templates')
const emailTemplates = ref<EmailTemplate[]>([])
const showTemplateModal = ref<boolean>(false)
const editingTemplate = ref<EmailTemplate | null>(null)
const savingTemplate = ref<boolean>(false)
const savingSettings = ref<boolean>(false)

// 统计数据
const stats = reactive<EmailStats>({
  totalSent: 0,
  successRate: 0,
  templateCount: 0,
  todaySent: 0
})

// 邮件设置
const emailSettings = reactive<EmailSettings>({
  smtpHost: '',
  smtpPort: 587,
  smtpUsername: '',
  smtpPassword: '',
  fromName: 'Code-Combined',
  fromEmail: '',
  enableSsl: true
})

// 模板表单
const templateForm = reactive({
  name: '',
  type: 'verification',
  description: '',
  content: ''
})

// 测试邮件模板
const testEmailTemplate = ref<string>(`
  <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
    <div style="background: #007bff; color: white; padding: 20px; text-align: center;">
      <h2 style="margin: 0;">Code-Combined 测试邮件</h2>
    </div>
    <div style="padding: 30px 20px;">
      <h3>邮件发送测试</h3>
      <p>这是一封测试邮件，用于验证邮件服务配置是否正确。</p>
      <p>如果您收到这封邮件，说明邮件服务工作正常。</p>
      <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <strong>测试时间：</strong> ${new Date().toLocaleString('zh-CN')}
      </div>
    </div>
    <div style="background: #f8f9fa; padding: 20px; text-align: center; color: #666;">
      <p style="margin: 0;">此邮件由 Code-Combined 系统自动发送</p>
    </div>
  </div>
`)

// 方法
const refreshData = async (): Promise<void> => {
  await Promise.all([
    loadEmailTemplates(),
    loadEmailStats(),
    loadEmailSettings()
  ])
}

const loadEmailTemplates = async (): Promise<void> => {
  try {
    const response = await store.dispatch('email/getTemplates')
    emailTemplates.value = response.data
    stats.templateCount = emailTemplates.value.length
  } catch (error) {
    showError('加载邮件模板失败')
  }
}

const loadEmailStats = async (): Promise<void> => {
  try {
    const response = await store.dispatch('email/getStats')
    Object.assign(stats, response.data)
  } catch (error) {
    showError('加载统计数据失败')
  }
}

const loadEmailSettings = async (): Promise<void> => {
  try {
    const response = await store.dispatch('email/getSettings')
    Object.assign(emailSettings, response.data)
  } catch (error) {
    showError('加载邮件配置失败')
  }
}

const editTemplate = (template: EmailTemplate): void => {
  editingTemplate.value = template
  Object.assign(templateForm, template)
  showTemplateModal.value = true
}

const previewTemplate = (template: EmailTemplate): void => {
  // 在新窗口中预览模板
  const previewWindow = window.open('', '_blank')
  if (previewWindow) {
    previewWindow.document.write(template.content)
    previewWindow.document.close()
  }
}

const duplicateTemplate = async (template: EmailTemplate): Promise<void> => {
  try {
    await store.dispatch('email/duplicateTemplate', template.id)
    showSuccess('模板复制成功')
    loadEmailTemplates()
  } catch (error) {
    showError('模板复制失败')
  }
}

const deleteTemplate = (template: EmailTemplate): void => {
  confirm({
    title: '确认删除',
    content: `确定要删除模板"${template.name}"吗？此操作不可恢复。`,
    onOk: async () => {
      try {
        await store.dispatch('email/deleteTemplate', template.id)
        showSuccess('模板删除成功')
        loadEmailTemplates()
      } catch (error) {
        showError('模板删除失败')
      }
    }
  })
}

const closeTemplateModal = (): void => {
  showTemplateModal.value = false
  editingTemplate.value = null
  Object.assign(templateForm, {
    name: '',
    type: 'verification',
    description: '',
    content: ''
  })
}

const saveTemplate = async (): Promise<void> => {
  savingTemplate.value = true
  try {
    if (editingTemplate.value) {
      await store.dispatch('email/updateTemplate', {
        id: editingTemplate.value.id,
        ...templateForm
      })
      showSuccess('模板更新成功')
    } else {
      await store.dispatch('email/createTemplate', templateForm)
      showSuccess('模板创建成功')
    }
    closeTemplateModal()
    loadEmailTemplates()
  } catch (error) {
    showError('保存模板失败')
  } finally {
    savingTemplate.value = false
  }
}

const saveEmailSettings = async (): Promise<void> => {
  savingSettings.value = true
  try {
    await store.dispatch('email/updateSettings', emailSettings)
    showSuccess('邮件配置保存成功')
  } catch (error) {
    showError('保存邮件配置失败')
  } finally {
    savingSettings.value = false
  }
}

const testEmailConnection = async (): Promise<void> => {
  try {
    await store.dispatch('email/testConnection', emailSettings)
    showSuccess('邮件服务连接测试成功')
  } catch (error) {
    showError('邮件服务连接测试失败')
  }
}

const handleTestSent = (success: boolean): void => {
  if (success) {
    showSuccess('测试邮件发送成功')
  } else {
    showError('测试邮件发送失败')
  }
}

const formatTime = (time: string): string => {
  return new Date(time).toLocaleDateString('zh-CN')
}

// 生命周期
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.email-management {
  padding: 1rem;
}

.modal.show {
  background: rgba(0, 0, 0, 0.5);
}

.card {
  transition: transform 0.2s;
}

.card:hover {
  transform: translateY(-2px);
}

.display-6 {
  font-size: 2rem;
}
</style>

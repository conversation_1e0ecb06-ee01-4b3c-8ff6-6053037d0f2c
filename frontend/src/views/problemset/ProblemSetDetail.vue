<template>
  <div class="problem-set-detail">
    <div class="container py-4">
      <!-- 加载状态 -->
      <div v-if="loading" class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">加载中...</span>
        </div>
        <p class="mt-2 text-muted">加载中...</p>
      </div>

      <!-- 题集不存在 -->
      <div v-else-if="!problemSet" class="text-center py-5">
        <i class="bi bi-exclamation-triangle text-warning" style="font-size: 4rem;"></i>
        <h4 class="mt-3 text-muted">题集不存在</h4>
        <p class="text-muted">该题集可能已被删除或您没有访问权限</p>
        <router-link to="/problemsets" class="btn btn-primary">
          返回题集列表
        </router-link>
      </div>

      <!-- 题集详情 -->
      <div v-else>
        <!-- 面包屑导航 -->
        <nav aria-label="breadcrumb" class="mb-4">
          <ol class="breadcrumb">
            <li class="breadcrumb-item">
              <router-link to="/" class="text-decoration-none">首页</router-link>
            </li>
            <li class="breadcrumb-item">
              <router-link to="/problemsets" class="text-decoration-none">题集</router-link>
            </li>
            <li class="breadcrumb-item active" aria-current="page">
              {{ problemSet.name }}
            </li>
          </ol>
        </nav>

        <!-- 题集头部信息 -->
        <div class="row mb-4">
          <div class="col-12">
            <div class="card border-0 shadow-sm">
              <div class="card-body p-4">
                <div class="row">
                  <div class="col-md-8">
                    <div class="d-flex align-items-start mb-3">
                      <div class="flex-grow-1">
                        <h1 class="h2 mb-2">{{ problemSet.name }}</h1>
                        <div class="d-flex align-items-center gap-3 mb-3">
                          <span
                            :class="problemSet.isPublic ? 'badge bg-success' : 'badge bg-secondary'"
                          >
                            {{ problemSet.isPublic ? '公开题集' : '私有题集' }}
                          </span>
                          <small class="text-muted">
                            <i class="bi bi-person me-1"></i>
                            {{ problemSet.creatorName }}
                          </small>
                          <small class="text-muted">
                            <i class="bi bi-calendar me-1"></i>
                            {{ formatDate(problemSet.createdTime) }}
                          </small>
                        </div>
                        <p class="text-muted mb-0">
                          {{ problemSet.description || '暂无描述' }}
                        </p>
                      </div>
                      <div class="ms-3">
                        <div class="dropdown" v-if="canEdit">
                          <button
                            class="btn btn-outline-secondary dropdown-toggle"
                            type="button"
                            data-bs-toggle="dropdown"
                          >
                            <i class="bi bi-three-dots"></i>
                          </button>
                          <ul class="dropdown-menu">
                            <li>
                              <button @click="editProblemSet" class="dropdown-item">
                                <i class="bi bi-pencil me-2"></i>
                                编辑题集
                              </button>
                            </li>
                            <li>
                              <button @click="deleteProblemSet" class="dropdown-item text-danger">
                                <i class="bi bi-trash me-2"></i>
                                删除题集
                              </button>
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="row g-3">
                      <div class="col-6">
                        <div class="text-center">
                          <div class="h4 text-primary mb-1">{{ problemSet.problemCount }}</div>
                          <small class="text-muted">题目数量</small>
                        </div>
                      </div>
                      <div class="col-6">
                        <div class="text-center">
                          <div class="h4 text-success mb-1">{{ problemSet.viewCount }}</div>
                          <small class="text-muted">浏览次数</small>
                        </div>
                      </div>
                      <div class="col-6">
                        <div class="text-center">
                          <div class="h4 text-info mb-1">{{ problemSet.likeCount }}</div>
                          <small class="text-muted">点赞数量</small>
                        </div>
                      </div>
                      <div class="col-6">
                        <div class="text-center">
                          <button @click="toggleLike" class="btn btn-outline-danger btn-sm">
                            <i :class="isLiked ? 'bi bi-heart-fill' : 'bi bi-heart'"></i>
                            {{ isLiked ? '已点赞' : '点赞' }}
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="row mb-4">
          <div class="col-12">
            <div class="d-flex gap-2 flex-wrap">
              <button
                v-if="isAuthenticated && canEdit"
                @click="showAddProblemModal"
                class="btn btn-primary"
              >
                <i class="bi bi-plus-circle me-2"></i>
                添加题目
              </button>
              <button @click="shareProblemSet" class="btn btn-outline-primary">
                <i class="bi bi-share me-2"></i>
                分享题集
              </button>
              <button @click="exportProblemSet" class="btn btn-outline-secondary">
                <i class="bi bi-download me-2"></i>
                导出题集
              </button>
            </div>
          </div>
        </div>

        <!-- 题目列表 -->
        <div class="row">
          <div class="col-12">
            <div class="card border-0 shadow-sm">
              <div class="card-header bg-transparent border-0">
                <div class="d-flex justify-content-between align-items-center">
                  <h5 class="mb-0">
                    <i class="bi bi-puzzle me-2"></i>
                    题目列表 ({{ problemSet.problemCount }})
                  </h5>
                  <div class="d-flex gap-2">
                    <select v-model="sortBy" class="form-select form-select-sm" style="width: auto;">
                      <option value="order">默认排序</option>
                      <option value="difficulty">按难度排序</option>
                      <option value="title">按标题排序</option>
                    </select>
                  </div>
                </div>
              </div>
              <div class="card-body p-0">
                <div v-if="problems.length === 0" class="text-center py-5">
                  <i class="bi bi-puzzle text-muted" style="font-size: 3rem;"></i>
                  <h5 class="mt-3 text-muted">暂无题目</h5>
                  <p class="text-muted">该题集还没有添加题目</p>
                  <button
                    v-if="isAuthenticated && canEdit"
                    @click="showAddProblemModal"
                    class="btn btn-primary"
                  >
                    <i class="bi bi-plus-circle me-2"></i>
                    添加第一个题目
                  </button>
                </div>

                <div v-else class="table-responsive">
                  <table class="table table-hover mb-0">
                    <thead class="table-light">
                      <tr>
                        <th style="width: 60px;">#</th>
                        <th>题目标题</th>
                        <th style="width: 100px;">难度</th>
                        <th style="width: 150px;">标签</th>
                        <th style="width: 120px;">操作</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="(problem, index) in sortedProblems" :key="problem.id">
                        <td class="text-muted">{{ index + 1 }}</td>
                        <td>
                          <router-link
                            :to="`/problems/${problem.id}`"
                            class="text-decoration-none fw-medium"
                          >
                            {{ problem.title }}
                          </router-link>
                        </td>
                        <td>
                          <span
                            :class="getDifficultyClass(problem.difficulty)"
                            class="badge"
                          >
                            {{ getDifficultyText(problem.difficulty) }}
                          </span>
                        </td>
                        <td>
                          <div class="d-flex flex-wrap gap-1">
                            <span
                              v-for="tag in problem.tags?.split(',').slice(0, 2)"
                              :key="tag"
                              class="badge bg-light text-dark"
                            >
                              {{ tag.trim() }}
                            </span>
                            <span
                              v-if="problem.tags?.split(',').length > 2"
                              class="badge bg-light text-muted"
                            >
                              +{{ problem.tags.split(',').length - 2 }}
                            </span>
                          </div>
                        </td>
                        <td>
                          <div class="d-flex gap-1">
                            <router-link
                              :to="`/problems/${problem.id}`"
                              class="btn btn-outline-primary btn-sm"
                            >
                              <i class="bi bi-eye"></i>
                            </router-link>
                            <button
                              v-if="canEdit"
                              @click="removeProblem(problem)"
                              class="btn btn-outline-danger btn-sm"
                            >
                              <i class="bi bi-trash"></i>
                            </button>
                          </div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加题目模态框 -->
    <AddProblemModal
      v-if="problemSet"
      :problem-set-id="problemSet.id"
      :existing-problem-ids="existingProblemIds"
      @added="handleProblemAdded"
    />
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { useRouter, useRoute } from 'vue-router'
import { showSuccess, showError, showConfirm } from '@/utils/message'
import AddProblemModal from '@/components/problemset/AddProblemModal.vue'

export default {
  name: 'ProblemSetDetail',
  components: {
    AddProblemModal
  },
  setup() {
    const store = useStore()
    const router = useRouter()
    const route = useRoute()

    const sortBy = ref('order')
    const isLiked = ref(false)

    const problemSet = computed(() => store.getters['problemset/currentProblemSet'])
    const loading = computed(() => store.getters['problemset/loading'])
    const isAuthenticated = computed(() => store.getters['auth/isAuthenticated'])
    const user = computed(() => store.getters['auth/user'])

    const canEdit = computed(() => {
      if (!user.value || !problemSet.value) return false
      return user.value.role === 'ADMIN' || problemSet.value.creatorId === user.value.id
    })

    // 题目数据
    const problems = ref([])

    const loadProblemSetProblems = async () => {
      if (!problemSet.value) return

      try {
        const response = await store.dispatch('problemset/fetchProblemSetProblems', problemSet.value.id)
        problems.value = response
      } catch (error) {
        console.error('获取题集题目失败:', error)
        // 如果API失败，使用模拟数据
        problems.value = [
          {
            id: 1,
            title: '两数之和',
            difficulty: 'EASY',
            tags: '数组,哈希表',
            description: '给定一个整数数组 nums 和一个整数目标值 target...'
          },
          {
            id: 2,
            title: '两数相加',
            difficulty: 'MEDIUM',
            tags: '链表,数学',
            description: '给你两个非空的链表，表示两个非负的整数...'
          },
          {
            id: 3,
            title: '无重复字符的最长子串',
            difficulty: 'MEDIUM',
            tags: '哈希表,字符串,滑动窗口',
            description: '给定一个字符串 s ，请你找出其中不含有重复字符的最长子串的长度...'
          }
        ].slice(0, problemSet.value.problemCount || 0)
      }
    }

    const sortedProblems = computed(() => {
      const list = [...problems.value]

      switch (sortBy.value) {
        case 'difficulty':
          return list.sort((a, b) => {
            const order = { 'EASY': 1, 'MEDIUM': 2, 'HARD': 3 }
            return order[a.difficulty] - order[b.difficulty]
          })
        case 'title':
          return list.sort((a, b) => a.title.localeCompare(b.title))
        default:
          return list
      }
    })

    const existingProblemIds = computed(() => {
      return problems.value.map(p => p.id)
    })

    const formatDate = (dateString) => {
      if (!dateString) return '-'
      return new Date(dateString).toLocaleDateString('zh-CN')
    }

    const getDifficultyClass = (difficulty) => {
      const classes = {
        'EASY': 'bg-success',
        'MEDIUM': 'bg-warning',
        'HARD': 'bg-danger'
      }
      return classes[difficulty] || 'bg-secondary'
    }

    const getDifficultyText = (difficulty) => {
      const texts = {
        'EASY': '简单',
        'MEDIUM': '中等',
        'HARD': '困难'
      }
      return texts[difficulty] || difficulty
    }

    const fetchProblemSet = async () => {
      try {
        const id = route.params.id
        await store.dispatch('problemset/fetchProblemSetById', id)
        // 获取题集信息后，加载题目列表
        await loadProblemSetProblems()
      } catch (error) {
        showError(error.message || '获取题集详情失败')
      }
    }

    const editProblemSet = () => {
      router.push(`/problemsets/${problemSet.value.id}/edit`)
    }

    const deleteProblemSet = async () => {
      const confirmed = await showConfirm('确定要删除这个题集吗？删除后无法恢复。')
      if (!confirmed) return

      try {
        await store.dispatch('problemset/deleteProblemSet', problemSet.value.id)
        showSuccess('题集删除成功')
        router.push('/problemsets')
      } catch (error) {
        showError(error.message || '删除题集失败')
      }
    }

    const toggleLike = () => {
      isLiked.value = !isLiked.value
      if (isLiked.value) {
        showSuccess('点赞成功')
      } else {
        showSuccess('取消点赞')
      }
    }

    const showAddProblemModal = () => {
      const modal = new window.bootstrap.Modal(document.getElementById('addProblemModal'))
      modal.show()
    }

    const handleProblemAdded = async (data) => {
      console.log('题目已添加到题集:', data)
      // 刷新题集信息和题目列表
      await fetchProblemSet()
      showSuccess(`已成功添加 ${data.problemIds.length} 个题目`)
    }

    const removeProblem = async (problem) => {
      const confirmed = await showConfirm(`确定要从题集中移除题目"${problem.title}"吗？`)
      if (!confirmed) return

      try {
        await store.dispatch('problemset/removeProblemFromProblemSet', {
          problemSetId: problemSet.value.id,
          problemId: problem.id
        })
        showSuccess('题目移除成功')
        // 刷新题集信息
        fetchProblemSet()
      } catch (error) {
        showError('移除失败：' + error.message)
      }
    }

    const shareProblemSet = () => {
      const url = window.location.href
      navigator.clipboard.writeText(url).then(() => {
        showSuccess('题集链接已复制到剪贴板')
      }).catch(() => {
        showError('复制失败，请手动复制链接')
      })
    }

    const exportProblemSet = () => {
      showSuccess('导出功能开发中')
    }

    onMounted(() => {
      fetchProblemSet()
    })

    return {
      problemSet,
      loading,
      isAuthenticated,
      user,
      canEdit,
      problems,
      sortedProblems,
      existingProblemIds,
      sortBy,
      isLiked,
      formatDate,
      getDifficultyClass,
      getDifficultyText,
      loadProblemSetProblems,
      editProblemSet,
      deleteProblemSet,
      toggleLike,
      showAddProblemModal,
      handleProblemAdded,
      removeProblem,
      shareProblemSet,
      exportProblemSet
    }
  }
}
</script>

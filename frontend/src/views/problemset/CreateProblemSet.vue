<template>
  <div class="create-problem-set">
    <div class="container py-4">
      <!-- 面包屑导航 -->
      <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
          <li class="breadcrumb-item">
            <router-link to="/" class="text-decoration-none">首页</router-link>
          </li>
          <li class="breadcrumb-item">
            <router-link to="/problemsets" class="text-decoration-none">题集</router-link>
          </li>
          <li class="breadcrumb-item active" aria-current="page">
            {{ isEdit ? '编辑题集' : '创建题集' }}
          </li>
        </ol>
      </nav>

      <!-- 页面标题 -->
      <div class="row mb-4">
        <div class="col-12">
          <h2>
            <i class="bi bi-plus-circle me-2"></i>
            {{ isEdit ? '编辑题集' : '创建题集' }}
          </h2>
          <p class="text-muted">
            {{ isEdit ? '修改题集信息' : '创建一个新的算法题集，组织和分享你的题目' }}
          </p>
        </div>
      </div>

      <!-- 创建表单 -->
      <div class="row justify-content-center">
        <div class="col-lg-8">
          <div class="card border-0 shadow-sm">
            <div class="card-body p-4">
              <form @submit.prevent="handleSubmit">
                <!-- 题集名称 -->
                <div class="mb-4">
                  <label for="name" class="form-label fw-medium">
                    题集名称 <span class="text-danger">*</span>
                  </label>
                  <input
                    id="name"
                    v-model="form.name"
                    type="text"
                    class="form-control"
                    :class="{ 'is-invalid': errors.name }"
                    placeholder="请输入题集名称"
                    maxlength="100"
                    required
                  >
                  <div v-if="errors.name" class="invalid-feedback">
                    {{ errors.name }}
                  </div>
                  <div class="form-text">
                    {{ form.name.length }}/100 字符
                  </div>
                </div>

                <!-- 题集描述 -->
                <div class="mb-4">
                  <label for="description" class="form-label fw-medium">
                    题集描述
                  </label>
                  <textarea
                    id="description"
                    v-model="form.description"
                    class="form-control"
                    :class="{ 'is-invalid': errors.description }"
                    rows="4"
                    placeholder="请输入题集描述，介绍这个题集的内容和目标..."
                    maxlength="500"
                  ></textarea>
                  <div v-if="errors.description" class="invalid-feedback">
                    {{ errors.description }}
                  </div>
                  <div class="form-text">
                    {{ form.description.length }}/500 字符
                  </div>
                </div>

                <!-- 封面图片 -->
                <div class="mb-4">
                  <label for="coverImage" class="form-label fw-medium">
                    封面图片
                  </label>
                  <div class="row">
                    <div class="col-md-8">
                      <input
                        id="coverImage"
                        v-model="form.coverImage"
                        type="url"
                        class="form-control"
                        placeholder="请输入图片URL（可选）"
                      >
                      <div class="form-text">
                        支持 JPG、PNG、GIF 格式，建议尺寸 300x200
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="cover-preview">
                        <img
                          v-if="form.coverImage"
                          :src="form.coverImage"
                          alt="封面预览"
                          class="img-fluid rounded"
                          @error="handleImageError"
                        >
                        <div v-else class="preview-placeholder">
                          <i class="bi bi-image text-muted"></i>
                          <small class="text-muted d-block">封面预览</small>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 可见性设置 -->
                <div class="mb-4">
                  <label class="form-label fw-medium">
                    可见性设置 <span class="text-danger">*</span>
                  </label>
                  <div class="row g-3">
                    <div class="col-md-6">
                      <div class="form-check">
                        <input
                          id="public"
                          v-model="form.isPublic"
                          type="radio"
                          :value="1"
                          class="form-check-input"
                          name="visibility"
                        >
                        <label for="public" class="form-check-label">
                          <div class="d-flex align-items-center">
                            <i class="bi bi-globe text-success me-2"></i>
                            <div>
                              <div class="fw-medium">公开题集</div>
                              <small class="text-muted">所有人都可以查看和使用</small>
                            </div>
                          </div>
                        </label>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="form-check">
                        <input
                          id="private"
                          v-model="form.isPublic"
                          type="radio"
                          :value="0"
                          class="form-check-input"
                          name="visibility"
                        >
                        <label for="private" class="form-check-label">
                          <div class="d-flex align-items-center">
                            <i class="bi bi-lock text-warning me-2"></i>
                            <div>
                              <div class="fw-medium">私有题集</div>
                              <small class="text-muted">仅自己可以查看和编辑</small>
                            </div>
                          </div>
                        </label>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 标签 -->
                <div class="mb-4">
                  <label for="tags" class="form-label fw-medium">
                    标签
                  </label>
                  <input
                    id="tags"
                    v-model="form.tags"
                    type="text"
                    class="form-control"
                    placeholder="请输入标签，用逗号分隔（如：数组,动态规划,字符串）"
                  >
                  <div class="form-text">
                    添加相关标签有助于其他用户发现你的题集
                  </div>
                  <div v-if="tagList.length > 0" class="mt-2">
                    <span
                      v-for="tag in tagList"
                      :key="tag"
                      class="badge bg-primary me-1 mb-1"
                    >
                      {{ tag }}
                    </span>
                  </div>
                </div>

                <!-- 操作按钮 -->
                <div class="d-flex gap-3 justify-content-end">
                  <router-link to="/problemsets" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left me-2"></i>
                    取消
                  </router-link>
                  <button
                    type="button"
                    @click="saveDraft"
                    class="btn btn-outline-primary"
                    :disabled="loading"
                  >
                    <i class="bi bi-save me-2"></i>
                    保存草稿
                  </button>
                  <button
                    type="submit"
                    class="btn btn-primary"
                    :disabled="loading || !isFormValid"
                  >
                    <span v-if="loading" class="spinner-border spinner-border-sm me-2"></span>
                    <i v-else class="bi bi-check-circle me-2"></i>
                    {{ isEdit ? '更新题集' : '创建题集' }}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { reactive, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { useRouter, useRoute } from 'vue-router'
import { showSuccess, showError } from '@/utils/message'

export default {
  name: 'CreateProblemSet',
  setup() {
    const store = useStore()
    const router = useRouter()
    const route = useRoute()

    const form = reactive({
      name: '',
      description: '',
      coverImage: '',
      isPublic: 1,
      tags: ''
    })

    const errors = reactive({})
    const loading = computed(() => store.getters['problemset/loading'])
    const isEdit = computed(() => !!route.params.id)

    const tagList = computed(() => {
      if (!form.tags) return []
      return form.tags.split(',').map(tag => tag.trim()).filter(tag => tag)
    })

    const isFormValid = computed(() => {
      return form.name.trim().length > 0 && form.isPublic !== undefined
    })

    const validateForm = () => {
      Object.keys(errors).forEach(key => delete errors[key])

      if (!form.name.trim()) {
        errors.name = '请输入题集名称'
      } else if (form.name.length > 100) {
        errors.name = '题集名称不能超过100个字符'
      }

      if (form.description.length > 500) {
        errors.description = '题集描述不能超过500个字符'
      }

      return Object.keys(errors).length === 0
    }

    const handleImageError = () => {
      form.coverImage = ''
      showError('图片加载失败，请检查图片URL是否正确')
    }

    const saveDraft = () => {
      if (!validateForm()) return

      // 保存到本地存储
      const draftKey = isEdit.value ? `problemset_draft_${route.params.id}` : 'problemset_draft_new'
      localStorage.setItem(draftKey, JSON.stringify(form))
      showSuccess('草稿已保存')
    }

    const loadDraft = () => {
      const draftKey = isEdit.value ? `problemset_draft_${route.params.id}` : 'problemset_draft_new'
      const draft = localStorage.getItem(draftKey)
      if (draft) {
        try {
          const draftData = JSON.parse(draft)
          Object.assign(form, draftData)
        } catch (error) {
          console.error('加载草稿失败:', error)
        }
      }
    }

    const clearDraft = () => {
      const draftKey = isEdit.value ? `problemset_draft_${route.params.id}` : 'problemset_draft_new'
      localStorage.removeItem(draftKey)
    }

    const handleSubmit = async () => {
      if (!validateForm()) return

      try {
        const submitData = {
          name: form.name.trim(),
          description: form.description.trim(),
          coverImage: form.coverImage.trim() || null,
          isPublic: form.isPublic,
          tags: form.tags.trim() || null
        }

        if (isEdit.value) {
          await store.dispatch('problemset/updateProblemSet', {
            id: route.params.id,
            data: submitData
          })
          showSuccess('题集更新成功')
        } else {
          const result = await store.dispatch('problemset/createProblemSet', submitData)
          showSuccess('题集创建成功')
          clearDraft()
          router.push(`/problemsets/${result.id}`)
          return
        }

        clearDraft()
        router.push(`/problemsets/${route.params.id}`)
      } catch (error) {
        showError(error.message || (isEdit.value ? '更新题集失败' : '创建题集失败'))
      }
    }

    const loadProblemSetForEdit = async () => {
      if (!isEdit.value) return

      try {
        const problemSet = await store.dispatch('problemset/fetchProblemSetById', route.params.id)

        // 检查编辑权限
        const user = store.getters['auth/user']
        if (!user || (user.role !== 'ADMIN' && problemSet.creatorId !== user.id)) {
          showError('您没有权限编辑此题集')
          router.push('/problemsets')
          return
        }

        // 填充表单
        form.name = problemSet.name
        form.description = problemSet.description || ''
        form.coverImage = problemSet.coverImage || ''
        form.isPublic = problemSet.isPublic
        form.tags = problemSet.tags || ''
      } catch (error) {
        showError(error.message || '获取题集信息失败')
        router.push('/problemsets')
      }
    }

    onMounted(() => {
      if (isEdit.value) {
        loadProblemSetForEdit()
      } else {
        loadDraft()
      }
    })

    return {
      form,
      errors,
      loading,
      isEdit,
      tagList,
      isFormValid,
      handleImageError,
      saveDraft,
      handleSubmit
    }
  }
}
</script>

<style scoped>
.cover-preview {
  height: 120px;
  border: 2px dashed #dee2e6;
  border-radius: 0.375rem;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.cover-preview img {
  max-height: 100%;
  max-width: 100%;
  object-fit: cover;
}

.preview-placeholder {
  text-align: center;
  color: #6c757d;
}

.preview-placeholder i {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.form-check-label {
  cursor: pointer;
  width: 100%;
}

.form-check {
  padding: 1rem;
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  transition: all 0.2s ease-in-out;
}

.form-check:hover {
  border-color: #007bff;
  background-color: #f8f9fa;
}

.form-check-input:checked + .form-check-label .form-check {
  border-color: #007bff;
  background-color: #e7f3ff;
}

@media (max-width: 768px) {
  .d-flex.gap-3 {
    flex-direction: column;
  }

  .d-flex.gap-3 .btn {
    margin-bottom: 0.5rem;
  }
}
</style>

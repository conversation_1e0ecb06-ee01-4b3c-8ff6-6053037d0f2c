<template>
  <div class="problem-sets">
    <div class="container py-4">
      <!-- 页面标题和操作 -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="d-flex justify-content-between align-items-center">
            <h2>
              <i class="bi bi-collection me-2"></i>
              题集列表
            </h2>
            <router-link
              v-if="isAuthenticated"
              to="/problemsets/create"
              class="btn btn-primary"
            >
              <i class="bi bi-plus-circle me-2"></i>
              创建题集
            </router-link>
          </div>
        </div>
      </div>

      <!-- 搜索和筛选 -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="card border-0 shadow-sm">
            <div class="card-body">
              <div class="row g-3">
                <div class="col-md-6">
                  <div class="input-group">
                    <span class="input-group-text">
                      <i class="bi bi-search"></i>
                    </span>
                    <input
                      v-model="searchForm.keyword"
                      type="text"
                      class="form-control"
                      placeholder="搜索题集名称或描述..."
                      @keyup.enter="handleSearch"
                    >
                  </div>
                </div>
                <div class="col-md-3">
                  <select v-model="searchForm.isPublic" class="form-select">
                    <option value="">全部题集</option>
                    <option value="1">公开题集</option>
                    <option value="0">私有题集</option>
                  </select>
                </div>
                <div class="col-md-3">
                  <div class="d-flex gap-2">
                    <button @click="handleSearch" class="btn btn-primary">
                      <i class="bi bi-search me-1"></i>
                      搜索
                    </button>
                    <button @click="handleReset" class="btn btn-outline-secondary">
                      <i class="bi bi-arrow-clockwise me-1"></i>
                      重置
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 题集列表 -->
      <div class="row">
        <div class="col-12">
          <div v-if="loading" class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2 text-muted">加载中...</p>
          </div>

          <div v-else-if="problemSets.length === 0" class="text-center py-5">
            <i class="bi bi-collection text-muted" style="font-size: 4rem;"></i>
            <h4 class="mt-3 text-muted">暂无题集</h4>
            <p class="text-muted">
              {{ searchForm.keyword ? '没有找到匹配的题集' : '还没有题集，创建第一个吧！' }}
            </p>
            <router-link
              v-if="isAuthenticated && !searchForm.keyword"
              to="/problemsets/create"
              class="btn btn-primary"
            >
              <i class="bi bi-plus-circle me-2"></i>
              创建题集
            </router-link>
          </div>

          <div v-else class="row g-4">
            <div
              v-for="problemSet in problemSets"
              :key="problemSet.id"
              class="col-md-6 col-lg-4"
            >
              <div class="card h-100 border-0 shadow-sm problem-set-card">
                <div class="card-body">
                  <!-- 题集标题 -->
                  <div class="d-flex justify-content-between align-items-start mb-3">
                    <h5 class="card-title mb-0">
                      <router-link
                        :to="`/problemsets/${problemSet.id}`"
                        class="text-decoration-none"
                      >
                        {{ problemSet.name }}
                      </router-link>
                    </h5>
                    <div class="d-flex gap-1">
                      <span
                        :class="problemSet.isPublic ? 'badge bg-success' : 'badge bg-secondary'"
                      >
                        {{ problemSet.isPublic ? '公开' : '私有' }}
                      </span>
                    </div>
                  </div>

                  <!-- 题集描述 -->
                  <p class="card-text text-muted text-truncate-3 mb-3">
                    {{ problemSet.description || '暂无描述' }}
                  </p>

                  <!-- 统计信息 -->
                  <div class="row g-2 mb-3">
                    <div class="col-4">
                      <small class="text-muted d-block">
                        <i class="bi bi-puzzle me-1"></i>
                        {{ problemSet.problemCount }} 题目
                      </small>
                    </div>
                    <div class="col-4">
                      <small class="text-muted d-block">
                        <i class="bi bi-eye me-1"></i>
                        {{ problemSet.viewCount }} 浏览
                      </small>
                    </div>
                    <div class="col-4">
                      <small class="text-muted d-block">
                        <i class="bi bi-heart me-1"></i>
                        {{ problemSet.likeCount }} 点赞
                      </small>
                    </div>
                  </div>

                  <!-- 创建者信息 -->
                  <div class="d-flex justify-content-between align-items-center">
                    <small class="text-muted">
                      <i class="bi bi-person me-1"></i>
                      {{ problemSet.creatorName }}
                    </small>
                    <small class="text-muted">
                      {{ formatDate(problemSet.createdTime) }}
                    </small>
                  </div>
                </div>

                <div class="card-footer bg-transparent border-0">
                  <div class="d-flex gap-2">
                    <router-link
                      :to="`/problemsets/${problemSet.id}`"
                      class="btn btn-outline-primary btn-sm flex-fill"
                    >
                      <i class="bi bi-eye me-1"></i>
                      查看详情
                    </router-link>
                    <button
                      v-if="canEdit(problemSet)"
                      @click="showAddProblemModal(problemSet)"
                      class="btn btn-outline-success btn-sm"
                      title="添加题目"
                    >
                      <i class="bi bi-plus-circle"></i>
                    </button>
                    <button
                      v-if="canEdit(problemSet)"
                      @click="editProblemSet(problemSet)"
                      class="btn btn-outline-secondary btn-sm"
                      title="编辑题集"
                    >
                      <i class="bi bi-pencil"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 分页 -->
          <div v-if="pagination.total > 0" class="row mt-4">
            <div class="col-12">
              <nav aria-label="题集分页">
                <ul class="pagination justify-content-center">
                  <li class="page-item" :class="{ disabled: pagination.current <= 1 }">
                    <button
                      class="page-link"
                      @click="changePage(pagination.current - 1)"
                      :disabled="pagination.current <= 1"
                    >
                      上一页
                    </button>
                  </li>

                  <li
                    v-for="page in visiblePages"
                    :key="page"
                    class="page-item"
                    :class="{ active: page === pagination.current }"
                  >
                    <button class="page-link" @click="changePage(page)">
                      {{ page }}
                    </button>
                  </li>

                  <li class="page-item" :class="{ disabled: pagination.current >= pagination.pages }">
                    <button
                      class="page-link"
                      @click="changePage(pagination.current + 1)"
                      :disabled="pagination.current >= pagination.pages"
                    >
                      下一页
                    </button>
                  </li>
                </ul>
              </nav>

              <div class="text-center text-muted">
                共 {{ pagination.total }} 个题集，第 {{ pagination.current }} / {{ pagination.pages }} 页
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加题目模态框 -->
    <AddProblemModal
      v-if="selectedProblemSet"
      :problem-set-id="selectedProblemSet.id"
      :existing-problem-ids="[]"
      @added="handleProblemAdded"
    />
  </div>
</template>

<script>
import { reactive, computed, onMounted, ref } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { showError, showConfirm, showSuccess } from '@/utils/message'
import AddProblemModal from '@/components/problemset/AddProblemModal.vue'

export default {
  name: 'ProblemSets',
  components: {
    AddProblemModal
  },
  setup() {
    const store = useStore()
    const router = useRouter()

    const searchForm = reactive({
      keyword: '',
      isPublic: ''
    })

    const selectedProblemSet = ref(null)

    const isAuthenticated = computed(() => store.getters['auth/isAuthenticated'])
    const user = computed(() => store.getters['auth/user'])
    const problemSets = computed(() => store.getters['problemset/problemSets'])
    const loading = computed(() => store.getters['problemset/loading'])
    const pagination = computed(() => store.getters['problemset/pagination'])

    const visiblePages = computed(() => {
      const current = pagination.value.current
      const total = pagination.value.pages
      const delta = 2
      const range = []
      const rangeWithDots = []

      for (let i = Math.max(2, current - delta); i <= Math.min(total - 1, current + delta); i++) {
        range.push(i)
      }

      if (current - delta > 2) {
        rangeWithDots.push(1, '...')
      } else {
        rangeWithDots.push(1)
      }

      rangeWithDots.push(...range)

      if (current + delta < total - 1) {
        rangeWithDots.push('...', total)
      } else {
        rangeWithDots.push(total)
      }

      return rangeWithDots.filter((item, index, arr) => arr.indexOf(item) === index && item !== 1 || index === 0)
    })

    const formatDate = (dateString) => {
      if (!dateString) return '-'
      return new Date(dateString).toLocaleDateString('zh-CN')
    }

    const canEdit = (problemSet) => {
      if (!user.value) return false
      return user.value.role === 'admin' || problemSet.creatorId === user.value.id
    }

    const fetchProblemSets = async (params = {}) => {
      try {
        const searchParams = {
          current: pagination.value.current,
          size: pagination.value.size,
          ...params
        }

        if (searchForm.keyword) {
          searchParams.keyword = searchForm.keyword
        }

        if (searchForm.isPublic !== '') {
          searchParams.isPublic = searchForm.isPublic
        }

        await store.dispatch('problemset/fetchProblemSets', searchParams)
      } catch (error) {
        showError(error.message || '获取题集列表失败')
      }
    }

    const handleSearch = () => {
      fetchProblemSets({ current: 1 })
    }

    const handleReset = () => {
      searchForm.keyword = ''
      searchForm.isPublic = ''
      fetchProblemSets({ current: 1 })
    }

    const changePage = (page) => {
      if (page < 1 || page > pagination.value.pages) return
      fetchProblemSets({ current: page })
    }

    const editProblemSet = (problemSet) => {
      router.push(`/problemsets/${problemSet.id}/edit`)
    }

    const showAddProblemModal = (problemSet) => {
      selectedProblemSet.value = problemSet
      // 使用 nextTick 确保 DOM 更新后再显示模态框
      setTimeout(() => {
        const modal = new window.bootstrap.Modal(document.getElementById('addProblemModal'))
        modal.show()
      }, 100)
    }

    const handleProblemAdded = async (data) => {
      console.log('题目已添加到题集:', data)
      showSuccess(`已成功添加 ${data.problemIds.length} 个题目到题集`)

      // 刷新题集列表以更新统计信息
      await fetchProblemSets()

      // 重置选中的题集
      selectedProblemSet.value = null
    }

    onMounted(() => {
      fetchProblemSets()
    })

    return {
      searchForm,
      isAuthenticated,
      user,
      problemSets,
      loading,
      pagination,
      visiblePages,
      selectedProblemSet,
      formatDate,
      canEdit,
      handleSearch,
      handleReset,
      changePage,
      editProblemSet,
      showAddProblemModal,
      handleProblemAdded
    }
  }
}
</script>

<style scoped>
.problem-set-card {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.problem-set-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.text-truncate-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.4;
  max-height: 4.2em;
}

.pagination .page-link {
  border-radius: 0.375rem;
  margin: 0 2px;
  border: 1px solid #dee2e6;
}

.pagination .page-item.active .page-link {
  background-color: #007bff;
  border-color: #007bff;
}

.badge {
  font-size: 0.75em;
}

@media (max-width: 768px) {
  .d-flex.gap-2 {
    flex-direction: column;
  }

  .d-flex.gap-2 .btn {
    margin-bottom: 0.5rem;
  }
}
</style>

<template>
  <div class="problem-detail">
    <div class="container py-4">
      <!-- 加载状态 -->
      <div v-if="loading" class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">加载中...</span>
        </div>
        <p class="mt-2 text-muted">加载中...</p>
      </div>

      <!-- 题目不存在 -->
      <div v-else-if="!problem" class="text-center py-5">
        <i class="bi bi-exclamation-triangle text-warning" style="font-size: 4rem;"></i>
        <h4 class="mt-3 text-muted">题目不存在</h4>
        <p class="text-muted">该题目可能已被删除或您没有访问权限</p>
        <router-link to="/problems" class="btn btn-primary">
          返回题目列表
        </router-link>
      </div>

      <!-- 题目详情 -->
      <div v-else>
        <!-- 面包屑导航 -->
        <nav aria-label="breadcrumb" class="mb-4">
          <ol class="breadcrumb">
            <li class="breadcrumb-item">
              <router-link to="/" class="text-decoration-none">首页</router-link>
            </li>
            <li class="breadcrumb-item">
              <router-link to="/problems" class="text-decoration-none">题目</router-link>
            </li>
            <li class="breadcrumb-item active" aria-current="page">
              {{ problem.title }}
            </li>
          </ol>
        </nav>

        <div class="row">
          <!-- 左侧：题目内容 -->
          <div class="col-lg-8">
            <!-- 题目头部 -->
            <div class="card border-0 shadow-sm mb-4">
              <div class="card-body">
                <div class="d-flex justify-content-between align-items-start mb-3">
                  <div class="flex-grow-1">
                    <h1 class="h3 mb-2">{{ problem.title }}</h1>
                    <div class="d-flex align-items-center gap-3 mb-3">
                      <span
                        :class="getDifficultyClass(problem.difficulty)"
                        class="badge fs-6"
                      >
                        {{ getDifficultyText(problem.difficulty) }}
                      </span>
                      <small class="text-muted">
                        <i class="bi bi-person me-1"></i>
                        {{ problem.creatorName }}
                      </small>
                      <small class="text-muted">
                        <i class="bi bi-calendar me-1"></i>
                        {{ formatDate(problem.createdTime) }}
                      </small>
                      <small v-if="problem.source" class="text-muted">
                        <i class="bi bi-tag me-1"></i>
                        {{ problem.source }}
                      </small>
                    </div>
                  </div>
                  <div class="ms-3">
                    <div class="dropdown" v-if="canEdit">
                      <button
                        class="btn btn-outline-secondary dropdown-toggle"
                        type="button"
                        data-bs-toggle="dropdown"
                      >
                        <i class="bi bi-three-dots"></i>
                      </button>
                      <ul class="dropdown-menu">
                        <li>
                          <button @click="editProblem" class="dropdown-item">
                            <i class="bi bi-pencil me-2"></i>
                            编辑题目
                          </button>
                        </li>
                        <li>
                          <button @click="deleteProblem" class="dropdown-item text-danger">
                            <i class="bi bi-trash me-2"></i>
                            删除题目
                          </button>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>

                <!-- 标签 -->
                <div v-if="tagList.length > 0" class="mb-3">
                  <span
                    v-for="tag in tagList"
                    :key="tag"
                    class="badge bg-light text-dark me-1 mb-1"
                  >
                    {{ tag }}
                  </span>
                </div>
              </div>
            </div>

            <!-- 题目描述 -->
            <div class="card border-0 shadow-sm mb-4">
              <div class="card-header bg-transparent border-0">
                <h5 class="mb-0">
                  <i class="bi bi-file-text me-2"></i>
                  题目描述
                </h5>
              </div>
              <div class="card-body">
                <div class="problem-content" v-html="formatContent(problem.description)"></div>
              </div>
            </div>

            <!-- 输入输出格式 -->
            <div v-if="problem.inputFormat || problem.outputFormat" class="card border-0 shadow-sm mb-4">
              <div class="card-header bg-transparent border-0">
                <h5 class="mb-0">
                  <i class="bi bi-arrow-left-right me-2"></i>
                  输入输出格式
                </h5>
              </div>
              <div class="card-body">
                <div v-if="problem.inputFormat" class="mb-3">
                  <h6 class="text-primary">输入格式</h6>
                  <div class="problem-content" v-html="formatContent(problem.inputFormat)"></div>
                </div>
                <div v-if="problem.outputFormat">
                  <h6 class="text-primary">输出格式</h6>
                  <div class="problem-content" v-html="formatContent(problem.outputFormat)"></div>
                </div>
              </div>
            </div>

            <!-- 示例 -->
            <div v-if="problem.sampleInput || problem.sampleOutput" class="card border-0 shadow-sm mb-4">
              <div class="card-header bg-transparent border-0">
                <h5 class="mb-0">
                  <i class="bi bi-code-square me-2"></i>
                  示例
                </h5>
              </div>
              <div class="card-body">
                <div class="row">
                  <div v-if="problem.sampleInput" class="col-md-6 mb-3">
                    <h6 class="text-success">示例输入</h6>
                    <pre class="bg-light p-3 rounded"><code>{{ problem.sampleInput }}</code></pre>
                  </div>
                  <div v-if="problem.sampleOutput" class="col-md-6 mb-3">
                    <h6 class="text-info">示例输出</h6>
                    <pre class="bg-light p-3 rounded"><code>{{ problem.sampleOutput }}</code></pre>
                  </div>
                </div>
              </div>
            </div>

            <!-- 约束条件 -->
            <div v-if="problem.constraints" class="card border-0 shadow-sm mb-4">
              <div class="card-header bg-transparent border-0">
                <h5 class="mb-0">
                  <i class="bi bi-exclamation-triangle me-2"></i>
                  约束条件
                </h5>
              </div>
              <div class="card-body">
                <div class="problem-content" v-html="formatContent(problem.constraints)"></div>
              </div>
            </div>
          </div>

          <!-- 右侧：题目信息和操作 -->
          <div class="col-lg-4">
            <!-- 题目信息 -->
            <div class="card border-0 shadow-sm mb-4">
              <div class="card-header bg-transparent border-0">
                <h5 class="mb-0">
                  <i class="bi bi-info-circle me-2"></i>
                  题目信息
                </h5>
              </div>
              <div class="card-body">
                <div class="row g-3">
                  <div class="col-6">
                    <div class="text-center">
                      <div class="h5 text-primary mb-1">{{ problem.timeLimit }}ms</div>
                      <small class="text-muted">时间限制</small>
                    </div>
                  </div>
                  <div class="col-6">
                    <div class="text-center">
                      <div class="h5 text-success mb-1">{{ problem.memoryLimit }}MB</div>
                      <small class="text-muted">内存限制</small>
                    </div>
                  </div>
                  <div v-if="problem.leetcodeId" class="col-12">
                    <div class="text-center">
                      <a
                        :href="`https://leetcode.cn/problems/${problem.leetcodeId}/`"
                        target="_blank"
                        class="btn btn-outline-warning btn-sm"
                      >
                        <i class="bi bi-link-45deg me-1"></i>
                        LeetCode 原题
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="card border-0 shadow-sm mb-4">
              <div class="card-header bg-transparent border-0">
                <h5 class="mb-0">
                  <i class="bi bi-gear me-2"></i>
                  操作
                </h5>
              </div>
              <div class="card-body">
                <div class="d-grid gap-2">
                  <button @click="startSolving" class="btn btn-primary">
                    <i class="bi bi-play-circle me-2"></i>
                    开始解题
                  </button>
                  <button @click="addToFavorites" class="btn btn-outline-warning">
                    <i :class="isFavorited ? 'bi bi-star-fill' : 'bi bi-star'" class="me-2"></i>
                    {{ isFavorited ? '已收藏' : '收藏题目' }}
                  </button>
                  <button
                    v-if="isAuthenticated"
                    @click="showAddToProblemSetModal"
                    class="btn btn-outline-success"
                  >
                    <i class="bi bi-collection-fill me-2"></i>
                    添加到题集
                  </button>
                  <button @click="shareProblem" class="btn btn-outline-info">
                    <i class="bi bi-share me-2"></i>
                    分享题目
                  </button>
                  <button @click="reportProblem" class="btn btn-outline-danger">
                    <i class="bi bi-flag me-2"></i>
                    举报题目
                  </button>
                </div>
              </div>
            </div>

            <!-- 相关题目 -->
            <div class="card border-0 shadow-sm">
              <div class="card-header bg-transparent border-0">
                <h5 class="mb-0">
                  <i class="bi bi-collection me-2"></i>
                  相关题目
                </h5>
              </div>
              <div class="card-body">
                <div class="text-center text-muted">
                  <i class="bi bi-hourglass-split"></i>
                  <p class="mt-2 mb-0">功能开发中</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加到题集模态框 -->
    <AddToProblemSetModal
      v-if="problem"
      :problem-id="problem.id"
      :problem-title="problem.title"
      @added="handleAddedToProblemSet"
    />
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { useRouter, useRoute } from 'vue-router'
import { showSuccess, showError, showConfirm } from '@/utils/message'
import AddToProblemSetModal from '@/components/problem/AddToProblemSetModal.vue'

export default {
  name: 'ProblemDetail',
  components: {
    AddToProblemSetModal
  },
  setup() {
    const store = useStore()
    const router = useRouter()
    const route = useRoute()

    const isFavorited = ref(false)

    const problem = computed(() => store.getters['problem/currentProblem'])
    const loading = computed(() => store.getters['problem/loading'])
    const isAuthenticated = computed(() => store.getters['auth/isAuthenticated'])
    const user = computed(() => store.getters['auth/user'])

    const canEdit = computed(() => {
      if (!user.value || !problem.value) return false
      return user.value.role === 'ADMIN' || problem.value.creatorId === user.value.id
    })

    const tagList = computed(() => {
      if (!problem.value?.tags) return []
      return problem.value.tags.split(',').map(tag => tag.trim()).filter(tag => tag)
    })

    const formatDate = (dateString) => {
      if (!dateString) return '-'
      return new Date(dateString).toLocaleDateString('zh-CN')
    }

    const getDifficultyClass = (difficulty) => {
      const classes = {
        'EASY': 'bg-success',
        'MEDIUM': 'bg-warning',
        'HARD': 'bg-danger'
      }
      return classes[difficulty] || 'bg-secondary'
    }

    const getDifficultyText = (difficulty) => {
      const texts = {
        'EASY': '简单',
        'MEDIUM': '中等',
        'HARD': '困难'
      }
      return texts[difficulty] || difficulty
    }

    const formatContent = (content) => {
      if (!content) return ''
      // 简单的文本格式化，将换行转换为<br>
      return content.replace(/\n/g, '<br>')
    }

    const fetchProblem = async () => {
      try {
        const id = route.params.id
        await store.dispatch('problem/fetchProblemById', id)
      } catch (error) {
        showError(error.message || '获取题目详情失败')
      }
    }

    const editProblem = () => {
      router.push(`/problems/${problem.value.id}/edit`)
    }

    const deleteProblem = async () => {
      const confirmed = await showConfirm('确定要删除这个题目吗？删除后无法恢复。')
      if (!confirmed) return

      try {
        await store.dispatch('problem/deleteProblem', problem.value.id)
        showSuccess('题目删除成功')
        router.push('/problems')
      } catch (error) {
        showError(error.message || '删除题目失败')
      }
    }

    const startSolving = () => {
      if (!isAuthenticated.value) {
        showError('请先登录后再开始解题')
        router.push('/login')
        return
      }
      showSuccess('解题功能开发中，敬请期待！')
    }

    const addToFavorites = () => {
      if (!isAuthenticated.value) {
        showError('请先登录后再收藏题目')
        router.push('/login')
        return
      }

      isFavorited.value = !isFavorited.value
      if (isFavorited.value) {
        showSuccess('题目已添加到收藏夹')
      } else {
        showSuccess('题目已从收藏夹移除')
      }
    }

    const shareProblem = () => {
      const url = window.location.href
      navigator.clipboard.writeText(url).then(() => {
        showSuccess('题目链接已复制到剪贴板')
      }).catch(() => {
        showError('复制失败，请手动复制链接')
      })
    }

    const reportProblem = () => {
      if (!isAuthenticated.value) {
        showError('请先登录后再举报题目')
        router.push('/login')
        return
      }
      showSuccess('举报功能开发中，如有问题请联系管理员')
    }

    const showAddToProblemSetModal = () => {
      const modal = new window.bootstrap.Modal(document.getElementById('addToProblemSetModal'))
      modal.show()
    }

    const handleAddedToProblemSet = (data) => {
      console.log('题目已添加到题集:', data)
      // 这里可以更新页面状态或刷新数据
    }

    onMounted(() => {
      fetchProblem()
    })

    return {
      problem,
      loading,
      isAuthenticated,
      user,
      canEdit,
      tagList,
      isFavorited,
      formatDate,
      getDifficultyClass,
      getDifficultyText,
      formatContent,
      editProblem,
      deleteProblem,
      startSolving,
      addToFavorites,
      shareProblem,
      reportProblem,
      showAddToProblemSetModal,
      handleAddedToProblemSet
    }
  }
}
</script>

<style scoped>
.problem-content {
  line-height: 1.6;
}

.problem-content p {
  margin-bottom: 1rem;
}

pre {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  line-height: 1.4;
}

pre code {
  background: none;
  color: inherit;
  padding: 0;
}

.badge.fs-6 {
  font-size: 0.875rem !important;
}

.card {
  transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1) !important;
}

@media (max-width: 768px) {
  .d-grid.gap-2 .btn {
    margin-bottom: 0.5rem;
  }
}
</style>

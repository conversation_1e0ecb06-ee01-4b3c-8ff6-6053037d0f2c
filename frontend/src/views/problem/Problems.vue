<template>
  <div class="problems">
    <div class="container py-4">
      <!-- 页面标题和操作 -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="d-flex justify-content-between align-items-center">
            <h2>
              <i class="bi bi-puzzle me-2"></i>
              题目列表
            </h2>
            <router-link
              v-if="isAuthenticated"
              class="btn btn-primary"
              @click="showCreateModal = true"
            >
              <i class="bi bi-plus-circle me-2"></i>
              创建题目
            </router-link>
            <button
              v-if="isAuthenticated"
              class="btn btn-success ms-2"
              @click="showCreateSetModal = true"
            >
              <i class="bi bi-folder-plus me-2"></i>
              创建题集
            </button>
          </div>
        </div>
      </div>

      <!-- 搜索和筛选 -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="card border-0 shadow-sm">
            <div class="card-body">
              <div class="row g-3">
                <div class="col-md-4">
                  <div class="input-group">
                    <span class="input-group-text">
                      <i class="bi bi-search"></i>
                    </span>
                    <input
                      v-model="searchForm.keyword"
                      type="text"
                      class="form-control"
                      placeholder="搜索题目标题..."
                      @keyup.enter="handleSearch"
                    >
                  </div>
                </div>
                <div class="col-md-2">
                  <select v-model="searchForm.difficulty" class="form-select">
                    <option value="">全部难度</option>
                    <option value="EASY">简单</option>
                    <option value="MEDIUM">中等</option>
                    <option value="HARD">困难</option>
                  </select>
                </div>
                <div class="col-md-3">
                  <input
                    v-model="searchForm.tags"
                    type="text"
                    class="form-control"
                    placeholder="标签筛选（如：数组,动态规划）"
                  >
                </div>
                <div class="col-md-3">
                  <div class="d-flex gap-2">
                    <button @click="handleSearch" class="btn btn-primary">
                      <i class="bi bi-search me-1"></i>
                      搜索
                    </button>
                    <button @click="handleReset" class="btn btn-outline-secondary">
                      <i class="bi bi-arrow-clockwise me-1"></i>
                      重置
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 题目列表 -->
      <div class="row">
        <div class="col-12">
          <div v-if="loading" class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2 text-muted">加载中...</p>
          </div>

          <div v-else-if="problems.length === 0" class="text-center py-5">
            <i class="bi bi-puzzle text-muted" style="font-size: 4rem;"></i>
            <h4 class="mt-3 text-muted">暂无题目</h4>
            <p class="text-muted">
              {{ searchForm.keyword ? '没有找到匹配的题目' : '还没有题目，创建第一个吧！' }}
            </p>
            <router-link
              v-if="isAuthenticated && !searchForm.keyword"
              class="btn btn-primary"
              @click="showCreateModal = true"
            >
              <i class="bi bi-plus-circle me-2"></i>
              创建题目
            </router-link>
          </div>

          <div v-else class="card border-0 shadow-sm">
            <div class="card-body p-0">
              <div class="table-responsive">
                <table class="table table-hover mb-0">
                  <thead class="table-light">
                    <tr>
                      <th style="width: 60px;">#</th>
                      <th>题目标题</th>
                      <th style="width: 100px;">难度</th>
                      <th style="width: 200px;">标签</th>
                      <th style="width: 120px;">创建者</th>
                      <th style="width: 120px;">创建时间</th>
                      <th style="width: 120px;">操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(problem, index) in problems" :key="problem.id">
                      <td class="text-muted">
                        {{ (pagination.current - 1) * pagination.size + index + 1 }}
                      </td>
                      <td>
                        <router-link
                          :to="`/problems/${problem.id}`"
                          class="text-decoration-none fw-medium"
                        >
                          {{ problem.title }}
                        </router-link>
                        <div v-if="problem.description" class="text-muted small text-truncate" style="max-width: 300px;">
                          {{ problem.description }}
                        </div>
                      </td>
                      <td>
                        <span
                          :class="getDifficultyClass(problem.difficulty)"
                          class="badge"
                        >
                          {{ getDifficultyText(problem.difficulty) }}
                        </span>
                      </td>
                      <td>
                        <div class="d-flex flex-wrap gap-1">
                          <span
                            v-for="tag in getTagList(problem.tags).slice(0, 3)"
                            :key="tag"
                            class="badge bg-light text-dark"
                          >
                            {{ tag }}
                          </span>
                          <span
                            v-if="getTagList(problem.tags).length > 3"
                            class="badge bg-light text-muted"
                          >
                            +{{ getTagList(problem.tags).length - 3 }}
                          </span>
                        </div>
                      </td>
                      <td>
                        <small class="text-muted">{{ problem.creatorName }}</small>
                      </td>
                      <td>
                        <small class="text-muted">{{ formatDate(problem.createdTime) }}</small>
                      </td>
                      <td>
                        <div class="d-flex gap-1">
                          <router-link
                            :to="`/problems/${problem.id}`"
                            class="btn btn-outline-primary btn-sm"
                          >
                            <i class="bi bi-eye"></i>
                          </router-link>
                          <button
                            v-if="canEdit(problem)"
                            @click="editProblem(problem)"
                            class="btn btn-outline-secondary btn-sm"
                          >
                            <i class="bi bi-pencil"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <!-- 分页 -->
          <div v-if="pagination.total > 0" class="row mt-4">
            <div class="col-12">
              <nav aria-label="题目分页">
                <ul class="pagination justify-content-center">
                  <li class="page-item" :class="{ disabled: pagination.current <= 1 }">
                    <button
                      class="page-link"
                      @click="changePage(pagination.current - 1)"
                      :disabled="pagination.current <= 1"
                    >
                      上一页
                    </button>
                  </li>

                  <li
                    v-for="page in visiblePages"
                    :key="page"
                    class="page-item"
                    :class="{ active: page === pagination.current }"
                  >
                    <button class="page-link" @click="changePage(page)">
                      {{ page }}
                    </button>
                  </li>

                  <li class="page-item" :class="{ disabled: pagination.current >= pagination.pages }">
                    <button
                      class="page-link"
                      @click="changePage(pagination.current + 1)"
                      :disabled="pagination.current >= pagination.pages"
                    >
                      下一页
                    </button>
                  </li>
                </ul>
              </nav>

              <div class="text-center text-muted">
                共 {{ pagination.total }} 个题目，第 {{ pagination.current }} / {{ pagination.pages }} 页
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <CreateProblemModal v-if="showCreateModal" @close="showCreateModal = false" @created="handleCreated" />
    <CreateProblemSetModal v-if="showCreateSetModal" @close="showCreateSetModal = false" @created="handleSetCreated" />
  </div>
</template>

<script>
import { reactive, computed, onMounted, ref } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { showError } from '@/utils/message'
import CreateProblemModal from '@/components/problem/CreateProblemModal.vue'
import CreateProblemSetModal from '@/components/problem/CreateProblemSetModal.vue'

export default {
  name: 'Problems',
  components: {
    CreateProblemModal,
    CreateProblemSetModal
  },
  setup() {
    const store = useStore()
    const router = useRouter()

    const searchForm = reactive({
      keyword: '',
      difficulty: '',
      tags: ''
    })

    const isAuthenticated = computed(() => store.getters['auth/isAuthenticated'])
    const user = computed(() => store.getters['auth/user'])
    const problems = computed(() => store.getters['problem/problems'])
    const loading = computed(() => store.getters['problem/loading'])
    const pagination = computed(() => store.getters['problem/pagination'])

    const visiblePages = computed(() => {
      const current = pagination.value.current
      const total = pagination.value.pages
      const delta = 2
      const range = []
      const rangeWithDots = []

      for (let i = Math.max(2, current - delta); i <= Math.min(total - 1, current + delta); i++) {
        range.push(i)
      }

      if (current - delta > 2) {
        rangeWithDots.push(1, '...')
      } else {
        rangeWithDots.push(1)
      }

      rangeWithDots.push(...range)

      if (current + delta < total - 1) {
        rangeWithDots.push('...', total)
      } else {
        rangeWithDots.push(total)
      }

      return rangeWithDots.filter((item, index, arr) => arr.indexOf(item) === index && item !== 1 || index === 0)
    })

    const formatDate = (dateString) => {
      if (!dateString) return '-'
      return new Date(dateString).toLocaleDateString('zh-CN')
    }

    const getDifficultyClass = (difficulty) => {
      const classes = {
        'EASY': 'bg-success',
        'MEDIUM': 'bg-warning',
        'HARD': 'bg-danger'
      }
      return classes[difficulty] || 'bg-secondary'
    }

    const getDifficultyText = (difficulty) => {
      const texts = {
        'EASY': '简单',
        'MEDIUM': '中等',
        'HARD': '困难'
      }
      return texts[difficulty] || difficulty
    }

    const getTagList = (tags) => {
      if (!tags) return []
      return tags.split(',').map(tag => tag.trim()).filter(tag => tag)
    }

    const canEdit = (problem) => {
      if (!user.value) return false
      return user.value.role === 'ADMIN' || problem.creatorId === user.value.id
    }

    const fetchProblems = async (params = {}) => {
      try {
        const searchParams = {
          current: pagination.value.current,
          size: pagination.value.size,
          ...params
        }

        if (searchForm.keyword) {
          searchParams.keyword = searchForm.keyword
        }

        if (searchForm.difficulty) {
          searchParams.difficulty = searchForm.difficulty
        }

        if (searchForm.tags) {
          searchParams.tags = searchForm.tags
        }

        await store.dispatch('problem/fetchProblems', searchParams)
      } catch (error) {
        showError(error.message || '获取题目列表失败')
      }
    }

    const handleSearch = () => {
      fetchProblems({ current: 1 })
    }

    const handleReset = () => {
      searchForm.keyword = ''
      searchForm.difficulty = ''
      searchForm.tags = ''
      fetchProblems({ current: 1 })
    }

    const changePage = (page) => {
      if (page < 1 || page > pagination.value.pages) return
      fetchProblems({ current: page })
    }

    const editProblem = (problem) => {
      router.push(`/problems/${problem.id}/edit`)
    }

    const showCreateModal = ref(false)
    const showCreateSetModal = ref(false)

    const handleCreated = () => {
      showCreateModal.value = false
      fetchProblems()
    }

    const handleSetCreated = () => {
      showCreateSetModal.value = false
      // 可选：刷新题集列表
    }

    onMounted(() => {
      fetchProblems()
    })

    return {
      searchForm,
      isAuthenticated,
      user,
      problems,
      loading,
      pagination,
      visiblePages,
      formatDate,
      getDifficultyClass,
      getDifficultyText,
      getTagList,
      canEdit,
      handleSearch,
      handleReset,
      changePage,
      editProblem,
      showCreateModal,
      handleCreated,
      showCreateSetModal,
      handleSetCreated
    }
  }
}
</script>

<style scoped>
.table th {
  border-top: none;
  font-weight: 600;
  color: #495057;
}

.table td {
  vertical-align: middle;
}

.pagination .page-link {
  border-radius: 0.375rem;
  margin: 0 2px;
  border: 1px solid #dee2e6;
}

.pagination .page-item.active .page-link {
  background-color: #007bff;
  border-color: #007bff;
}

.badge {
  font-size: 0.75em;
}

.text-truncate {
  max-width: 300px;
}

@media (max-width: 768px) {
  .d-flex.gap-2 {
    flex-direction: column;
  }

  .d-flex.gap-2 .btn {
    margin-bottom: 0.5rem;
  }

  .table-responsive {
    font-size: 0.875rem;
  }
}
</style>

/* Bootstrap 5 Style - 全局样式 */
:root {
  --bs-primary: #0d6efd;
  --bs-primary-hover: #0b5ed7;
  --bs-secondary: #6c757d;
  --bs-success: #198754;
  --bs-danger: #dc3545;
  --bs-warning: #ffc107;
  --bs-info: #0dcaf0;
  --bs-light: #f8f9fa;
  --bs-dark: #212529;
  --bs-white: #fff;
  --bs-gray: #adb5bd;
  --bs-border: #dee2e6;
  --bs-link: #0d6efd;
  --bs-link-hover: #0a58ca;
  --bs-btn: #0d6efd;
  --bs-btn-hover: #0b5ed7;
  --bs-btn-text: #fff;
  --bs-shadow: 0 0.5rem 1rem rgba(0,0,0,0.05);
  --bs-radius: 0.375rem;
  --bs-radius-sm: 0.25rem;
  --bs-radius-lg: 0.5rem;
  --bs-transition: all 0.15s ease-in-out;
}

body {
  font-family: system-ui, -apple-system, Segoe UI, Roboto, Arial, sans-serif;
  background: var(--bs-light);
  color: var(--bs-dark);
  line-height: 1.7;
  font-size: 16px;
}

/* 链接 */
a {
  color: var(--bs-link);
  text-decoration: underline;
  transition: color 0.15s;
}
a:hover {
  color: var(--bs-link-hover);
}

/* 卡片 */
.card {
  background: var(--bs-white);
  border-radius: var(--bs-radius);
  box-shadow: var(--bs-shadow);
  border: 1px solid var(--bs-border);
  padding: 1.5rem 1.5rem 1.25rem 1.5rem;
  transition: var(--bs-transition);
}
.card-header {
  background: none;
  border-bottom: 1px solid var(--bs-border);
  font-weight: 600;
  font-size: 1.08rem;
  color: var(--bs-dark);
  border-radius: var(--bs-radius) var(--bs-radius) 0 0;
  padding-bottom: 0.7rem;
}
.card-footer {
  background: none;
  border-top: 1px solid var(--bs-border);
  border-radius: 0 0 var(--bs-radius) var(--bs-radius);
}

/* 按钮 */
.btn {
  border-radius: var(--bs-radius);
  font-weight: 600;
  font-size: 1rem;
  padding: 0.5rem 1.25rem;
  border: 1px solid var(--bs-btn);
  background: var(--bs-btn);
  color: var(--bs-btn-text);
  box-shadow: none;
  transition: var(--bs-transition);
  cursor: pointer;
}
.btn:hover, .btn:focus {
  background: var(--bs-btn-hover);
  border-color: var(--bs-btn-hover);
  color: var(--bs-btn-text);
}
.btn-outline {
  background: #fff;
  color: var(--bs-btn);
  border: 1.5px solid var(--bs-btn);
}
.btn-outline:hover {
  background: var(--bs-btn);
  color: #fff;
}
.btn-link {
  background: none;
  color: var(--bs-link);
  border: none;
  box-shadow: none;
  padding: 0 6px;
  font-weight: 500;
  text-decoration: underline;
}
.btn-link:hover {
  color: var(--bs-link-hover);
}

/* 表单 */
.form-control {
  border-radius: var(--bs-radius);
  border: 1px solid var(--bs-border);
  background: #fff;
  font-size: 1rem;
  padding: 0.5rem 1rem;
  color: var(--bs-dark);
  outline: none;
  box-shadow: none;
  transition: border-color 0.15s, box-shadow 0.15s;
}
.form-control:focus {
  border-color: var(--bs-btn);
  box-shadow: 0 0 0 0.2rem rgba(13,110,253,0.15);
}
.form-control::placeholder {
  color: var(--bs-gray);
  font-weight: 400;
}
.is-invalid {
  border-color: var(--bs-danger) !important;
  background: #fff0f0 !important;
}
.invalid-feedback {
  color: var(--bs-danger);
  font-size: 14px;
  margin-top: 2px;
}

/* 导航栏 */
.navbar-container {
  background: #fff;
  color: var(--bs-dark);
  padding: 1rem 0;
  border-bottom: 1px solid var(--bs-border);
  box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.03);
}
.navbar-brand .logo-img {
  height: 32px;
  margin-right: 8px;
  vertical-align: middle;
}
.nav-links .nav-item {
  color: var(--bs-dark);
  font-weight: 600;
  font-size: 1rem;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: var(--bs-radius);
  transition: background 0.15s;
}
.nav-links .nav-item:hover, .nav-links .nav-item.router-link-exact-active {
  background: #e7f1ff;
  color: var(--bs-btn);
}
.user-avatar {
  width: 28px;
  height: 28px;
  object-fit: cover;
  border-radius: 50%;
  border: 1.5px solid var(--bs-border);
}
.dropdown-menu {
  background: #fff;
  border-radius: var(--bs-radius);
  box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.08);
  border: 1px solid var(--bs-border);
  font-size: 1rem;
}
.dropdown-item {
  color: var(--bs-dark);
  padding: 0.5rem 1.25rem;
  border-radius: var(--bs-radius);
}
.dropdown-item:hover {
  background: #e7f1ff;
  color: var(--bs-btn);
}

/* 其他通用 */
::-webkit-scrollbar {
  width: 8px;
  background: var(--bs-light);
}
::-webkit-scrollbar-thumb {
  background: var(--bs-border);
  border-radius: 8px;
}

@media (max-width: 700px) {
  .card {
    padding: 1rem 0.7rem;
    border-radius: var(--bs-radius);
  }
  .navbar-container {
    padding: 0.5rem 0;
  }
}

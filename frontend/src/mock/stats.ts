import type MockAdapter from 'axios-mock-adapter'

export function mockStats(mock: MockAdapter) {
  // 暂时禁用，以排查启动问题
}

// 模拟获取用户统计
mock.onGet('/stats/user').reply(200, {
  code: 200,
  message: '成功',
  data: {
    onlineUsers: 123,
    totalUsers: 4567,
  }
})

// 模拟获取平台统计
mock.onGet('/stats/platform').reply(200, {
  code: 200,
  message: '成功',
  data: {
    totalProblemSets: 89,
    totalProblems: 1234,
    totalSubmissions: 56789,
  }
}) 
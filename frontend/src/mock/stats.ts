import type MockAdapter from 'axios-mock-adapter'

export function mockStats(mock: MockAdapter) {
  // 模拟获取公开用户统计
  mock.onGet('/stats/public/users').reply(200, {
    code: 200,
    message: '成功',
    data: {
      onlineUsers: 123,
      totalUsers: 4567,
      activeUsers: 890,
      newUsersToday: 12
    }
  })

  // 模拟获取公开平台统计
  mock.onGet('/stats/public/platform').reply(200, {
    code: 200,
    message: '成功',
    data: {
      totalProblemSets: 89,
      totalProblems: 1234,
      totalSubmissions: 56789,
      totalSolutions: 23456
    }
  })

  // 模拟获取用户个人统计
  mock.onGet('/stats/user').reply(200, {
    code: 200,
    message: '成功',
    data: {
      solvedProblems: 45,
      totalSubmissions: 123,
      acceptedSubmissions: 67,
      rank: 156,
      points: 2340
    }
  })

  // 模拟获取题目统计
  mock.onGet(/\/stats\/problems\/\d+/).reply(config => {
    const id = config.url?.match(/\/stats\/problems\/(\d+)/)?.[1]
    return [200, {
      code: 200,
      message: '成功',
      data: {
        problemId: Number(id),
        totalSubmissions: Math.floor(Math.random() * 1000) + 100,
        acceptedSubmissions: Math.floor(Math.random() * 500) + 50,
        acceptanceRate: (Math.random() * 0.5 + 0.3).toFixed(2),
        difficulty: ['EASY', 'MEDIUM', 'HARD'][Math.floor(Math.random() * 3)]
      }
    }]
  })

  // 模拟获取题集统计
  mock.onGet(/\/stats\/problemsets\/\d+/).reply(config => {
    const id = config.url?.match(/\/stats\/problemsets\/(\d+)/)?.[1]
    return [200, {
      code: 200,
      message: '成功',
      data: {
        problemSetId: Number(id),
        totalProblems: Math.floor(Math.random() * 50) + 10,
        completedUsers: Math.floor(Math.random() * 200) + 20,
        averageRating: (Math.random() * 2 + 3).toFixed(1),
        difficulty: ['EASY', 'MEDIUM', 'HARD'][Math.floor(Math.random() * 3)]
      }
    }]
  })
}
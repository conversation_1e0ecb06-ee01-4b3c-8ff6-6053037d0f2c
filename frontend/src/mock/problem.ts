import type MockAdapter from 'axios-mock-adapter'

const problemTitles = [
  '两数之和', '两数相加', '无重复字符的最长子串', '寻找两个正序数组的中位数', '最长回文子串',
  'Z 字形变换', '整数反转', '字符串转换整数', '回文数', '正则表达式匹配',
  '盛最多水的容器', '整数转罗马数字', '罗马数字转整数', '最长公共前缀', '三数之和',
  '最接近的三数之和', '电话号码的字母组合', '四数之和', '删除链表的倒数第N个节点', '有效的括号',
  '合并两个有序链表', '括号生成', '合并K个升序链表', '两两交换链表中的节点', 'K个一组翻转链表'
]

const problemDescriptions = [
  '给定一个整数数组 nums 和一个整数目标值 target，请你在该数组中找出和为目标值 target 的那两个整数，并返回它们的数组下标。',
  '给你两个非空的链表，表示两个非负的整数。它们每位数字都是按照逆序的方式存储的，并且每个节点只能存储一位数字。',
  '给定一个字符串 s ，请你找出其中不含有重复字符的最长子串的长度。',
  '给定两个大小分别为 m 和 n 的正序（从小到大）数组 nums1 和 nums2。请你找出并返回这两个正序数组的中位数。',
  '给你一个字符串 s，找到 s 中最长的回文子串。'
]

const tagsList = [
  '数组,哈希表', '链表,数学,递归', '哈希表,字符串,滑动窗口', '数组,二分查找,分治',
  '字符串,动态规划', '字符串', '数学', '字符串,数学', '数学', '字符串,动态规划,回溯',
  '贪心,数组,双指针', '哈希表,数学,字符串', '哈希表,数学,字符串', '字符串', '数组,双指针,排序',
  '数组,双指针,排序', '哈希表,字符串,回溯', '数组,双指针,排序', '链表,双指针', '栈,字符串',
  '链表,递归', '字符串,动态规划,回溯', '链表,分治,堆', '链表,递归', '链表,递归'
]

const problems = Array.from({ length: 50 }, (_, i) => ({
  id: i + 1,
  title: problemTitles[i % problemTitles.length],
  description: problemDescriptions[i % problemDescriptions.length],
  inputFormat: '第一行包含整数 n，表示数组长度。\n第二行包含 n 个整数，表示数组元素。',
  outputFormat: '输出一行，包含两个整数，表示目标和的两个数的下标。',
  sampleInput: '4\n2 7 11 15\n9',
  sampleOutput: '0 1',
  hint: '可以使用哈希表来优化时间复杂度。',
  difficulty: ['EASY', 'MEDIUM', 'HARD'][i % 3],
  tags: tagsList[i % tagsList.length],
  timeLimit: 1000,
  memoryLimit: 256,
  source: ['LeetCode', '牛客网', '洛谷', 'Codeforces'][i % 4],
  creatorId: 1,
  creatorName: 'admin',
  creatorNickname: '管理员',
  status: 'PUBLISHED',
  viewCount: Math.floor(Math.random() * 1000) + 100,
  likeCount: Math.floor(Math.random() * 50) + 10,
  submissionCount: Math.floor(Math.random() * 200) + 50,
  acceptedCount: Math.floor(Math.random() * 100) + 20,
  createdTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
  updatedTime: new Date().toISOString(),
}));

export function mockProblem(mock: MockAdapter) {
  // Get problem list with pagination
  mock.onGet('/problems').reply(config => {
    const params = config.params || {}
    const current = parseInt(params.current) || 1
    const size = parseInt(params.size) || 10
    const keyword = params.keyword || ''
    const difficulty = params.difficulty || ''
    const tags = params.tags || ''

    let filteredProblems = [...problems]

    // 关键词搜索
    if (keyword) {
      filteredProblems = filteredProblems.filter(problem =>
        problem.title.toLowerCase().includes(keyword.toLowerCase()) ||
        problem.description.toLowerCase().includes(keyword.toLowerCase()) ||
        problem.tags.toLowerCase().includes(keyword.toLowerCase())
      )
    }

    // 难度筛选
    if (difficulty) {
      filteredProblems = filteredProblems.filter(problem => problem.difficulty === difficulty)
    }

    // 标签筛选
    if (tags) {
      filteredProblems = filteredProblems.filter(problem =>
        problem.tags.toLowerCase().includes(tags.toLowerCase())
      )
    }

    const total = filteredProblems.length
    const pages = Math.ceil(total / size)
    const start = (current - 1) * size
    const end = start + size
    const records = filteredProblems.slice(start, end)

    return [200, {
      code: 200,
      message: 'success',
      data: {
        records,
        total,
        size,
        current,
        pages
      }
    }]
  });

  // Get single problem
  mock.onGet(/\/problems\/\d+/).reply(config => {
    const id = config.url?.match(/\/problems\/(\d+)/)?.[1]
    const problem = problems.find(p => p.id === Number(id))
    if (problem) {
      return [200, {
        code: 200,
        message: 'success',
        data: problem,
      }]
    }
    return [404, { code: 404, message: '题目不存在', data: null }]
  })

  // Create problem
  mock.onPost('/problems').reply(config => {
    const data = JSON.parse(config.data)
    const newProblem = {
      id: problems.length + 1,
      ...data,
      creatorId: 1,
      creatorName: 'admin',
      creatorNickname: '管理员',
      status: 'PUBLISHED',
      viewCount: 0,
      likeCount: 0,
      submissionCount: 0,
      acceptedCount: 0,
      createdTime: new Date().toISOString(),
      updatedTime: new Date().toISOString(),
    }
    problems.push(newProblem)
    return [200, {
      code: 200,
      message: 'success',
      data: newProblem,
    }];
  });

  // Search problems
  mock.onGet('/problems/search').reply(config => {
    const params = config.params || {}
    const keyword = params.keyword || ''
    const difficulty = params.difficulty || ''
    const tags = params.tags || ''

    let filteredProblems = [...problems]

    if (keyword) {
      filteredProblems = filteredProblems.filter(problem =>
        problem.title.toLowerCase().includes(keyword.toLowerCase()) ||
        problem.description.toLowerCase().includes(keyword.toLowerCase())
      )
    }

    if (difficulty) {
      filteredProblems = filteredProblems.filter(problem => problem.difficulty === difficulty)
    }

    if (tags) {
      filteredProblems = filteredProblems.filter(problem =>
        problem.tags.toLowerCase().includes(tags.toLowerCase())
      )
    }

    return [200, {
      code: 200,
      message: 'success',
      data: filteredProblems.slice(0, 20) // 限制搜索结果数量
    }]
  });

  // Increment view count
  mock.onPost(/\/problems\/\d+\/view/).reply(config => {
    const id = config.url?.match(/\/problems\/(\d+)\/view/)?.[1]
    const problem = problems.find(p => p.id === Number(id))
    if (problem) {
      problem.viewCount++
      return [200, {
        code: 200,
        message: 'success',
        data: null,
      }]
    }
    return [404, { code: 404, message: '题目不存在', data: null }]
  })
}
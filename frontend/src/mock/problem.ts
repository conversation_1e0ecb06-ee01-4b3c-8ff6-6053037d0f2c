import type MockAdapter from 'axios-mock-adapter'

// LeetCode 风格的经典算法题目数据
const leetcodeProblems = [
  {
    id: 1,
    title: '两数之和',
    description: `给定一个整数数组 nums 和一个整数目标值 target，请你在该数组中找出和为目标值 target 的那两个整数，并返回它们的数组下标。

你可以假设每种输入只会对应一个答案。但是，数组中同一个元素在答案里不能重复出现。

你可以按任意顺序返回答案。`,
    inputFormat: `nums: 整数数组
target: 整数目标值`,
    outputFormat: `返回两个整数的数组下标`,
    sampleInput: `nums = [2,7,11,15], target = 9`,
    sampleOutput: `[0,1]`,
    explanation: `因为 nums[0] + nums[1] == 9 ，返回 [0, 1] 。`,
    hint: `你可以想出一个时间复杂度小于 O(n²) 的算法吗？`,
    difficulty: 'EASY',
    tags: '数组,哈希表',
    timeLimit: 1000,
    memoryLimit: 256,
    acceptanceRate: 0.52
  },
  {
    id: 2,
    title: '两数相加',
    description: `给你两个非空的链表，表示两个非负的整数。它们每位数字都是按照逆序的方式存储的，并且每个节点只能存储一位数字。

请你将两个数相加，并以相同形式返回一个表示和的链表。

你可以假设除了数字 0 之外，这两个数都不会以 0 开头。`,
    inputFormat: `l1: 链表，表示第一个数
l2: 链表，表示第二个数`,
    outputFormat: `返回表示和的链表`,
    sampleInput: `l1 = [2,4,3], l2 = [5,6,4]`,
    sampleOutput: `[7,0,8]`,
    explanation: `342 + 465 = 807`,
    hint: `注意处理进位和不同长度的链表。`,
    difficulty: 'MEDIUM',
    tags: '链表,数学,递归',
    timeLimit: 1000,
    memoryLimit: 256,
    acceptanceRate: 0.39
  },
  {
    id: 3,
    title: '无重复字符的最长子串',
    description: `给定一个字符串 s ，请你找出其中不含有重复字符的最长子串的长度。`,
    inputFormat: `s: 字符串`,
    outputFormat: `返回最长子串的长度`,
    sampleInput: `s = "abcabcbb"`,
    sampleOutput: `3`,
    explanation: `因为无重复字符的最长子串是 "abc"，所以其长度为 3。`,
    hint: `你能想出时间复杂度为 O(n) 的解决方案吗？`,
    difficulty: 'MEDIUM',
    tags: '哈希表,字符串,滑动窗口',
    timeLimit: 1000,
    memoryLimit: 256,
    acceptanceRate: 0.36
  },
  {
    id: 4,
    title: '寻找两个正序数组的中位数',
    description: `给定两个大小分别为 m 和 n 的正序（从小到大）数组 nums1 和 nums2。请你找出并返回这两个正序数组的中位数。

算法的时间复杂度应该为 O(log (m+n)) 。`,
    inputFormat: `nums1: 正序数组
nums2: 正序数组`,
    outputFormat: `返回中位数`,
    sampleInput: `nums1 = [1,3], nums2 = [2]`,
    sampleOutput: `2.00000`,
    explanation: `合并数组 = [1,2,3] ，中位数 2`,
    hint: `你能想出一个时间复杂度为 O(log (m+n)) 的算法吗？`,
    difficulty: 'HARD',
    tags: '数组,二分查找,分治',
    timeLimit: 1000,
    memoryLimit: 256,
    acceptanceRate: 0.37
  },
  {
    id: 5,
    title: '最长回文子串',
    description: `给你一个字符串 s，找到 s 中最长的回文子串。

如果字符串的反序与原始字符串相同，则该字符串称为回文字符串。`,
    inputFormat: `s: 字符串`,
    outputFormat: `返回最长的回文子串`,
    sampleInput: `s = "babad"`,
    sampleOutput: `"bab"`,
    explanation: `"aba" 同样是符合题意的答案。`,
    hint: `你能在 O(n) 时间复杂度内解决这个问题吗？`,
    difficulty: 'MEDIUM',
    tags: '字符串,动态规划',
    timeLimit: 1000,
    memoryLimit: 256,
    acceptanceRate: 0.33
  },
  {
    id: 15,
    title: '三数之和',
    description: `给你一个整数数组 nums ，判断是否存在三元组 [nums[i], nums[j], nums[k]] 满足 i != j、i != k 且 j != k ，同时还满足 nums[i] + nums[j] + nums[k] == 0 。

请你返回所有和为 0 且不重复的三元组。

注意：答案中不可以包含重复的三元组。`,
    inputFormat: `nums: 整数数组`,
    outputFormat: `返回所有和为 0 的三元组`,
    sampleInput: `nums = [-1,0,1,2,-1,-4]`,
    sampleOutput: `[[-1,-1,2],[-1,0,1]]`,
    explanation: `nums[0] + nums[1] + nums[2] = (-1) + 0 + 1 = 0 。
nums[1] + nums[2] + nums[4] = 0 + 1 + (-1) = 0 。
nums[0] + nums[3] + nums[4] = (-1) + 2 + (-1) = 0 。
不同的三元组是 [-1,0,1] 和 [-1,-1,2] 。`,
    hint: `排序 + 双指针`,
    difficulty: 'MEDIUM',
    tags: '数组,双指针,排序',
    timeLimit: 1000,
    memoryLimit: 256,
    acceptanceRate: 0.33
  },
  {
    id: 20,
    title: '有效的括号',
    description: `给定一个只包括 '('，')'，'{'，'}'，'['，']' 的字符串 s ，判断字符串是否有效。

有效字符串需满足：
1. 左括号必须用相同类型的右括号闭合。
2. 左括号必须以正确的顺序闭合。
3. 每个右括号都有一个对应的相同类型的左括号。`,
    inputFormat: `s: 只包含括号的字符串`,
    outputFormat: `返回布尔值`,
    sampleInput: `s = "()[]{}"`,
    sampleOutput: `true`,
    explanation: `所有括号都正确匹配。`,
    hint: `使用栈来匹配括号。`,
    difficulty: 'EASY',
    tags: '栈,字符串',
    timeLimit: 1000,
    memoryLimit: 256,
    acceptanceRate: 0.40
  },
  {
    id: 21,
    title: '合并两个有序链表',
    description: `将两个升序链表合并为一个新的升序链表并返回。新链表是通过拼接给定的两个链表的所有节点组成的。`,
    inputFormat: `list1: 升序链表
list2: 升序链表`,
    outputFormat: `返回合并后的升序链表`,
    sampleInput: `list1 = [1,2,4], list2 = [1,3,4]`,
    sampleOutput: `[1,1,2,3,4,4]`,
    explanation: `合并后的链表为升序。`,
    hint: `使用递归或迭代方法。`,
    difficulty: 'EASY',
    tags: '链表,递归',
    timeLimit: 1000,
    memoryLimit: 256,
    acceptanceRate: 0.62
  },
  {
    id: 53,
    title: '最大子数组和',
    description: `给你一个整数数组 nums ，请你找出一个具有最大和的连续子数组（子数组最少包含一个元素），返回其最大和。

子数组是数组中的一个连续部分。`,
    inputFormat: `nums: 整数数组`,
    outputFormat: `返回最大子数组和`,
    sampleInput: `nums = [-2,1,-3,4,-1,2,1,-5,4]`,
    sampleOutput: `6`,
    explanation: `连续子数组 [4,-1,2,1] 的和最大，为 6 。`,
    hint: `动态规划：Kadane算法`,
    difficulty: 'MEDIUM',
    tags: '数组,分治,动态规划',
    timeLimit: 1000,
    memoryLimit: 256,
    acceptanceRate: 0.50
  },
  {
    id: 70,
    title: '爬楼梯',
    description: `假设你正在爬楼梯。需要 n 阶你才能到达楼顶。

每次你可以爬 1 或 2 个台阶。你有多少种不同的方法可以爬到楼顶呢？`,
    inputFormat: `n: 正整数，表示楼梯阶数`,
    outputFormat: `返回爬楼梯的方法数`,
    sampleInput: `n = 3`,
    sampleOutput: `3`,
    explanation: `有三种方法可以爬到三阶楼顶：
1. 1 阶 + 1 阶 + 1 阶
2. 1 阶 + 2 阶
3. 2 阶 + 1 阶`,
    hint: `这是一个斐波那契数列问题。`,
    difficulty: 'EASY',
    tags: '记忆化搜索,数学,动态规划',
    timeLimit: 1000,
    memoryLimit: 256,
    acceptanceRate: 0.52
  },
  {
    id: 121,
    title: '买卖股票的最佳时机',
    description: `给定一个数组 prices ，它的第 i 个元素 prices[i] 表示一支给定股票第 i 天的价格。

你只能选择某一天买入这只股票，并选择在未来的某一天卖出该股票。设计一个算法来计算你所能获取的最大利润。

返回你可以从这笔交易中获取的最大利润。如果你不能获取任何利润，返回 0 。`,
    inputFormat: `prices: 整数数组，表示股票价格`,
    outputFormat: `返回最大利润`,
    sampleInput: `prices = [7,1,5,3,6,4]`,
    sampleOutput: `5`,
    explanation: `在第 2 天（股票价格 = 1）的时候买入，在第 5 天（股票价格 = 6）的时候卖出，最大利润 = 6-1 = 5 。`,
    hint: `记录历史最低价格。`,
    difficulty: 'EASY',
    tags: '数组,动态规划',
    timeLimit: 1000,
    memoryLimit: 256,
    acceptanceRate: 0.56
  },
  {
    id: 141,
    title: '环形链表',
    description: `给你一个链表的头节点 head ，判断链表中是否有环。

如果链表中有某个节点，可以通过连续跟踪 next 指针再次到达，则链表中存在环。 为了表示给定链表中的环，评测系统内部使用整数 pos 来表示链表尾连接到链表中的位置（索引从 0 开始）。注意：pos 不作为参数进行传递 。仅仅是为了标识链表的实际情况。

如果链表中存在环 ，则返回 true 。 否则，返回 false 。`,
    inputFormat: `head: 链表头节点`,
    outputFormat: `返回布尔值`,
    sampleInput: `head = [3,2,0,-4], pos = 1`,
    sampleOutput: `true`,
    explanation: `链表中有一个环，其尾部连接到第二个节点。`,
    hint: `使用快慢指针（Floyd判圈算法）。`,
    difficulty: 'EASY',
    tags: '哈希表,链表,双指针',
    timeLimit: 1000,
    memoryLimit: 256,
    acceptanceRate: 0.51
  },
  {
    id: 200,
    title: '岛屿数量',
    description: `给你一个由 '1'（陆地）和 '0'（水）组成的的二维网格，请你计算网格中岛屿的数量。

岛屿总是被水包围，并且每座岛屿只能由水平方向和/或竖直方向上相邻的陆地连接形成。

此外，你可以假设该网格的四条边均被水包围。`,
    inputFormat: `grid: 二维字符数组`,
    outputFormat: `返回岛屿数量`,
    sampleInput: `grid = [
  ["1","1","1","1","0"],
  ["1","1","0","1","0"],
  ["1","1","0","0","0"],
  ["0","0","0","0","0"]
]`,
    sampleOutput: `1`,
    explanation: `只有一个岛屿。`,
    hint: `使用深度优先搜索（DFS）或广度优先搜索（BFS）。`,
    difficulty: 'MEDIUM',
    tags: '深度优先搜索,广度优先搜索,并查集,数组,矩阵',
    timeLimit: 1000,
    memoryLimit: 256,
    acceptanceRate: 0.58
  },
  {
    id: 206,
    title: '反转链表',
    description: `给你单链表的头节点 head ，请你反转链表，并返回反转后的链表。`,
    inputFormat: `head: 链表头节点`,
    outputFormat: `返回反转后的链表头节点`,
    sampleInput: `head = [1,2,3,4,5]`,
    sampleOutput: `[5,4,3,2,1]`,
    explanation: `链表被反转。`,
    hint: `可以使用迭代或递归方法。`,
    difficulty: 'EASY',
    tags: '递归,链表',
    timeLimit: 1000,
    memoryLimit: 256,
    acceptanceRate: 0.73
  }
]

// 生成完整的题目列表，包含 LeetCode 经典题目和扩展题目
const problems = leetcodeProblems.map(problem => {
  const submissionCount = Math.floor(Math.random() * 10000) + 1000
  const acceptedCount = Math.floor(submissionCount * problem.acceptanceRate)

  return {
    ...problem,
    source: 'LeetCode',
    creatorId: 1,
    creatorName: 'admin',
    creatorNickname: '管理员',
    status: 'PUBLISHED',
    viewCount: Math.floor(Math.random() * 50000) + 5000,
    likeCount: Math.floor(Math.random() * 1000) + 100,
    submissionCount,
    acceptedCount,
    createdTime: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
    updatedTime: new Date().toISOString(),
  }
})

// 添加更多扩展题目以达到50个
const additionalProblems = Array.from({ length: 50 - leetcodeProblems.length }, (_, i) => {
  const baseId = leetcodeProblems.length + i + 1
  const difficulties = ['EASY', 'MEDIUM', 'HARD']
  const difficulty = difficulties[i % 3]
  const acceptanceRates = { 'EASY': 0.6, 'MEDIUM': 0.4, 'HARD': 0.25 }
  const submissionCount = Math.floor(Math.random() * 5000) + 500
  const acceptedCount = Math.floor(submissionCount * acceptanceRates[difficulty])

  const topics = [
    { name: '数组操作', tags: '数组,排序', desc: '数组相关的基础操作和算法' },
    { name: '字符串处理', tags: '字符串,哈希表', desc: '字符串匹配、处理和转换' },
    { name: '树的遍历', tags: '树,深度优先搜索,广度优先搜索', desc: '二叉树的各种遍历方法' },
    { name: '图算法', tags: '图,深度优先搜索,广度优先搜索', desc: '图的遍历和最短路径算法' },
    { name: '动态规划', tags: '动态规划,记忆化搜索', desc: '使用动态规划解决优化问题' },
    { name: '贪心算法', tags: '贪心,排序', desc: '贪心策略解决最优化问题' },
    { name: '二分查找', tags: '数组,二分查找', desc: '在有序数组中快速查找元素' },
    { name: '双指针', tags: '数组,双指针', desc: '使用双指针技巧解决数组问题' }
  ]

  const topic = topics[i % topics.length]

  return {
    id: baseId,
    title: `${topic.name} ${Math.floor(i / topics.length) + 1}`,
    description: `${topic.desc}。这是一个${difficulty === 'EASY' ? '简单' : difficulty === 'MEDIUM' ? '中等' : '困难'}难度的算法题目，需要运用相关的数据结构和算法知识来解决。`,
    inputFormat: '第一行包含测试数据的基本信息。\n后续行包含具体的输入数据。',
    outputFormat: '输出符合题目要求的结果。',
    sampleInput: '示例输入数据',
    sampleOutput: '示例输出结果',
    explanation: '详细的解题思路和步骤说明。',
    hint: `考虑使用${topic.name}的相关算法和数据结构。`,
    difficulty,
    tags: topic.tags,
    timeLimit: 1000,
    memoryLimit: 256,
    acceptanceRate: acceptanceRates[difficulty],
    source: 'LeetCode',
    creatorId: 1,
    creatorName: 'admin',
    creatorNickname: '管理员',
    status: 'PUBLISHED',
    viewCount: Math.floor(Math.random() * 10000) + 1000,
    likeCount: Math.floor(Math.random() * 500) + 50,
    submissionCount,
    acceptedCount,
    createdTime: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
    updatedTime: new Date().toISOString(),
  }
})

// 合并所有题目
const allProblems = [...problems, ...additionalProblems];

export function mockProblem(mock: MockAdapter) {
  // Get problem list with pagination
  mock.onGet('/problems').reply(config => {
    const params = config.params || {}
    const current = parseInt(params.current) || 1
    const size = parseInt(params.size) || 10
    const keyword = params.keyword || ''
    const difficulty = params.difficulty || ''
    const tags = params.tags || ''

    let filteredProblems = [...allProblems]

    // 关键词搜索
    if (keyword) {
      filteredProblems = filteredProblems.filter(problem =>
        problem.title.toLowerCase().includes(keyword.toLowerCase()) ||
        problem.description.toLowerCase().includes(keyword.toLowerCase()) ||
        problem.tags.toLowerCase().includes(keyword.toLowerCase())
      )
    }

    // 难度筛选
    if (difficulty) {
      filteredProblems = filteredProblems.filter(problem => problem.difficulty === difficulty)
    }

    // 标签筛选
    if (tags) {
      filteredProblems = filteredProblems.filter(problem =>
        problem.tags.toLowerCase().includes(tags.toLowerCase())
      )
    }

    const total = filteredProblems.length
    const pages = Math.ceil(total / size)
    const start = (current - 1) * size
    const end = start + size
    const records = filteredProblems.slice(start, end)

    return [200, {
      code: 200,
      message: 'success',
      data: {
        records,
        total,
        size,
        current,
        pages
      }
    }]
  });

  // Get single problem
  mock.onGet(/\/problems\/\d+/).reply(config => {
    const id = config.url?.match(/\/problems\/(\d+)/)?.[1]
    const problem = allProblems.find(p => p.id === Number(id))
    if (problem) {
      return [200, {
        code: 200,
        message: 'success',
        data: problem,
      }]
    }
    return [404, { code: 404, message: '题目不存在', data: null }]
  })

  // Create problem
  mock.onPost('/problems').reply(config => {
    const data = JSON.parse(config.data)
    const newProblem = {
      id: problems.length + 1,
      ...data,
      creatorId: 1,
      creatorName: 'admin',
      creatorNickname: '管理员',
      status: 'PUBLISHED',
      viewCount: 0,
      likeCount: 0,
      submissionCount: 0,
      acceptedCount: 0,
      createdTime: new Date().toISOString(),
      updatedTime: new Date().toISOString(),
    }
    allProblems.push(newProblem)
    return [200, {
      code: 200,
      message: 'success',
      data: newProblem,
    }];
  });

  // Search problems
  mock.onGet('/problems/search').reply(config => {
    const params = config.params || {}
    const keyword = params.keyword || ''
    const difficulty = params.difficulty || ''
    const tags = params.tags || ''

    let filteredProblems = [...allProblems]

    if (keyword) {
      filteredProblems = filteredProblems.filter(problem =>
        problem.title.toLowerCase().includes(keyword.toLowerCase()) ||
        problem.description.toLowerCase().includes(keyword.toLowerCase())
      )
    }

    if (difficulty) {
      filteredProblems = filteredProblems.filter(problem => problem.difficulty === difficulty)
    }

    if (tags) {
      filteredProblems = filteredProblems.filter(problem =>
        problem.tags.toLowerCase().includes(tags.toLowerCase())
      )
    }

    return [200, {
      code: 200,
      message: 'success',
      data: filteredProblems.slice(0, 20) // 限制搜索结果数量
    }]
  });

  // Increment view count
  mock.onPost(/\/problems\/\d+\/view/).reply(config => {
    const id = config.url?.match(/\/problems\/(\d+)\/view/)?.[1]
    const problem = allProblems.find(p => p.id === Number(id))
    if (problem) {
      problem.viewCount++
      return [200, {
        code: 200,
        message: 'success',
        data: null,
      }]
    }
    return [404, { code: 404, message: '题目不存在', data: null }]
  })
}
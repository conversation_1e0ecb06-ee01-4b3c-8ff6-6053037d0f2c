import type Mock<PERSON>dapter from 'axios-mock-adapter'

const problems = Array.from({ length: 25 }, (_, i) => ({
  id: i + 1,
  title: `Problem #${i + 1}: Two Sum`,
  description: `Given an array of integers nums and an integer target, return indices of the two numbers such that they add up to target. You may assume that each input would have exactly one solution, and you may not use the same element twice. You can return the answer in any order.`,
  difficulty: ['EASY', 'MEDIUM', 'HARD'][i % 3],
  tags: ['Array', 'Hash Table'],
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
}));

export function mockProblem(mock: MockAdapter) {
  // Get problem list
  mock.onGet('/api/problems').reply(200, {
    data: problems
  });

  // Get single problem
  mock.onGet(/\/api\/problems\/\d+/).reply(config => {
    const id = config.url?.match(/\/api\/problems\/(\d+)/)?.[1]
    const problem = problems.find(p => p.id === id)
    if (problem) {
      return [200, {
        code: 0,
        message: 'success',
        data: problem,
      }]
    }
    return [404, { code: 404, message: '题目不存在', data: null }]
  })
} 
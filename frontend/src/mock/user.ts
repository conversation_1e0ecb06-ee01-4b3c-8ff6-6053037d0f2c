import type MockAdapter from 'axios-mock-adapter'

// 模拟用户配置文件数据
const userProfiles = new Map([
  [1, {
    id: 1,
    username: 'testuser',
    nickname: '测试用户',
    email: '<EMAIL>',
    avatar: 'https://i.pravatar.cc/150?u=testuser',
    role: 'USER',
    status: 1,
    emailVerified: true,
    points: 1250,
    bio: '这是一个测试用户的个人简介，热爱算法和编程。',
    location: '北京',
    company: '测试公司',
    github: 'https://github.com/testuser',
    website: 'https://testuser.dev',
    skills: ['JavaScript', 'Vue.js', 'Python', '算法'],
    interests: ['前端开发', '数据结构', '机器学习'],
    createTime: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
    updateTime: new Date().toISOString(),
    lastLoginTime: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
    lastLoginIp: '*************'
  }],
  [99, {
    id: 99,
    username: 'admin',
    nickname: '系统管理员',
    email: '<EMAIL>',
    avatar: 'https://i.pravatar.cc/150?u=admin',
    role: 'ADMIN',
    status: 1,
    emailVerified: true,
    points: 9999,
    bio: '系统管理员账户，负责平台维护和管理。',
    location: '上海',
    company: 'Code-Combined',
    github: 'https://github.com/admin',
    website: 'https://code-combined.com',
    skills: ['Java', 'Spring Boot', 'Vue.js', '系统架构'],
    interests: ['系统设计', '性能优化', '团队管理'],
    createTime: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString(),
    updateTime: new Date().toISOString(),
    lastLoginTime: new Date(Date.now() - 10 * 60 * 1000).toISOString(),
    lastLoginIp: '***********'
  }]
])

// 模拟用户统计数据
const userStats = new Map([
  [1, {
    userId: 1,
    totalSubmissions: 156,
    acceptedSubmissions: 89,
    acceptanceRate: 0.57,
    solvedProblems: 67,
    easyProblems: 35,
    mediumProblems: 25,
    hardProblems: 7,
    rank: 1247,
    points: 1250,
    streak: 5, // 连续提交天数
    lastSubmissionTime: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString()
  }],
  [99, {
    userId: 99,
    totalSubmissions: 2345,
    acceptedSubmissions: 1987,
    acceptanceRate: 0.85,
    solvedProblems: 456,
    easyProblems: 150,
    mediumProblems: 200,
    hardProblems: 106,
    rank: 1,
    points: 9999,
    streak: 365,
    lastSubmissionTime: new Date(Date.now() - 30 * 60 * 1000).toISOString()
  }]
])

export function mockUser(mock: MockAdapter) {
  // 获取用户配置文件
  mock.onGet(/\/users\/\d+\/profile/).reply(config => {
    const id = config.url?.match(/\/users\/(\d+)\/profile/)?.[1]
    const userId = Number(id)
    const profile = userProfiles.get(userId)
    
    if (profile) {
      return [200, {
        code: 200,
        message: 'success',
        data: profile
      }]
    }
    
    return [404, {
      code: 404,
      message: '用户不存在',
      data: null
    }]
  })

  // 更新用户配置文件
  mock.onPut(/\/users\/\d+\/profile/).reply(config => {
    const id = config.url?.match(/\/users\/(\d+)\/profile/)?.[1]
    const userId = Number(id)
    const updateData = JSON.parse(config.data)
    const currentProfile = userProfiles.get(userId)
    
    if (currentProfile) {
      const updatedProfile = {
        ...currentProfile,
        ...updateData,
        updateTime: new Date().toISOString()
      }
      userProfiles.set(userId, updatedProfile)
      
      return [200, {
        code: 200,
        message: '配置文件更新成功',
        data: updatedProfile
      }]
    }
    
    return [404, {
      code: 404,
      message: '用户不存在',
      data: null
    }]
  })

  // 获取用户统计数据
  mock.onGet(/\/users\/\d+\/stats/).reply(config => {
    const id = config.url?.match(/\/users\/(\d+)\/stats/)?.[1]
    const userId = Number(id)
    const stats = userStats.get(userId)
    
    if (stats) {
      return [200, {
        code: 200,
        message: 'success',
        data: stats
      }]
    }
    
    return [404, {
      code: 404,
      message: '用户统计数据不存在',
      data: null
    }]
  })

  // 上传用户头像
  mock.onPost(/\/users\/\d+\/avatar/).reply(config => {
    const id = config.url?.match(/\/users\/(\d+)\/avatar/)?.[1]
    const userId = Number(id)
    const profile = userProfiles.get(userId)
    
    if (profile) {
      // 模拟头像上传成功
      const newAvatarUrl = `https://i.pravatar.cc/150?u=${profile.username}&t=${Date.now()}`
      profile.avatar = newAvatarUrl
      profile.updateTime = new Date().toISOString()
      userProfiles.set(userId, profile)
      
      return [200, {
        code: 200,
        message: '头像上传成功',
        data: {
          avatarUrl: newAvatarUrl
        }
      }]
    }
    
    return [404, {
      code: 404,
      message: '用户不存在',
      data: null
    }]
  })

  // 修改密码
  mock.onPost(/\/users\/\d+\/change-password/).reply(config => {
    const id = config.url?.match(/\/users\/(\d+)\/change-password/)?.[1]
    const userId = Number(id)
    const { oldPassword, newPassword } = JSON.parse(config.data)
    const profile = userProfiles.get(userId)
    
    if (profile) {
      // 模拟密码验证（在实际应用中不应该这样做）
      if (oldPassword === 'user123' || oldPassword === 'admin123') {
        profile.updateTime = new Date().toISOString()
        userProfiles.set(userId, profile)
        
        return [200, {
          code: 200,
          message: '密码修改成功',
          data: null
        }]
      } else {
        return [400, {
          code: 400,
          message: '原密码错误',
          data: null
        }]
      }
    }
    
    return [404, {
      code: 404,
      message: '用户不存在',
      data: null
    }]
  })

  // 获取用户提交历史
  mock.onGet(/\/users\/\d+\/submissions/).reply(config => {
    const id = config.url?.match(/\/users\/(\d+)\/submissions/)?.[1]
    const userId = Number(id)
    const params = config.params || {}
    const current = parseInt(params.current) || 1
    const size = parseInt(params.size) || 10
    
    // 模拟提交历史数据
    const submissions = Array.from({ length: 50 }, (_, i) => ({
      id: i + 1,
      problemId: Math.floor(Math.random() * 100) + 1,
      problemTitle: `题目 ${Math.floor(Math.random() * 100) + 1}`,
      status: ['ACCEPTED', 'WRONG_ANSWER', 'TIME_LIMIT_EXCEEDED', 'RUNTIME_ERROR'][Math.floor(Math.random() * 4)],
      language: ['Java', 'Python', 'JavaScript', 'C++'][Math.floor(Math.random() * 4)],
      runtime: Math.floor(Math.random() * 1000) + 100,
      memory: Math.floor(Math.random() * 50) + 10,
      submitTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString()
    }))
    
    const total = submissions.length
    const pages = Math.ceil(total / size)
    const start = (current - 1) * size
    const end = start + size
    const records = submissions.slice(start, end)
    
    return [200, {
      code: 200,
      message: 'success',
      data: {
        records,
        total,
        size,
        current,
        pages
      }
    }]
  })

  // 获取用户收藏的题目
  mock.onGet(/\/users\/\d+\/favorites/).reply(config => {
    const id = config.url?.match(/\/users\/(\d+)\/favorites/)?.[1]
    const userId = Number(id)
    const params = config.params || {}
    const current = parseInt(params.current) || 1
    const size = parseInt(params.size) || 10
    
    // 模拟收藏的题目数据
    const favorites = Array.from({ length: 20 }, (_, i) => ({
      id: i + 1,
      problemId: i + 1,
      problemTitle: `收藏题目 ${i + 1}`,
      difficulty: ['EASY', 'MEDIUM', 'HARD'][i % 3],
      tags: '数组,动态规划',
      favoriteTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString()
    }))
    
    const total = favorites.length
    const pages = Math.ceil(total / size)
    const start = (current - 1) * size
    const end = start + size
    const records = favorites.slice(start, end)
    
    return [200, {
      code: 200,
      message: 'success',
      data: {
        records,
        total,
        size,
        current,
        pages
      }
    }]
  })
}

import type MockAdapter from 'axios-mock-adapter'
import { users } from './data'

const mockUser: any = {
  id: '1',
  username: 'mockuser',
  nickname: '<PERSON>ck User',
  email: '<EMAIL>',
  avatar: 'https://i.pravatar.cc/150?u=mockuser',
  role: 'USER',
  status: 'ACTIVE',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
}

const mockAdmin: any = {
  id: '99',
  username: 'mockadmin',
  nickname: 'Mock Admin',
  email: '<EMAIL>',
  avatar: 'https://i.pravatar.cc/150?u=mockadmin',
  role: 'ADMIN',
  status: 'ACTIVE',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
}

export function mockAuth(mock: MockAdapter) {
  // 模拟登录
  mock.onPost('/api/auth/login').reply(config => {
    const { email, password } = JSON.parse(config.data)
    
    if (email === '<EMAIL>' && password === 'admin123') {
      return [200, {
        code: 200,
        message: '登录成功',
        data: {
          token: 'fake-admin-jwt-token-from-mock',
          user: mockAdmin
        }
      }]
    }

    if (email === '<EMAIL>' && password === 'user123') {
      return [200, {
        code: 200,
        message: '登录成功',
        data: {
          token: 'fake-user-jwt-token-from-mock',
          user: mockUser
        }
      }]
    }

    return [400, {
      code: 400,
      message: '邮箱或密码错误',
      data: null
    }]
  })

  // 模拟获取当前用户信息
  mock.onGet('/users/me').reply(config => {
    const token = config.headers?.Authorization
    if (token === 'Bearer fake-admin-jwt-token-from-mock') {
      return [200, { code: 200, message: '成功', data: mockAdmin }]
    }
    if (token === 'Bearer fake-user-jwt-token-from-mock') {
      return [200, { code: 200, message: '成功', data: mockUser }]
    }
    return [401, { code: 401, message: '未授权', data: null }]
  })

  // 模拟注册
  mock.onPost('/api/auth/register').reply(config => {
    const { email, password } = JSON.parse(config.data)
    const newUser = {
      id: 'new-user-id',
      username: email.split('@')[0],
      nickname: 'New User',
      email: email,
      avatar: 'https://i.pravatar.cc/150?u=new-user',
      role: 'USER',
      status: 'ACTIVE',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }
    return [200, {
      code: 0,
      message: 'Registration successful',
      data: { user: newUser, token: 'new-mock-jwt-token-for-registration' }
    }]
  })
  
  // 模拟登出
  mock.onPost('/api/auth/logout').reply(200, {
    code: 0,
    message: 'Logout successful'
  })
} 
import type MockAdapter from 'axios-mock-adapter'

const mockUser: any = {
  id: '1',
  username: 'mockuser',
  nickname: 'Mock User',
  email: '<EMAIL>',
  avatar: 'https://i.pravatar.cc/150?u=mockuser',
  role: 'USER',
  status: 'ACTIVE',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
}

const mockAdmin: any = {
  id: '99',
  username: 'mockadmin',
  nickname: 'Mock Admin',
  email: '<EMAIL>',
  avatar: 'https://i.pravatar.cc/150?u=mockadmin',
  role: 'ADMIN',
  status: 'ACTIVE',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
}

export function mockAuth(mock: MockAdapter) {
  // 模拟登录
  mock.onPost('/auth/login').reply(config => {
    const { email, password } = JSON.parse(config.data)

    if (email === '<EMAIL>' && password === 'admin123') {
      return [200, {
        code: 200,
        message: '登录成功',
        data: {
          token: 'fake-admin-jwt-token-from-mock',
          user: mockAdmin
        }
      }]
    }

    if (email === '<EMAIL>' && password === 'user123') {
      return [200, {
        code: 200,
        message: '登录成功',
        data: {
          token: 'fake-user-jwt-token-from-mock',
          user: mockUser
        }
      }]
    }

    return [400, {
      code: 400,
      message: '邮箱或密码错误',
      data: null
    }]
  })

  // 模拟获取当前用户信息
  mock.onGet('/users/me').reply(config => {
    const token = config.headers?.Authorization
    if (token === 'Bearer fake-admin-jwt-token-from-mock') {
      return [200, { code: 200, message: '成功', data: mockAdmin }]
    }
    if (token === 'Bearer fake-user-jwt-token-from-mock') {
      return [200, { code: 200, message: '成功', data: mockUser }]
    }
    return [401, { code: 401, message: '未授权', data: null }]
  })

  // 模拟注册
  mock.onPost('/auth/register').reply(config => {
    const { email, password, username, nickname } = JSON.parse(config.data)
    const newUser = {
      id: Date.now(),
      username: username || email.split('@')[0],
      nickname: nickname || 'New User',
      email: email,
      avatar: 'https://i.pravatar.cc/150?u=new-user',
      role: 'USER',
      status: 1,
      emailVerified: false,
      points: 0,
      createdTime: new Date().toISOString(),
      updatedTime: new Date().toISOString(),
    }
    return [200, {
      code: 200,
      message: '注册成功',
      data: { user: newUser, token: 'new-mock-jwt-token-for-registration' }
    }]
  })

  // 模拟登出
  mock.onPost('/auth/logout').reply(200, {
    code: 200,
    message: '登出成功'
  })

  // 模拟发送验证码
  mock.onPost('/auth/send-code').reply(config => {
    const { email } = JSON.parse(config.data)
    console.log(`Mock: 发送验证码到 ${email}`)
    return [200, {
      code: 200,
      message: '验证码发送成功',
      data: null
    }]
  })

  // 模拟忘记密码
  mock.onPost('/auth/forgot-password').reply(config => {
    const { email } = JSON.parse(config.data)
    console.log(`Mock: 重置密码邮件发送到 ${email}`)
    return [200, {
      code: 200,
      message: '重置密码邮件已发送',
      data: null
    }]
  })
}
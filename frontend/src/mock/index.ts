import axios from 'axios'
import MockAdapter from 'axios-mock-adapter'
import { mockAuth } from './auth'
import { mockProblem } from './problem'
import { mockProblemSet } from './problemset'
import { mockStats } from './stats'
import { mockAdmin } from './admin'
import api from '@/utils/api'

export function setupMocks() {
  console.log('Attempting to set up mocks...')
  
  if (!api) {
    console.error('❌ Mock setup failed: Axios instance (api) is not available.')
    return
  }

  const mock = new MockAdapter(api, {
    delayResponse: 500
  })

  mockAuth(mock)
  mockProblem(mock)
  mockProblemSet(mock)
  mockStats(mock)
  mockAdmin(mock)

  // 对于所有未被mock的请求，返回404
  mock.onAny().reply(404, {
    code: 404,
    message: 'Mock API: 接口未找到',
    data: null
  })
  
  console.log('✅ Mock adapter setup complete.')
} 
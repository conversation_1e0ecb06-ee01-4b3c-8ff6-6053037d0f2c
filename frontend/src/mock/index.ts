import axios from 'axios'
import Mock<PERSON>dapter from 'axios-mock-adapter'
import { mockAuth } from './auth'
import { mockProblem } from './problem'
import { mockProblemSet } from './problemset'
import { mockStats } from './stats'
import api from '@/utils/api'

export function setupMocks() {
  console.log('Attempting to set up mocks...')
  
  if (!api) {
    console.error('❌ Mock setup failed: Axios instance (api) is not available.')
    return
  }

  const mock = new MockAdapter(api, {
    delayResponse: 500
  })

  mockAuth(mock)
  mockProblem(mock)
  mockProblemSet(mock)
  // mockStats(mock) // 暂时禁用

  // 对于所有未被mock的请求，让它们正常通过
  mock.onAny().passThrough()
  
  console.log('✅ Mock adapter setup complete.')
} 
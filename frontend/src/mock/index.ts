import axios from 'axios'
import MockAdapter from 'axios-mock-adapter'
import { mockAuth } from './auth'
import { mockUser } from './user'
import { mockProblem } from './problem'
import { mockProblemSet } from './problemset'
import { mockSubmission } from './submission'
import { mockStats } from './stats'
import { mockAdmin } from './admin'
import api from '@/utils/api'

export function setupMocks() {
  console.log('🎭 [Mock] Initializing Mock Data Mode...')

  if (!api) {
    console.error('❌ [Mock] Setup failed: Axios instance (api) is not available.')
    return
  }

  // 创建 MockAdapter 实例，添加延迟模拟真实网络请求
  const mock = new MockAdapter(api, {
    delayResponse: 300, // 300ms 延迟，模拟网络请求
    onNoMatch: 'throwException' // 未匹配的请求抛出异常
  })

  console.log('🔧 [Mock] Setting up API interceptors...')

  // 注册各模块的 mock 数据
  try {
    console.log('  📝 [Mock] Loading auth module...')
    mockAuth(mock)

    console.log('  👤 [Mock] Loading user module...')
    mockUser(mock)

    console.log('  🧩 [Mock] Loading problem module...')
    mockProblem(mock)

    console.log('  📚 [Mock] Loading problemset module...')
    mockProblemSet(mock)

    console.log('  📝 [Mock] Loading submission module...')
    mockSubmission(mock)

    console.log('  📊 [Mock] Loading stats module...')
    mockStats(mock)

    console.log('  👑 [Mock] Loading admin module...')
    mockAdmin(mock)

    console.log('✅ [Mock] All modules loaded successfully')
  } catch (error) {
    console.error('❌ [Mock] Error loading modules:', error)
  }

  // 对于所有未被mock的请求，返回详细的404错误
  mock.onAny().reply(config => {
    const url = config.url || 'unknown'
    const method = (config.method || 'unknown').toUpperCase()

    console.warn(`⚠️ [Mock] Unhandled API request: ${method} ${url}`)
    console.warn('  💡 [Mock] This API endpoint needs to be added to mock configuration')

    return [404, {
      code: 404,
      message: `Mock API: 接口未找到 - ${method} ${url}`,
      data: null,
      timestamp: new Date().toISOString(),
      path: url,
      method: method
    }]
  })

  console.log('🎉 [Mock] Mock adapter setup complete - All API calls will use mock data')
  console.log('🔍 [Mock] Check browser console for API request logs')
}
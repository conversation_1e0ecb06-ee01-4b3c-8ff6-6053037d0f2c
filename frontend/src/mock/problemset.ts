import type MockAdapter from 'axios-mock-adapter'

const problemSets = Array.from({ length: 15 }, (_, i) => ({
  id: i + 1,
  name: `题集 #${i + 1}: 动态规划专题`,
  description: `一个专门练习动态规划算法的题目集合，包含从基础到进阶的各种题目。`,
  isPublic: i % 2 === 0,
  problemCount: Math.floor(Math.random() * 20) + 5,
  viewCount: Math.floor(Math.random() * 1000) + 100,
  likeCount: Math.floor(Math.random() * 50) + 10,
  creatorId: 1,
  creatorName: 'admin',
  status: 'PUBLISHED',
  createdTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
  updatedTime: new Date().toISOString(),
}));

// 模拟题目数据
const problems = Array.from({ length: 50 }, (_, i) => ({
  id: i + 1,
  title: `题目 ${i + 1}: ${['两数之和', '最长公共子序列', '背包问题', '斐波那契数列', '爬楼梯'][i % 5]}`,
  description: `这是第 ${i + 1} 个算法题目的描述...`,
  difficulty: ['EASY', 'MEDIUM', 'HARD'][i % 3],
  tags: ['动态规划', '数组', '字符串', '树', '图'][Math.floor(i / 10)] + ',算法',
  timeLimit: 1000,
  memoryLimit: 256,
  creatorId: 1,
  creatorName: 'admin',
  status: 'PUBLISHED',
  viewCount: Math.floor(Math.random() * 500) + 50,
  likeCount: Math.floor(Math.random() * 20) + 5,
  submissionCount: Math.floor(Math.random() * 100) + 10,
  acceptedCount: Math.floor(Math.random() * 50) + 5,
  createdTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
  updatedTime: new Date().toISOString(),
}));

export function mockProblemSet(mock: MockAdapter) {
  // Get problem set list with pagination
  mock.onGet('/problemsets').reply(config => {
    const params = config.params || {}
    const current = parseInt(params.current) || 1
    const size = parseInt(params.size) || 10
    const keyword = params.keyword || ''
    const isPublic = params.isPublic

    let filteredSets = [...problemSets]

    // 关键词搜索
    if (keyword) {
      filteredSets = filteredSets.filter(set =>
        set.name.toLowerCase().includes(keyword.toLowerCase()) ||
        set.description.toLowerCase().includes(keyword.toLowerCase())
      )
    }

    // 公开性筛选
    if (isPublic !== undefined && isPublic !== '') {
      filteredSets = filteredSets.filter(set => set.isPublic === (isPublic === '1'))
    }

    const total = filteredSets.length
    const pages = Math.ceil(total / size)
    const start = (current - 1) * size
    const end = start + size
    const records = filteredSets.slice(start, end)

    return [200, {
      code: 200,
      message: 'success',
      data: {
        records,
        total,
        size,
        current,
        pages
      }
    }]
  });

  // Get single problem set
  mock.onGet(/\/problemsets\/\d+/).reply(config => {
    const id = config.url?.match(/\/problemsets\/(\d+)/)?.[1]
    const set = problemSets.find(p => p.id === Number(id))
    if (set) {
      return [200, {
        code: 200,
        message: 'success',
        data: set,
      }];
    }
    return [404, { code: 404, message: 'Problem set not found' }];
  });

  // Get problems in a problem set
  mock.onGet(/\/problemsets\/\d+\/problems/).reply(config => {
    const id = config.url?.match(/\/problemsets\/(\d+)\/problems/)?.[1]
    const set = problemSets.find(p => p.id === Number(id))
    if (set) {
      // 返回该题集的前几个题目
      const setProblems = problems.slice(0, Math.min(set.problemCount, 10))
      return [200, {
        code: 200,
        message: 'success',
        data: setProblems,
      }];
    }
    return [404, { code: 404, message: 'Problem set not found' }];
  });

  // Create problem set
  mock.onPost('/problemsets').reply(config => {
    const data = JSON.parse(config.data)
    const newSet = {
      id: problemSets.length + 1,
      ...data,
      problemCount: 0,
      viewCount: 0,
      likeCount: 0,
      creatorId: 1,
      creatorName: 'admin',
      status: 'PUBLISHED',
      createdTime: new Date().toISOString(),
      updatedTime: new Date().toISOString(),
    }
    problemSets.push(newSet)
    return [200, {
      code: 200,
      message: 'success',
      data: newSet,
    }];
  });

  // Add problem to problem set
  mock.onPost(/\/problemsets\/\d+\/problems/).reply(config => {
    const problemSetId = config.url?.match(/\/problemsets\/(\d+)\/problems/)?.[1]
    const data = JSON.parse(config.data)
    const problemId = data.problemId

    console.log(`Adding problem ${problemId} to problem set ${problemSetId}`)

    return [200, {
      code: 200,
      message: 'success',
      data: null,
    }];
  });

  // Remove problem from problem set
  mock.onDelete(/\/problemsets\/\d+\/problems\/\d+/).reply(config => {
    const match = config.url?.match(/\/problemsets\/(\d+)\/problems\/(\d+)/)
    const problemSetId = match?.[1]
    const problemId = match?.[2]

    console.log(`Removing problem ${problemId} from problem set ${problemSetId}`)

    return [200, {
      code: 200,
      message: 'success',
      data: null,
    }];
  });
}
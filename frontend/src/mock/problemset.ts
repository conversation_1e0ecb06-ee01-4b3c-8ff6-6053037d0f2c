import type MockAdapter from 'axios-mock-adapter'

// LeetCode 风格的主题分类题集
const leetcodeProblemSets = [
  {
    id: 1,
    name: '数组与哈希表',
    description: '掌握数组和哈希表的基本操作，包括查找、插入、删除等常见算法。适合算法入门者练习基础数据结构。',
    isPublic: true,
    problemCount: 12,
    difficulty: 'EASY',
    tags: '数组,哈希表,基础',
    estimatedTime: '2-3小时',
    problems: [1, 15, 121, 141] // 对应的题目ID
  },
  {
    id: 2,
    name: '链表操作精讲',
    description: '深入理解链表的各种操作，包括反转、合并、环检测等经典问题。掌握指针操作的核心技巧。',
    isPublic: true,
    problemCount: 8,
    difficulty: 'MEDIUM',
    tags: '链表,指针,递归',
    estimatedTime: '3-4小时',
    problems: [2, 21, 141, 206]
  },
  {
    id: 3,
    name: '字符串算法专题',
    description: '字符串处理的各种算法，包括模式匹配、回文检测、子串查找等。提升字符串处理能力。',
    isPublic: true,
    problemCount: 10,
    difficulty: 'MEDIUM',
    tags: '字符串,动态规划,滑动窗口',
    estimatedTime: '4-5小时',
    problems: [3, 5, 20]
  },
  {
    id: 4,
    name: '动态规划入门',
    description: '从简单的动态规划问题开始，逐步掌握状态转移方程的设计思路。包含经典的爬楼梯、股票买卖等问题。',
    isPublic: true,
    problemCount: 15,
    difficulty: 'MEDIUM',
    tags: '动态规划,记忆化搜索,优化',
    estimatedTime: '6-8小时',
    problems: [53, 70, 121]
  },
  {
    id: 5,
    name: '树与图算法',
    description: '树和图的遍历算法，包括DFS、BFS、最短路径等。掌握递归和非递归的实现方式。',
    isPublic: true,
    problemCount: 18,
    difficulty: 'HARD',
    tags: '树,图,深度优先搜索,广度优先搜索',
    estimatedTime: '8-10小时',
    problems: [200]
  },
  {
    id: 6,
    name: '双指针技巧',
    description: '双指针是解决数组和字符串问题的重要技巧。包含快慢指针、左右指针等多种应用场景。',
    isPublic: true,
    problemCount: 12,
    difficulty: 'MEDIUM',
    tags: '双指针,数组,字符串',
    estimatedTime: '3-4小时',
    problems: [15, 141]
  },
  {
    id: 7,
    name: '栈与队列应用',
    description: '栈和队列的经典应用，包括括号匹配、表达式求值、单调栈等问题。',
    isPublic: true,
    problemCount: 10,
    difficulty: 'MEDIUM',
    tags: '栈,队列,单调栈',
    estimatedTime: '3-4小时',
    problems: [20]
  },
  {
    id: 8,
    name: '二分查找专题',
    description: '二分查找的各种变形，包括查找边界、旋转数组查找等。掌握二分查找的核心思想。',
    isPublic: true,
    problemCount: 8,
    difficulty: 'MEDIUM',
    tags: '二分查找,数组,查找',
    estimatedTime: '2-3小时',
    problems: [4]
  },
  {
    id: 9,
    name: '贪心算法精选',
    description: '贪心算法的经典问题，学会如何证明贪心策略的正确性。包含区间调度、活动选择等问题。',
    isPublic: true,
    problemCount: 12,
    difficulty: 'MEDIUM',
    tags: '贪心,排序,区间',
    estimatedTime: '4-5小时',
    problems: []
  },
  {
    id: 10,
    name: '高频面试题',
    description: '互联网公司高频面试算法题，涵盖各个知识点。适合面试前的集中练习和复习。',
    isPublic: true,
    problemCount: 25,
    difficulty: 'MIXED',
    tags: '面试,综合,高频',
    estimatedTime: '10-15小时',
    problems: [1, 2, 3, 15, 20, 21, 53, 70, 121, 141, 200, 206]
  }
]

const problemSets = leetcodeProblemSets.map(set => ({
  ...set,
  viewCount: Math.floor(Math.random() * 5000) + 1000,
  likeCount: Math.floor(Math.random() * 200) + 50,
  creatorId: 1,
  creatorName: 'admin',
  status: 'PUBLISHED',
  createdTime: new Date(Date.now() - Math.random() * 180 * 24 * 60 * 60 * 1000).toISOString(),
  updatedTime: new Date().toISOString(),
}));

// 题集中的题目映射（从 problem.ts 中引用）
const problemsInSets = {
  1: [ // 数组与哈希表
    { id: 1, title: '两数之和', difficulty: 'EASY', tags: '数组,哈希表', acceptanceRate: 0.52 },
    { id: 15, title: '三数之和', difficulty: 'MEDIUM', tags: '数组,双指针,排序', acceptanceRate: 0.33 },
    { id: 121, title: '买卖股票的最佳时机', difficulty: 'EASY', tags: '数组,动态规划', acceptanceRate: 0.56 },
    { id: 141, title: '环形链表', difficulty: 'EASY', tags: '哈希表,链表,双指针', acceptanceRate: 0.51 }
  ],
  2: [ // 链表操作精讲
    { id: 2, title: '两数相加', difficulty: 'MEDIUM', tags: '链表,数学,递归', acceptanceRate: 0.39 },
    { id: 21, title: '合并两个有序链表', difficulty: 'EASY', tags: '链表,递归', acceptanceRate: 0.62 },
    { id: 141, title: '环形链表', difficulty: 'EASY', tags: '哈希表,链表,双指针', acceptanceRate: 0.51 },
    { id: 206, title: '反转链表', difficulty: 'EASY', tags: '递归,链表', acceptanceRate: 0.73 }
  ],
  3: [ // 字符串算法专题
    { id: 3, title: '无重复字符的最长子串', difficulty: 'MEDIUM', tags: '哈希表,字符串,滑动窗口', acceptanceRate: 0.36 },
    { id: 5, title: '最长回文子串', difficulty: 'MEDIUM', tags: '字符串,动态规划', acceptanceRate: 0.33 },
    { id: 20, title: '有效的括号', difficulty: 'EASY', tags: '栈,字符串', acceptanceRate: 0.40 }
  ],
  4: [ // 动态规划入门
    { id: 53, title: '最大子数组和', difficulty: 'MEDIUM', tags: '数组,分治,动态规划', acceptanceRate: 0.50 },
    { id: 70, title: '爬楼梯', difficulty: 'EASY', tags: '记忆化搜索,数学,动态规划', acceptanceRate: 0.52 },
    { id: 121, title: '买卖股票的最佳时机', difficulty: 'EASY', tags: '数组,动态规划', acceptanceRate: 0.56 }
  ],
  5: [ // 树与图算法
    { id: 200, title: '岛屿数量', difficulty: 'MEDIUM', tags: '深度优先搜索,广度优先搜索,并查集,数组,矩阵', acceptanceRate: 0.58 }
  ],
  6: [ // 双指针技巧
    { id: 15, title: '三数之和', difficulty: 'MEDIUM', tags: '数组,双指针,排序', acceptanceRate: 0.33 },
    { id: 141, title: '环形链表', difficulty: 'EASY', tags: '哈希表,链表,双指针', acceptanceRate: 0.51 }
  ],
  7: [ // 栈与队列应用
    { id: 20, title: '有效的括号', difficulty: 'EASY', tags: '栈,字符串', acceptanceRate: 0.40 }
  ],
  8: [ // 二分查找专题
    { id: 4, title: '寻找两个正序数组的中位数', difficulty: 'HARD', tags: '数组,二分查找,分治', acceptanceRate: 0.37 }
  ],
  9: [], // 贪心算法精选
  10: [ // 高频面试题
    { id: 1, title: '两数之和', difficulty: 'EASY', tags: '数组,哈希表', acceptanceRate: 0.52 },
    { id: 2, title: '两数相加', difficulty: 'MEDIUM', tags: '链表,数学,递归', acceptanceRate: 0.39 },
    { id: 3, title: '无重复字符的最长子串', difficulty: 'MEDIUM', tags: '哈希表,字符串,滑动窗口', acceptanceRate: 0.36 },
    { id: 15, title: '三数之和', difficulty: 'MEDIUM', tags: '数组,双指针,排序', acceptanceRate: 0.33 },
    { id: 20, title: '有效的括号', difficulty: 'EASY', tags: '栈,字符串', acceptanceRate: 0.40 },
    { id: 21, title: '合并两个有序链表', difficulty: 'EASY', tags: '链表,递归', acceptanceRate: 0.62 },
    { id: 53, title: '最大子数组和', difficulty: 'MEDIUM', tags: '数组,分治,动态规划', acceptanceRate: 0.50 },
    { id: 70, title: '爬楼梯', difficulty: 'EASY', tags: '记忆化搜索,数学,动态规划', acceptanceRate: 0.52 },
    { id: 121, title: '买卖股票的最佳时机', difficulty: 'EASY', tags: '数组,动态规划', acceptanceRate: 0.56 },
    { id: 141, title: '环形链表', difficulty: 'EASY', tags: '哈希表,链表,双指针', acceptanceRate: 0.51 },
    { id: 200, title: '岛屿数量', difficulty: 'MEDIUM', tags: '深度优先搜索,广度优先搜索,并查集,数组,矩阵', acceptanceRate: 0.58 },
    { id: 206, title: '反转链表', difficulty: 'EASY', tags: '递归,链表', acceptanceRate: 0.73 }
  ]
};

export function mockProblemSet(mock: MockAdapter) {
  // Get problem set list with pagination
  mock.onGet('/problemsets').reply(config => {
    const params = config.params || {}
    const current = parseInt(params.current) || 1
    const size = parseInt(params.size) || 10
    const keyword = params.keyword || ''
    const isPublic = params.isPublic

    let filteredSets = [...problemSets]

    // 关键词搜索
    if (keyword) {
      filteredSets = filteredSets.filter(set =>
        set.name.toLowerCase().includes(keyword.toLowerCase()) ||
        set.description.toLowerCase().includes(keyword.toLowerCase())
      )
    }

    // 公开性筛选
    if (isPublic !== undefined && isPublic !== '') {
      filteredSets = filteredSets.filter(set => set.isPublic === (isPublic === '1'))
    }

    const total = filteredSets.length
    const pages = Math.ceil(total / size)
    const start = (current - 1) * size
    const end = start + size
    const records = filteredSets.slice(start, end)

    return [200, {
      code: 200,
      message: 'success',
      data: {
        records,
        total,
        size,
        current,
        pages
      }
    }]
  });

  // Get single problem set
  mock.onGet(/\/problemsets\/\d+/).reply(config => {
    const id = config.url?.match(/\/problemsets\/(\d+)/)?.[1]
    const set = problemSets.find(p => p.id === Number(id))
    if (set) {
      return [200, {
        code: 200,
        message: 'success',
        data: set,
      }];
    }
    return [404, { code: 404, message: 'Problem set not found' }];
  });

  // Get problems in a problem set
  mock.onGet(/\/problemsets\/\d+\/problems/).reply(config => {
    const id = config.url?.match(/\/problemsets\/(\d+)\/problems/)?.[1]
    const setId = Number(id)
    const set = problemSets.find(p => p.id === setId)

    if (set) {
      // 获取该题集的题目列表
      const setProblems = problemsInSets[setId] || []

      // 添加额外的题目信息
      const enrichedProblems = setProblems.map(problem => ({
        ...problem,
        description: `这是${set.name}专题中的一道${problem.difficulty === 'EASY' ? '简单' : problem.difficulty === 'MEDIUM' ? '中等' : '困难'}题目。`,
        timeLimit: 1000,
        memoryLimit: 256,
        source: 'LeetCode',
        viewCount: Math.floor(Math.random() * 10000) + 1000,
        likeCount: Math.floor(Math.random() * 500) + 50,
        submissionCount: Math.floor(Math.random() * 5000) + 500,
        acceptedCount: Math.floor(Math.random() * 2000) + 200,
        createdTime: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
        updatedTime: new Date().toISOString(),
      }))

      return [200, {
        code: 200,
        message: 'success',
        data: enrichedProblems,
      }];
    }
    return [404, { code: 404, message: 'Problem set not found' }];
  });

  // Create problem set
  mock.onPost('/problemsets').reply(config => {
    const data = JSON.parse(config.data)
    const newSet = {
      id: problemSets.length + 1,
      ...data,
      problemCount: 0,
      viewCount: 0,
      likeCount: 0,
      creatorId: 1,
      creatorName: 'admin',
      status: 'PUBLISHED',
      createdTime: new Date().toISOString(),
      updatedTime: new Date().toISOString(),
    }
    problemSets.push(newSet)
    return [200, {
      code: 200,
      message: 'success',
      data: newSet,
    }];
  });

  // Add problem to problem set
  mock.onPost(/\/problemsets\/\d+\/problems/).reply(config => {
    const problemSetId = config.url?.match(/\/problemsets\/(\d+)\/problems/)?.[1]
    const data = JSON.parse(config.data)
    const problemId = data.problemId

    console.log(`Adding problem ${problemId} to problem set ${problemSetId}`)

    return [200, {
      code: 200,
      message: 'success',
      data: null,
    }];
  });

  // Remove problem from problem set
  mock.onDelete(/\/problemsets\/\d+\/problems\/\d+/).reply(config => {
    const match = config.url?.match(/\/problemsets\/(\d+)\/problems\/(\d+)/)
    const problemSetId = match?.[1]
    const problemId = match?.[2]

    console.log(`Removing problem ${problemId} from problem set ${problemSetId}`)

    return [200, {
      code: 200,
      message: 'success',
      data: null,
    }];
  });
}
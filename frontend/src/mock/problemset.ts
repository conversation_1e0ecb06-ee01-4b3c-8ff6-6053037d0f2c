import type Mo<PERSON><PERSON><PERSON>pter from 'axios-mock-adapter'

const problemSets = Array.from({ length: 15 }, (_, i) => ({
  id: i + 1,
  title: `Problem Set #${i + 1}: Dynamic Programming`,
  description: `A collection of problems to master dynamic programming.`,
  isPublic: i % 2 === 0,
  problemCount: Math.floor(Math.random() * 20) + 5,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
}));

export function mockProblemSet(mock: MockAdapter) {
  // Get problem set list
  mock.onGet('/api/problemsets').reply(200, {
    data: problemSets,
  });

  // Get single problem set
  mock.onGet(/\/api\/problemsets\/\d+/).reply(config => {
    const id = config.url?.match(/\/api\/problemsets\/(\d+)/)?.[1]
    const set = problemSets.find(p => p.id === Number(id))
    if (set) {
      return [200, {
        code: 0,
        message: 'success',
        data: set,
      }];
    }
    return [404, { code: 404, message: 'Problem set not found' }];
  });
} 
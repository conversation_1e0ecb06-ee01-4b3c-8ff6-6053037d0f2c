import type MockAdapter from 'axios-mock-adapter'

// 模拟提交数据
const submissions = Array.from({ length: 100 }, (_, i) => ({
  id: i + 1,
  userId: Math.floor(Math.random() * 10) + 1,
  username: `user${Math.floor(Math.random() * 10) + 1}`,
  problemId: Math.floor(Math.random() * 50) + 1,
  problemTitle: `题目 ${Math.floor(Math.random() * 50) + 1}`,
  language: ['Java', 'Python', 'JavaScript', 'C++', 'Go'][Math.floor(Math.random() * 5)],
  status: ['ACCEPTED', 'WRONG_ANSWER', 'TIME_LIMIT_EXCEEDED', 'MEMORY_LIMIT_EXCEEDED', 'RUNTIME_ERROR', 'COMPILE_ERROR'][Math.floor(Math.random() * 6)],
  runtime: Math.floor(Math.random() * 2000) + 100, // ms
  memory: Math.floor(Math.random() * 100) + 10, // MB
  codeLength: Math.floor(Math.random() * 5000) + 100,
  submitTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
  judgeTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
  score: Math.floor(Math.random() * 101), // 0-100
  testCases: {
    total: Math.floor(Math.random() * 20) + 10,
    passed: Math.floor(Math.random() * 15) + 5
  }
}))

// 模拟代码内容
const codeTemplates = {
  Java: `public class Solution {
    public int[] twoSum(int[] nums, int target) {
        Map<Integer, Integer> map = new HashMap<>();
        for (int i = 0; i < nums.length; i++) {
            int complement = target - nums[i];
            if (map.containsKey(complement)) {
                return new int[] { map.get(complement), i };
            }
            map.put(nums[i], i);
        }
        throw new IllegalArgumentException("No two sum solution");
    }
}`,
  Python: `def twoSum(nums, target):
    num_map = {}
    for i, num in enumerate(nums):
        complement = target - num
        if complement in num_map:
            return [num_map[complement], i]
        num_map[num] = i
    return []`,
  JavaScript: `function twoSum(nums, target) {
    const map = new Map();
    for (let i = 0; i < nums.length; i++) {
        const complement = target - nums[i];
        if (map.has(complement)) {
            return [map.get(complement), i];
        }
        map.set(nums[i], i);
    }
    return [];
}`,
  'C++': `class Solution {
public:
    vector<int> twoSum(vector<int>& nums, int target) {
        unordered_map<int, int> map;
        for (int i = 0; i < nums.size(); i++) {
            int complement = target - nums[i];
            if (map.find(complement) != map.end()) {
                return {map[complement], i};
            }
            map[nums[i]] = i;
        }
        return {};
    }
};`,
  Go: `func twoSum(nums []int, target int) []int {
    m := make(map[int]int)
    for i, num := range nums {
        complement := target - num
        if j, ok := m[complement]; ok {
            return []int{j, i}
        }
        m[num] = i
    }
    return nil
}`
}

export function mockSubmission(mock: MockAdapter) {
  // 获取提交列表
  mock.onGet('/submissions').reply(config => {
    const params = config.params || {}
    const current = parseInt(params.current) || 1
    const size = parseInt(params.size) || 10
    const userId = params.userId
    const problemId = params.problemId
    const status = params.status
    const language = params.language

    let filteredSubmissions = [...submissions]

    // 用户筛选
    if (userId) {
      filteredSubmissions = filteredSubmissions.filter(s => s.userId === parseInt(userId))
    }

    // 题目筛选
    if (problemId) {
      filteredSubmissions = filteredSubmissions.filter(s => s.problemId === parseInt(problemId))
    }

    // 状态筛选
    if (status) {
      filteredSubmissions = filteredSubmissions.filter(s => s.status === status)
    }

    // 语言筛选
    if (language) {
      filteredSubmissions = filteredSubmissions.filter(s => s.language === language)
    }

    // 按提交时间倒序排列
    filteredSubmissions.sort((a, b) => new Date(b.submitTime).getTime() - new Date(a.submitTime).getTime())

    const total = filteredSubmissions.length
    const pages = Math.ceil(total / size)
    const start = (current - 1) * size
    const end = start + size
    const records = filteredSubmissions.slice(start, end)

    return [200, {
      code: 200,
      message: 'success',
      data: {
        records,
        total,
        size,
        current,
        pages
      }
    }]
  })

  // 获取单个提交详情
  mock.onGet(/\/submissions\/\d+/).reply(config => {
    const id = config.url?.match(/\/submissions\/(\d+)/)?.[1]
    const submission = submissions.find(s => s.id === Number(id))
    
    if (submission) {
      const detailSubmission = {
        ...submission,
        code: codeTemplates[submission.language as keyof typeof codeTemplates] || '// 代码内容',
        judgeResult: {
          status: submission.status,
          runtime: submission.runtime,
          memory: submission.memory,
          score: submission.score,
          testCases: submission.testCases,
          errorMessage: submission.status !== 'ACCEPTED' ? '样例测试失败：输出结果与期望不符' : null,
          compileMessage: submission.status === 'COMPILE_ERROR' ? '编译错误：语法错误' : null
        }
      }
      
      return [200, {
        code: 200,
        message: 'success',
        data: detailSubmission
      }]
    }
    
    return [404, {
      code: 404,
      message: '提交记录不存在',
      data: null
    }]
  })

  // 提交代码
  mock.onPost('/submissions').reply(config => {
    const { problemId, language, code } = JSON.parse(config.data)
    
    // 模拟判题过程
    const statuses = ['ACCEPTED', 'WRONG_ANSWER', 'TIME_LIMIT_EXCEEDED', 'RUNTIME_ERROR']
    const randomStatus = statuses[Math.floor(Math.random() * statuses.length)]
    
    const newSubmission = {
      id: submissions.length + 1,
      userId: 1, // 当前用户ID
      username: 'testuser',
      problemId: problemId,
      problemTitle: `题目 ${problemId}`,
      language: language,
      status: 'JUDGING', // 初始状态为判题中
      runtime: 0,
      memory: 0,
      codeLength: code.length,
      submitTime: new Date().toISOString(),
      judgeTime: null,
      score: 0,
      testCases: {
        total: 10,
        passed: 0
      },
      code: code
    }
    
    submissions.push(newSubmission)
    
    // 模拟异步判题过程
    setTimeout(() => {
      newSubmission.status = randomStatus
      newSubmission.runtime = Math.floor(Math.random() * 1000) + 100
      newSubmission.memory = Math.floor(Math.random() * 50) + 10
      newSubmission.judgeTime = new Date().toISOString()
      newSubmission.score = randomStatus === 'ACCEPTED' ? 100 : Math.floor(Math.random() * 50)
      newSubmission.testCases.passed = randomStatus === 'ACCEPTED' ? 10 : Math.floor(Math.random() * 8)
    }, 2000)
    
    return [200, {
      code: 200,
      message: '代码提交成功，正在判题中...',
      data: {
        submissionId: newSubmission.id,
        status: 'JUDGING'
      }
    }]
  })

  // 获取提交状态（用于轮询判题结果）
  mock.onGet(/\/submissions\/\d+\/status/).reply(config => {
    const id = config.url?.match(/\/submissions\/(\d+)\/status/)?.[1]
    const submission = submissions.find(s => s.id === Number(id))
    
    if (submission) {
      return [200, {
        code: 200,
        message: 'success',
        data: {
          submissionId: submission.id,
          status: submission.status,
          runtime: submission.runtime,
          memory: submission.memory,
          score: submission.score,
          testCases: submission.testCases
        }
      }]
    }
    
    return [404, {
      code: 404,
      message: '提交记录不存在',
      data: null
    }]
  })

  // 重新判题
  mock.onPost(/\/submissions\/\d+\/rejudge/).reply(config => {
    const id = config.url?.match(/\/submissions\/(\d+)\/rejudge/)?.[1]
    const submission = submissions.find(s => s.id === Number(id))
    
    if (submission) {
      submission.status = 'JUDGING'
      submission.judgeTime = new Date().toISOString()
      
      // 模拟重新判题
      setTimeout(() => {
        const statuses = ['ACCEPTED', 'WRONG_ANSWER', 'TIME_LIMIT_EXCEEDED', 'RUNTIME_ERROR']
        submission.status = statuses[Math.floor(Math.random() * statuses.length)]
        submission.runtime = Math.floor(Math.random() * 1000) + 100
        submission.memory = Math.floor(Math.random() * 50) + 10
        submission.score = submission.status === 'ACCEPTED' ? 100 : Math.floor(Math.random() * 50)
        submission.testCases.passed = submission.status === 'ACCEPTED' ? submission.testCases.total : Math.floor(Math.random() * submission.testCases.total)
      }, 1500)
      
      return [200, {
        code: 200,
        message: '重新判题请求已提交',
        data: null
      }]
    }
    
    return [404, {
      code: 404,
      message: '提交记录不存在',
      data: null
    }]
  })

  // 获取提交统计
  mock.onGet('/submissions/stats').reply(config => {
    const params = config.params || {}
    const userId = params.userId
    const problemId = params.problemId
    
    let targetSubmissions = submissions
    
    if (userId) {
      targetSubmissions = targetSubmissions.filter(s => s.userId === parseInt(userId))
    }
    
    if (problemId) {
      targetSubmissions = targetSubmissions.filter(s => s.problemId === parseInt(problemId))
    }
    
    const total = targetSubmissions.length
    const accepted = targetSubmissions.filter(s => s.status === 'ACCEPTED').length
    const acceptanceRate = total > 0 ? (accepted / total * 100).toFixed(2) : '0.00'
    
    const languageStats = targetSubmissions.reduce((acc, s) => {
      acc[s.language] = (acc[s.language] || 0) + 1
      return acc
    }, {} as Record<string, number>)
    
    const statusStats = targetSubmissions.reduce((acc, s) => {
      acc[s.status] = (acc[s.status] || 0) + 1
      return acc
    }, {} as Record<string, number>)
    
    return [200, {
      code: 200,
      message: 'success',
      data: {
        total,
        accepted,
        acceptanceRate: parseFloat(acceptanceRate),
        languageStats,
        statusStats
      }
    }]
  })
}

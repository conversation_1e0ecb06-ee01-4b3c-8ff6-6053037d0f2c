// Vue 组件类型声明

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

// 全局属性类型声明
declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $message: {
      success: (message: string) => void
      error: (message: string) => void
      warning: (message: string) => void
      info: (message: string) => void
    }
    $confirm: (options: {
      title?: string
      content: string
      onOk?: () => void | Promise<void>
      onCancel?: () => void
    }) => void
    $loading: {
      show: (message?: string) => void
      hide: () => void
    }
  }
}

// 环境变量类型声明
interface ImportMetaEnv {
  readonly VITE_APP_TITLE: string
  readonly VITE_APP_API_BASE_URL: string
  readonly VITE_APP_ENV: 'development' | 'production' | 'test'
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

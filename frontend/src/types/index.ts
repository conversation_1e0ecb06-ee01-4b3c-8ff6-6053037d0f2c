// 通用类型定义

export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp?: string
}

export interface PaginationParams {
  page: number
  size: number
  keyword?: string
}

export interface PaginationResponse<T> {
  records: T[]
  total: number
  size: number
  current: number
  pages: number
}

// 用户相关类型
export interface User {
  id: number
  username: string
  email: string
  nickname?: string
  avatar?: string
  role: 'ADMIN' | 'USER'
  status: number
  emailVerified?: boolean
  points?: number
  bio?: string
  location?: string
  company?: string
  github?: string
  website?: string
  createTime?: string
  updateTime?: string
  lastLoginTime?: string
  lastLoginIp?: string
}

export interface LoginRequest {
  email: string
  password: string
}

export interface RegisterRequest {
  username: string
  email: string
  password: string
  nickname?: string
}

// 题目相关类型
export interface Problem {
  id: number
  title: string
  description?: string
  inputFormat?: string
  outputFormat?: string
  sampleInput?: string
  sampleOutput?: string
  hint?: string
  difficulty: 'EASY' | 'MEDIUM' | 'HARD'
  tags?: string
  timeLimit?: number
  memoryLimit?: number
  creatorId?: number
  creatorName?: string
  creatorNickname?: string
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED'
  viewCount?: number
  likeCount?: number
  submissionCount?: number
  acceptedCount?: number
  createdTime?: string
  updatedTime?: string
}

export interface ProblemCreateRequest {
  title: string
  description: string
  inputFormat?: string
  outputFormat?: string
  sampleInput?: string
  sampleOutput?: string
  hint?: string
  difficulty: 'EASY' | 'MEDIUM' | 'HARD'
  tags?: string
  timeLimit?: number
  memoryLimit?: number
}

// 题集相关类型
export interface ProblemSet {
  id: number
  name: string
  description?: string
  coverImage?: string
  isPublic: boolean
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED'
  creatorId?: number
  creatorName?: string
  creatorNickname?: string
  viewCount?: number
  likeCount?: number
  favoriteCount?: number
  problemCount?: number
  createdTime?: string
  updatedTime?: string
}

export interface ProblemSetCreateRequest {
  name: string
  description?: string
  coverImage?: string
  isPublic: boolean
}

// 统计相关类型
export interface UserStatistics {
  totalUsers: number
  activeUsers: number
  adminUsers: number
  normalUsers: number
  todayNewUsers: number
  weekNewUsers: number
  monthNewUsers: number
}

export interface ProblemStatistics {
  total: number
  easyCount: number
  mediumCount: number
  hardCount: number
  totalViews: number
  totalLikes: number
  totalSubmissions: number
  totalAccepted: number
}

export interface ProblemSetStatistics {
  total: number
  publicCount: number
  privateCount: number
  publishedCount: number
  draftCount: number
  totalViews: number
  totalLikes: number
  totalFavorites: number
  avgProblemCount: number
}

// 表格相关类型
export interface TableColumn {
  key: string
  title: string
  width?: string | number
  sortable?: boolean
  filterable?: boolean
  render?: (value: any, record: any, index: number) => any
}

export interface TableAction {
  label: string
  type?: 'primary' | 'success' | 'warning' | 'danger'
  icon?: string
  onClick: (record: any, index: number) => void
  show?: (record: any) => boolean
}

// 表单相关类型
export interface FormRule {
  required?: boolean
  message?: string
  type?: 'string' | 'number' | 'email' | 'url'
  min?: number
  max?: number
  pattern?: RegExp
  validator?: (value: any) => boolean | string
}

export interface FormField {
  key: string
  label: string
  type: 'input' | 'textarea' | 'select' | 'radio' | 'checkbox' | 'switch' | 'upload'
  placeholder?: string
  options?: Array<{ label: string; value: any }>
  rules?: FormRule[]
  disabled?: boolean
  show?: boolean
}

// 路由相关类型
export interface RouteMetaCustom {
  title?: string
  requiresAuth?: boolean
  requiresAdmin?: boolean
  guest?: boolean
  icon?: string
  hidden?: boolean
}

// Store 相关类型
export interface AuthState {
  token: string | null
  user: User | null
  loading: boolean
}

export interface AppState {
  sidebarCollapsed: boolean
  theme: 'light' | 'dark'
  language: 'zh-CN' | 'en-US'
}

// 组件 Props 类型
export interface ModalProps {
  visible: boolean
  title?: string
  width?: string | number
  maskClosable?: boolean
  destroyOnClose?: boolean
}

export interface TableProps<T = any> {
  data: T[]
  columns: TableColumn[]
  loading?: boolean
  pagination?: {
    current: number
    pageSize: number
    total: number
    showSizeChanger?: boolean
    showQuickJumper?: boolean
  }
  rowSelection?: {
    selectedRowKeys: (string | number)[]
    onChange: (selectedRowKeys: (string | number)[], selectedRows: T[]) => void
  }
  actions?: TableAction[]
}

// 事件类型
export interface SelectChangeEvent {
  target: {
    value: any
  }
}

export interface InputChangeEvent {
  target: {
    value: string
  }
}

// 工具类型
export type Nullable<T> = T | null
export type Optional<T> = T | undefined
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

// 常量类型
export const USER_ROLES = {
  ADMIN: 'ADMIN',
  USER: 'USER'
} as const

export const PROBLEM_DIFFICULTIES = {
  EASY: 'EASY',
  MEDIUM: 'MEDIUM',
  HARD: 'HARD'
} as const

export const PROBLEM_STATUSES = {
  DRAFT: 'DRAFT',
  PUBLISHED: 'PUBLISHED',
  ARCHIVED: 'ARCHIVED'
} as const

export type UserRole = typeof USER_ROLES[keyof typeof USER_ROLES]
export type ProblemDifficulty = typeof PROBLEM_DIFFICULTIES[keyof typeof PROBLEM_DIFFICULTIES]
export type ProblemStatus = typeof PROBLEM_STATUSES[keyof typeof PROBLEM_STATUSES]

/**
 * 消息提示工具类
 * 使用浏览器原生API或可以集成SweetAlert2等第三方库
 */

/**
 * 显示成功消息
 */
export function showSuccess(message) {
  console.log('✅ Success:', message)
  // 可以在这里集成具体的提示组件，比如SweetAlert2
  // Swal.fire({
  //   icon: 'success',
  //   title: '成功',
  //   text: message,
  //   timer: 3000,
  //   showConfirmButton: false
  // })
  
  // 临时使用浏览器原生提示
  if (typeof window !== 'undefined') {
    // 创建一个简单的Toast提示
    createToast(message, 'success')
  }
}

/**
 * 显示错误消息
 */
export function showError(message) {
  console.error('❌ Error:', message)
  // 可以在这里集成具体的提示组件
  
  // 临时使用浏览器原生提示
  if (typeof window !== 'undefined') {
    createToast(message, 'error')
  }
}

/**
 * 显示警告消息
 */
export function showWarning(message) {
  console.warn('⚠️ Warning:', message)
  
  if (typeof window !== 'undefined') {
    createToast(message, 'warning')
  }
}

/**
 * 显示信息消息
 */
export function showInfo(message) {
  console.info('ℹ️ Info:', message)
  
  if (typeof window !== 'undefined') {
    createToast(message, 'info')
  }
}

/**
 * 创建简单的Toast提示
 */
function createToast(message, type = 'info') {
  // 创建toast容器（如果不存在）
  let container = document.getElementById('toast-container')
  if (!container) {
    container = document.createElement('div')
    container.id = 'toast-container'
    container.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 9999;
      pointer-events: none;
    `
    document.body.appendChild(container)
  }

  // 创建toast元素
  const toast = document.createElement('div')
  toast.style.cssText = `
    background: ${getToastColor(type)};
    color: white;
    padding: 12px 20px;
    border-radius: 6px;
    margin-bottom: 10px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    transform: translateX(100%);
    transition: transform 0.3s ease;
    pointer-events: auto;
    max-width: 300px;
    word-wrap: break-word;
  `
  toast.textContent = message

  // 添加到容器
  container.appendChild(toast)

  // 动画显示
  setTimeout(() => {
    toast.style.transform = 'translateX(0)'
  }, 10)

  // 自动隐藏
  setTimeout(() => {
    toast.style.transform = 'translateX(100%)'
    setTimeout(() => {
      if (container.contains(toast)) {
        container.removeChild(toast)
      }
    }, 300)
  }, 3000)
}

/**
 * 获取Toast颜色
 */
function getToastColor(type) {
  const colors = {
    success: '#28a745',
    error: '#dc3545',
    warning: '#ffc107',
    info: '#17a2b8'
  }
  return colors[type] || colors.info
}

/**
 * 确认对话框
 */
export function showConfirm(message, title = '确认') {
  return new Promise((resolve) => {
    const result = window.confirm(`${title}\n\n${message}`)
    resolve(result)
  })
}

/**
 * 输入对话框
 */
export function showPrompt(message, defaultValue = '') {
  return new Promise((resolve) => {
    const result = window.prompt(message, defaultValue)
    resolve(result)
  })
}

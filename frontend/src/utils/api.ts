import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse, type InternalAxiosRequestConfig } from 'axios'
import NProgress from 'nprogress'
import { getToken, removeToken } from './auth'
import { showError } from './message'
import type { Store } from 'vuex'
import type { Router } from 'vue-router'
import type { ApiResponse } from '@/types'

// 创建axios实例
const api: AxiosInstance = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

export function setupApiInterceptors(store: Store<any>, router: Router) {
  // 请求拦截器
  api.interceptors.request.use(
    (config: InternalAxiosRequestConfig) => {
      NProgress.start()
      
      const token = getToken()
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }
      
      return config
    },
    (error) => {
      NProgress.done()
      return Promise.reject(error)
    }
  )

  // 响应拦截器
  api.interceptors.response.use(
    (response: AxiosResponse<ApiResponse>) => {
      NProgress.done()
      
      const res = response.data
      
      if (res.code && res.code !== 200) {
        showError(res.message || '请求失败')
        
        if (res.code === 401) {
          handleUnauthorized(store, router)
        }
        
        return Promise.reject(new Error(res.message || '请求失败'))
      }
      
      return response
    },
    (error) => {
      NProgress.done()
      
      let message = '网络错误'
      
      if (error.response) {
        const { status, data } = error.response
        
        switch (status) {
          case 400:
            message = data?.message || '请求参数错误'
            break
          case 401:
            message = '未授权，请重新登录'
            handleUnauthorized(store, router)
            break
          case 403:
            message = '拒绝访问'
            break
          case 404:
            message = '请求的资源不存在'
            break
          case 500:
            message = '服务器内部错误'
            break
          default:
            message = data?.message || `请求失败 (${status})`
        }
      } else if (error.request) {
        message = '网络连接失败'
      } else {
        message = error.message || '请求失败'
      }
      
      showError(message)
      return Promise.reject(error)
    }
  )
}

// 处理未授权
function handleUnauthorized(store: Store<any>, router: Router): void {
  // 清除认证信息
  removeToken()
  store.dispatch('auth/logout').finally(() => {
    // 跳转到登录页
    if (router.currentRoute.value.path !== '/login') {
      router.push({
        path: '/login',
        query: { redirect: router.currentRoute.value.fullPath }
      })
    }
  })
}

// 封装常用的请求方法
export const request = {
  get<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return api.get(url, config).then(res => res.data)
  },
  
  post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return api.post(url, data, config).then(res => res.data)
  },
  
  put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return api.put(url, data, config).then(res => res.data)
  },
  
  delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return api.delete(url, config).then(res => res.data)
  },
  
  patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return api.patch(url, data, config).then(res => res.data)
  }
}

// 文件上传
export function uploadFile(url: string, file: File, onProgress?: (progress: number) => void): Promise<ApiResponse> {
  const formData = new FormData()
  formData.append('file', file)
  
  return api.post(url, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress: (progressEvent) => {
      if (onProgress && progressEvent.total) {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        onProgress(progress)
      }
    }
  }).then(res => res.data)
}

// 下载文件
export function downloadFile(url: string, filename?: string): Promise<void> {
  return api.get(url, {
    responseType: 'blob'
  }).then((response) => {
    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename || 'download'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)
  })
}

export default api

const TOKEN_KEY = 'code_combined_token'

// JWT Token 载荷类型
export interface TokenPayload {
  userId: number
  sub: string // username
  role: string
  exp: number
  iat: number
}

// 用户信息类型
export interface TokenUser {
  userId: number
  username: string
  role: string
  exp: number
  iat: number
}

/**
 * 获取Token
 */
export function getToken(): string | null {
  return localStorage.getItem(TOKEN_KEY)
}

/**
 * 设置Token
 */
export function setToken(token: string): void {
  localStorage.setItem(TOKEN_KEY, token)
}

/**
 * 移除Token
 */
export function removeToken(): void {
  localStorage.removeItem(TOKEN_KEY)
}

/**
 * 检查Token是否存在
 */
export function hasToken(): boolean {
  return !!getToken()
}

/**
 * 解析JWT Token
 */
export function parseToken(token: string): TokenPayload | null {
  if (!token) return null
  
  try {
    const base64Url = token.split('.')[1]
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    )
    
    return JSON.parse(jsonPayload) as TokenPayload
  } catch (error) {
    console.error('Token解析失败:', error)
    return null
  }
}

/**
 * 检查Token是否过期
 */
export function isTokenExpired(token: string): boolean {
  const payload = parseToken(token)
  if (!payload || !payload.exp) return true
  
  const currentTime = Math.floor(Date.now() / 1000)
  return payload.exp < currentTime
}

/**
 * 获取Token中的用户信息
 */
export function getUserFromToken(token: string): TokenUser | null {
  const payload = parseToken(token)
  if (!payload) return null
  
  return {
    userId: payload.userId,
    username: payload.sub,
    role: payload.role,
    exp: payload.exp,
    iat: payload.iat
  }
}

/**
 * 检查当前Token是否有效
 */
export function isValidToken(): boolean {
  const token = getToken()
  if (!token) return false
  
  return !isTokenExpired(token)
}

/**
 * 获取Token剩余有效时间（秒）
 */
export function getTokenRemainingTime(): number {
  const token = getToken()
  if (!token) return 0
  
  const payload = parseToken(token)
  if (!payload || !payload.exp) return 0
  
  const currentTime = Math.floor(Date.now() / 1000)
  const remainingTime = payload.exp - currentTime
  
  return Math.max(0, remainingTime)
}

/**
 * 格式化Token剩余时间
 */
export function formatTokenRemainingTime(): string {
  const seconds = getTokenRemainingTime()
  
  if (seconds <= 0) return '已过期'
  
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const remainingSeconds = seconds % 60
  
  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟${remainingSeconds}秒`
  } else {
    return `${remainingSeconds}秒`
  }
}

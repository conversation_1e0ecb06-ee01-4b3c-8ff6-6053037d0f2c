import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import { setupMocks } from './mock' // 导入 mock setup
import { setupApiInterceptors } from './utils/api' // 导入注入函数

// Bootstrap CSS
import 'bootstrap/dist/css/bootstrap.min.css'
import 'bootstrap-icons/font/bootstrap-icons.css'

// Bootstrap JS
import 'bootstrap/dist/js/bootstrap.bundle.min.js'

// 自定义样式
import './assets/css/main.css'

// 进度条
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 类型导入
import type { App as VueApp } from 'vue'

// 配置进度条
NProgress.configure({ 
  showSpinner: false,
  minimum: 0.2,
  speed: 500
})

const app: VueApp = createApp(App)

try {
  console.log('[Init] Setting up Store...');
  app.use(store)
  
  console.log('[Init] Setting up Router...');
  app.use(router)

  console.log('[Init] Setting up API Interceptors...');
  setupApiInterceptors(store, router)

  // ===== MOCK 数据模式 =====
  // 强制启用 mock 数据，无需环境变量判断
  // 所有 API 请求将被 MockAdapter 拦截并返回模拟数据
  // 这确保了前端应用可以完全独立于后端服务运行
  console.log('[Init] 🎭 Setting up Mock Data Mode...');
  setupMocks()
  console.log('[Init] ✅ Mock interceptors activated - all API calls will use mock data');
  
  console.log('[Init] Mounting App...');
  app.mount('#app')
  console.log('✅ Application mounted successfully!');
  
} catch (error) {
  console.error('❌ An error occurred during application initialization:', error);
  // 在页面上显示一个错误信息，以便用户知道出了问题
  const root = document.querySelector('#app');
  if (root) {
    root.innerHTML = `
      <div style="padding: 20px; text-align: center; font-family: sans-serif;">
        <h1>Application failed to start</h1>
        <p>A critical error occurred during initialization. Please check the developer console for details.</p>
        <pre style="background: #fdd; border: 1px solid #f00; padding: 10px; text-align: left; display: inline-block;">${(error as Error).stack}</pre>
      </div>
    `;
  }
}

// 全局属性
app.config.globalProperties.$message = {
  success: (message: string) => {
    console.log('Success:', message)
    // TODO: 实现消息提示组件
  },
  error: (message: string) => {
    console.error('Error:', message)
    // TODO: 实现消息提示组件
  },
  warning: (message: string) => {
    console.warn('Warning:', message)
    // TODO: 实现消息提示组件
  },
  info: (message: string) => {
    console.info('Info:', message)
    // TODO: 实现消息提示组件
  }
}

app.config.globalProperties.$confirm = (options: {
  title?: string
  content: string
  onOk?: () => void | Promise<void>
  onCancel?: () => void
}) => {
  // TODO: 实现确认对话框组件
  const confirmed = window.confirm(`${options.title || '确认'}\n${options.content}`)
  if (confirmed && options.onOk) {
    options.onOk()
  } else if (!confirmed && options.onCancel) {
    options.onCancel()
  }
}

app.config.globalProperties.$loading = {
  show: (message?: string) => {
    NProgress.start()
    console.log('Loading:', message || '加载中...')
  },
  hide: () => {
    NProgress.done()
  }
}

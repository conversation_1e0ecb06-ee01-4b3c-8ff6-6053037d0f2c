import { request } from '@/utils/api'

/**
 * 管理员用户管理API
 * 直接调用后端接口，不使用mock数据
 */

// 获取用户列表
export function getUserList(params) {
  return request.get('/admin/users', { params })
}

// 获取用户详情
export function getUserDetail(userId) {
  return request.get(`/admin/users/${userId}`)
}

// 创建用户
export function createUser(data) {
  return request.post('/admin/users', data)
}

// 更新用户
export function updateUser(userId, data) {
  return request.put(`/admin/users/${userId}`, data)
}

// 删除用户
export function deleteUser(userId) {
  return request.delete(`/admin/users/${userId}`)
}

// 批量删除用户
export function batchDeleteUsers(userIds) {
  return request.delete('/admin/users/batch', { data: userIds })
}

// 启用/禁用用户
export function toggleUserStatus(userId, status) {
  return request.put(`/admin/users/${userId}/status`, null, { params: { status } })
}

// 批量启用/禁用用户
export function batchToggleUserStatus(userIds, status) {
  return request.put('/admin/users/batch/status', userIds, { params: { status } })
}

// 重置用户密码
export function resetUserPassword(userId) {
  return request.put(`/admin/users/${userId}/password/reset`)
}

// 获取用户统计信息
export function getUserStatistics() {
  return request.get('/admin/users/statistics')
}

// 导出用户数据
export function exportUsers(params) {
  return request.get('/admin/users/export', { params })
}

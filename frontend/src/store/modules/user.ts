import api from '@/utils/api'
import type { Module } from 'vuex'
import type { RootState } from '@/store'
import type { User, ApiResponse } from '@/types'

// 用户活动类型
interface UserActivity {
  id: number
  type: string
  description: string
  points: number
  createdTime: string
}

// 用户统计信息
interface UserStats {
  points: number
  problemsSolved: number
  problemSetsCreated: number
  rank: number
}

// 用户偏好设置
interface UserPreferences {
  theme: 'light' | 'dark'
  language: 'zh-CN' | 'en-US'
  emailNotifications: boolean
  pushNotifications: boolean
}

// 社交链接
interface SocialLinks {
  github?: string
  website?: string
  linkedin?: string
  twitter?: string
}

// 扩展的用户信息
interface UserProfile extends User {
  stats?: UserStats
  preferences?: UserPreferences
  socialLinks?: SocialLinks
}

// 定义 user 模块的状态类型
export interface UserState {
  profile: UserProfile | null
  activities: UserActivity[]
  activitiesPagination: {
    current: number
    size: number
    total: number
    pages: number
  }
  loading: boolean
  updating: boolean
}

const state: UserState = {
  profile: null,
  activities: [],
  activitiesPagination: {
    current: 1,
    size: 10,
    total: 0,
    pages: 0
  },
  loading: false,
  updating: false
}

const getters = {
  profile: (state: UserState): UserProfile | null => state.profile,
  activities: (state: UserState): UserActivity[] => state.activities,
  activitiesPagination: (state: UserState): UserState['activitiesPagination'] => state.activitiesPagination,
  loading: (state: UserState): boolean => state.loading,
  updating: (state: UserState): boolean => state.updating,
  
  // 计算属性
  userStats: (state: UserState): UserStats => state.profile?.stats || {} as UserStats,
  userPreferences: (state: UserState): UserPreferences => state.profile?.preferences || {} as UserPreferences,
  socialLinks: (state: UserState): SocialLinks => state.profile?.socialLinks || {},
  
  // 用户等级计算
  userLevel: (state: UserState): number => {
    if (!state.profile?.stats) return 1
    const points = state.profile.stats.points
    return Math.floor(points / 100) + 1
  },
  
  // 下一等级所需积分
  nextLevelPoints: (state: UserState, getters: any): number => {
    return getters.userLevel * 100
  },
  
  // 当前等级进度
  levelProgress: (state: UserState, getters: any): number => {
    if (!state.profile?.stats) return 0
    const points = state.profile.stats.points
    const currentLevelPoints = (getters.userLevel - 1) * 100
    const nextLevelPoints = getters.nextLevelPoints
    return ((points - currentLevelPoints) / (nextLevelPoints - currentLevelPoints)) * 100
  }
}

const mutations = {
  SET_PROFILE(state: UserState, profile: UserProfile | null) {
    state.profile = profile
  },
  SET_ACTIVITIES(state: UserState, activities: UserActivity[]) {
    state.activities = activities
  },
  SET_ACTIVITIES_PAGINATION(state: UserState, pagination: Partial<UserState['activitiesPagination']>) {
    state.activitiesPagination = { ...state.activitiesPagination, ...pagination }
  },
  SET_LOADING(state: UserState, loading: boolean) {
    state.loading = loading
  },
  SET_UPDATING(state: UserState, updating: boolean) {
    state.updating = updating
  },
  UPDATE_PROFILE_FIELD(state: UserState, { field, value }: { field: keyof UserProfile; value: any }) {
    if (state.profile) {
      (state.profile as any)[field] = value
    }
  },
  UPDATE_PREFERENCES(state: UserState, preferences: Partial<UserPreferences>) {
    if (state.profile) {
      state.profile.preferences = { ...state.profile.preferences, ...preferences }
    }
  },
  UPDATE_SOCIAL_LINKS(state: UserState, socialLinks: Partial<SocialLinks>) {
    if (state.profile) {
      state.profile.socialLinks = { ...state.profile.socialLinks, ...socialLinks }
    }
  }
}

const actions = {
  async fetchProfile({ commit }: any, userId?: number): Promise<ApiResponse<UserProfile>> {
    commit('SET_LOADING', true)
    try {
      const url = userId ? `/users/${userId}` : '/user/profile'
      const response = await api.get<UserProfile>(url)
      commit('SET_PROFILE', response.data)
      return response
    } finally {
      commit('SET_LOADING', false)
    }
  },

  async updateProfile({ commit }: any, profileData: Partial<UserProfile>): Promise<ApiResponse<UserProfile>> {
    commit('SET_UPDATING', true)
    try {
      const response = await api.put<UserProfile>('/user/profile', profileData)
      commit('SET_PROFILE', response.data)
      return response
    } finally {
      commit('SET_UPDATING', false)
    }
  },

  async updateAvatar({ commit }: any, avatarFile: File): Promise<ApiResponse<{ avatarUrl: string }>> {
    commit('SET_UPDATING', true)
    try {
      const formData = new FormData()
      formData.append('avatar', avatarFile)
      
      const response = await api.post<{ avatarUrl: string }>('/user/avatar', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      })
      
      commit('UPDATE_PROFILE_FIELD', { field: 'avatar', value: response.data.avatarUrl })
      return response
    } finally {
      commit('SET_UPDATING', false)
    }
  },

  async updatePreferences({ commit }: any, preferences: Partial<UserPreferences>): Promise<ApiResponse<UserPreferences>> {
    commit('SET_UPDATING', true)
    try {
      const response = await api.put<UserPreferences>('/user/preferences', preferences)
      commit('UPDATE_PREFERENCES', response.data)
      return response
    } finally {
      commit('SET_UPDATING', false)
    }
  },

  async updateSocialLinks({ commit }: any, socialLinks: Partial<SocialLinks>): Promise<ApiResponse<SocialLinks>> {
    commit('SET_UPDATING', true)
    try {
      const response = await api.put<SocialLinks>('/user/social-links', socialLinks)
      commit('UPDATE_SOCIAL_LINKS', response.data)
      return response
    } finally {
      commit('SET_UPDATING', false)
    }
  },

  async fetchActivities({ commit }: any, params: { page?: number; size?: number } = {}): Promise<ApiResponse<{ records: UserActivity[]; total: number; current: number; size: number; pages: number }>> {
    commit('SET_LOADING', true)
    try {
      const response = await api.get<{ records: UserActivity[]; total: number; current: number; size: number; pages: number }>('/user/activities', { params })
      commit('SET_ACTIVITIES', response.data.records)
      commit('SET_ACTIVITIES_PAGINATION', {
        current: response.data.current,
        size: response.data.size,
        total: response.data.total,
        pages: response.data.pages
      })
      return response
    } finally {
      commit('SET_LOADING', false)
    }
  },

  async changePassword({ commit }: any, { oldPassword, newPassword }: { oldPassword: string; newPassword: string }): Promise<ApiResponse<void>> {
    commit('SET_UPDATING', true)
    try {
      const response = await api.put<void>('/user/password', { oldPassword, newPassword })
      return response
    } finally {
      commit('SET_UPDATING', false)
    }
  },

  async deleteAccount({ commit }: any, password: string): Promise<ApiResponse<void>> {
    commit('SET_UPDATING', true)
    try {
      const response = await api.delete<void>('/user/account', { data: { password } })
      commit('SET_PROFILE', null)
      return response
    } finally {
      commit('SET_UPDATING', false)
    }
  },

  clearProfile({ commit }: any): void {
    commit('SET_PROFILE', null)
    commit('SET_ACTIVITIES', [])
    commit('SET_ACTIVITIES_PAGINATION', {
      current: 1,
      size: 10,
      total: 0,
      pages: 0
    })
  }
}

const userModule: Module<UserState, RootState> = {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}

export default userModule

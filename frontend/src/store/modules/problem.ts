import api from '@/utils/api'
import type { Module } from 'vuex'
import type { RootState } from '@/store'
import type { Problem, PaginationParams, PaginationResponse, ApiResponse, ProblemCreateRequest } from '@/types'

// 定义 problem 模块的状态类型
export interface ProblemState {
  problems: Problem[]
  currentProblem: Problem | null
  loading: boolean
  pagination: {
    current: number
    size: number
    total: number
  }
}

const state: ProblemState = {
  problems: [],
  currentProblem: null,
  loading: false,
  pagination: {
    current: 1,
    size: 10,
    total: 0
  }
}

const mutations = {
  SET_PROBLEMS(state: ProblemState, problems: Problem[]) {
    state.problems = problems
  },
  SET_CURRENT_PROBLEM(state: ProblemState, problem: Problem | null) {
    state.currentProblem = problem
  },
  SET_LOADING(state: ProblemState, loading: boolean) {
    state.loading = loading
  },
  SET_PAGINATION(state: ProblemState, pagination: Partial<ProblemState['pagination']>) {
    state.pagination = { ...state.pagination, ...pagination }
  }
}

const actions = {
  async fetchProblems({ commit }: any, params: PaginationParams = { page: 1, size: 10 }): Promise<ApiResponse<PaginationResponse<Problem>>> {
    commit('SET_LOADING', true)
    try {
      const response = await api.get<PaginationResponse<Problem>>('/problems', { params })
      commit('SET_PROBLEMS', response.data.records)
      commit('SET_PAGINATION', {
        current: response.data.current,
        size: response.data.size,
        total: response.data.total
      })
      return response
    } finally {
      commit('SET_LOADING', false)
    }
  },

  async fetchProblemById({ commit }: any, id: number): Promise<Problem> {
    commit('SET_LOADING', true)
    try {
      const response = await api.get<Problem>(`/problems/${id}`)
      commit('SET_CURRENT_PROBLEM', response.data)
      return response.data
    } finally {
      commit('SET_LOADING', false)
    }
  },

  async createProblem({ commit }: any, problemData: ProblemCreateRequest): Promise<ApiResponse<Problem>> {
    commit('SET_LOADING', true)
    try {
      const response = await api.post<Problem>('/problems', problemData)
      return response
    } finally {
      commit('SET_LOADING', false)
    }
  },

  async updateProblem({ commit }: any, { id, data }: { id: number; data: Partial<Problem> }): Promise<ApiResponse<Problem>> {
    commit('SET_LOADING', true)
    try {
      const response = await api.put<Problem>(`/problems/${id}`, data)
      commit('SET_CURRENT_PROBLEM', response.data)
      return response
    } finally {
      commit('SET_LOADING', false)
    }
  },

  async deleteProblem({ commit }: any, id: number): Promise<ApiResponse<void>> {
    commit('SET_LOADING', true)
    try {
      const response = await api.delete<void>(`/problems/${id}`)
      return response
    } finally {
      commit('SET_LOADING', false)
    }
  },

  async searchProblems({ commit }: any, { keyword, difficulty, tags }: { keyword?: string; difficulty?: string; tags?: string }): Promise<ApiResponse<Problem[]>> {
    commit('SET_LOADING', true)
    try {
      const params: any = {}
      if (keyword) params.keyword = keyword
      if (difficulty) params.difficulty = difficulty
      if (tags) params.tags = tags

      const response = await api.get<Problem[]>('/problems/search', { params })
      commit('SET_PROBLEMS', response.data)
      return response
    } finally {
      commit('SET_LOADING', false)
    }
  },

  async incrementViewCount({ commit }: any, id: number): Promise<ApiResponse<void>> {
    try {
      const response = await api.post<void>(`/problems/${id}/view`)
      return response
    } catch (error) {
      console.error('增加浏览次数失败:', error)
      throw error
    }
  },

  async likeProblem({ commit }: any, id: number): Promise<ApiResponse<void>> {
    try {
      const response = await api.post<void>(`/problems/${id}/like`)
      return response
    } catch (error) {
      console.error('点赞失败:', error)
      throw error
    }
  },

  async unlikeProblem({ commit }: any, id: number): Promise<ApiResponse<void>> {
    try {
      const response = await api.delete<void>(`/problems/${id}/like`)
      return response
    } catch (error) {
      console.error('取消点赞失败:', error)
      throw error
    }
  },

  clearCurrentProblem({ commit }: any): void {
    commit('SET_CURRENT_PROBLEM', null)
  }
}

const getters = {
  problems: (state: ProblemState): Problem[] => state.problems,
  currentProblem: (state: ProblemState): Problem | null => state.currentProblem,
  loading: (state: ProblemState): boolean => state.loading,
  pagination: (state: ProblemState): ProblemState['pagination'] => state.pagination,
  totalProblems: (state: ProblemState): number => state.pagination.total,
  hasMoreProblems: (state: ProblemState): boolean => {
    const { current, size, total } = state.pagination
    return current * size < total
  },
  problemsByDifficulty: (state: ProblemState) => (difficulty: string): Problem[] => {
    return state.problems.filter((problem: Problem) => problem.difficulty === difficulty)
  },
  problemsByTag: (state: ProblemState) => (tag: string): Problem[] => {
    return state.problems.filter((problem: Problem) => 
      problem.tags && problem.tags.toLowerCase().includes(tag.toLowerCase())
    )
  }
}

const problemModule: Module<ProblemState, RootState> = {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}

export default problemModule

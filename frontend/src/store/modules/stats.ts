import api from '@/utils/api'
import type { Module } from 'vuex'
import type { RootState } from '@/store'
import type { ApiResponse, UserStatistics, ProblemStatistics, ProblemSetStatistics } from '@/types'

// 平台统计信息
interface PlatformStats {
  totalUsers: number
  onlineUsers: number
  totalProblems: number
  totalProblemSets: number
  totalSubmissions: number
  totalSolutions: number
}

// 实时统计信息
interface RealtimeStats {
  activeUsers: number
  todayRegistrations: number
  todaySubmissions: number
  todayProblems: number
}

// 趋势数据
interface TrendData {
  date: string
  users: number
  problems: number
  submissions: number
}

// 定义 stats 模块的状态类型
export interface StatsState {
  // 基础统计
  onlineUsers: number
  totalUsers: number
  totalProblemSets: number
  totalProblems: number
  
  // 详细统计
  userStats: UserStatistics | null
  problemStats: ProblemStatistics | null
  problemSetStats: ProblemSetStatistics | null
  platformStats: PlatformStats | null
  realtimeStats: RealtimeStats | null
  
  // 趋势数据
  userTrend: TrendData[]
  problemTrend: TrendData[]
  submissionTrend: TrendData[]
  
  // 状态
  loading: boolean
  lastUpdated: string | null
}

const state: StatsState = {
  onlineUsers: 0,
  totalUsers: 0,
  totalProblemSets: 0,
  totalProblems: 0,
  userStats: null,
  problemStats: null,
  problemSetStats: null,
  platformStats: null,
  realtimeStats: null,
  userTrend: [],
  problemTrend: [],
  submissionTrend: [],
  loading: false,
  lastUpdated: null
}

const mutations = {
  SET_USER_STATS(state: StatsState, { onlineUsers, totalUsers }: { onlineUsers: number; totalUsers: number }) {
    state.onlineUsers = onlineUsers
    state.totalUsers = totalUsers
  },
  SET_PLATFORM_STATS(state: StatsState, { totalProblemSets, totalProblems }: { totalProblemSets: number; totalProblems: number }) {
    state.totalProblemSets = totalProblemSets
    state.totalProblems = totalProblems
  },
  SET_DETAILED_USER_STATS(state: StatsState, userStats: UserStatistics) {
    state.userStats = userStats
  },
  SET_DETAILED_PROBLEM_STATS(state: StatsState, problemStats: ProblemStatistics) {
    state.problemStats = problemStats
  },
  SET_DETAILED_PROBLEM_SET_STATS(state: StatsState, problemSetStats: ProblemSetStatistics) {
    state.problemSetStats = problemSetStats
  },
  SET_DETAILED_PLATFORM_STATS(state: StatsState, platformStats: PlatformStats) {
    state.platformStats = platformStats
  },
  SET_REALTIME_STATS(state: StatsState, realtimeStats: RealtimeStats) {
    state.realtimeStats = realtimeStats
  },
  SET_USER_TREND(state: StatsState, trend: TrendData[]) {
    state.userTrend = trend
  },
  SET_PROBLEM_TREND(state: StatsState, trend: TrendData[]) {
    state.problemTrend = trend
  },
  SET_SUBMISSION_TREND(state: StatsState, trend: TrendData[]) {
    state.submissionTrend = trend
  },
  SET_LOADING(state: StatsState, loading: boolean) {
    state.loading = loading
  },
  SET_LAST_UPDATED(state: StatsState, timestamp: string) {
    state.lastUpdated = timestamp
  }
}

const actions = {
  async fetchUserStats({ commit }: any): Promise<ApiResponse<{ onlineUsers: number; totalUsers: number }>> {
    try {
      const response = await api.get<{ onlineUsers: number; totalUsers: number }>('/stats/public/users')
      commit('SET_USER_STATS', response.data)
      return response
    } catch (error) {
      console.error('获取用户统计失败:', error)
      throw error
    }
  },

  async fetchPlatformStats({ commit }: any): Promise<ApiResponse<{ totalProblemSets: number; totalProblems: number }>> {
    try {
      const response = await api.get<{ totalProblemSets: number; totalProblems: number }>('/stats/public/platform')
      commit('SET_PLATFORM_STATS', response.data)
      return response
    } catch (error) {
      console.error('获取平台统计失败:', error)
      throw error
    }
  },

  async fetchDetailedUserStats({ commit }: any): Promise<ApiResponse<UserStatistics>> {
    commit('SET_LOADING', true)
    try {
      const response = await api.get<UserStatistics>('/admin/users/statistics')
      commit('SET_DETAILED_USER_STATS', response.data)
      return response
    } finally {
      commit('SET_LOADING', false)
    }
  },

  async fetchDetailedProblemStats({ commit }: any): Promise<ApiResponse<ProblemStatistics>> {
    commit('SET_LOADING', true)
    try {
      const response = await api.get<ProblemStatistics>('/admin/problems/statistics')
      commit('SET_DETAILED_PROBLEM_STATS', response.data)
      return response
    } finally {
      commit('SET_LOADING', false)
    }
  },

  async fetchDetailedProblemSetStats({ commit }: any): Promise<ApiResponse<ProblemSetStatistics>> {
    commit('SET_LOADING', true)
    try {
      const response = await api.get<ProblemSetStatistics>('/admin/problem-sets/statistics')
      commit('SET_DETAILED_PROBLEM_SET_STATS', response.data)
      return response
    } finally {
      commit('SET_LOADING', false)
    }
  },

  async fetchDetailedPlatformStats({ commit }: any): Promise<ApiResponse<PlatformStats>> {
    commit('SET_LOADING', true)
    try {
      const response = await api.get<PlatformStats>('/admin/stats/platform')
      commit('SET_DETAILED_PLATFORM_STATS', response.data)
      return response
    } finally {
      commit('SET_LOADING', false)
    }
  },

  async fetchRealtimeStats({ commit }: any): Promise<ApiResponse<RealtimeStats>> {
    try {
      const response = await api.get<RealtimeStats>('/admin/stats/realtime')
      commit('SET_REALTIME_STATS', response.data)
      return response
    } catch (error) {
      console.error('获取实时统计失败:', error)
      throw error
    }
  },

  async fetchUserTrend({ commit }: any, days: number = 30): Promise<ApiResponse<TrendData[]>> {
    try {
      const response = await api.get<TrendData[]>(`/admin/stats/trend/users?days=${days}`)
      commit('SET_USER_TREND', response.data)
      return response
    } catch (error) {
      console.error('获取用户趋势失败:', error)
      throw error
    }
  },

  async fetchProblemTrend({ commit }: any, days: number = 30): Promise<ApiResponse<TrendData[]>> {
    try {
      const response = await api.get<TrendData[]>(`/admin/stats/trend/problems?days=${days}`)
      commit('SET_PROBLEM_TREND', response.data)
      return response
    } catch (error) {
      console.error('获取题目趋势失败:', error)
      throw error
    }
  },

  async fetchSubmissionTrend({ commit }: any, days: number = 30): Promise<ApiResponse<TrendData[]>> {
    try {
      const response = await api.get<TrendData[]>(`/admin/stats/trend/submissions?days=${days}`)
      commit('SET_SUBMISSION_TREND', response.data)
      return response
    } catch (error) {
      console.error('获取提交趋势失败:', error)
      throw error
    }
  },

  async fetchAllStats({ dispatch }: any): Promise<void> {
    try {
      await Promise.all([
        dispatch('fetchUserStats'),
        dispatch('fetchPlatformStats'),
        dispatch('fetchDetailedUserStats'),
        dispatch('fetchDetailedProblemStats'),
        dispatch('fetchDetailedProblemSetStats'),
        dispatch('fetchRealtimeStats')
      ])
    } catch (error) {
      console.error('获取统计数据失败:', error)
      throw error
    }
  },

  updateLastUpdated({ commit }: any): void {
    commit('SET_LAST_UPDATED', new Date().toISOString())
  }
}

const getters = {
  onlineUsers: (state: StatsState): number => state.onlineUsers,
  totalUsers: (state: StatsState): number => state.totalUsers,
  totalProblemSets: (state: StatsState): number => state.totalProblemSets,
  totalProblems: (state: StatsState): number => state.totalProblems,
  userStats: (state: StatsState): UserStatistics | null => state.userStats,
  problemStats: (state: StatsState): ProblemStatistics | null => state.problemStats,
  problemSetStats: (state: StatsState): ProblemSetStatistics | null => state.problemSetStats,
  platformStats: (state: StatsState): PlatformStats | null => state.platformStats,
  realtimeStats: (state: StatsState): RealtimeStats | null => state.realtimeStats,
  userTrend: (state: StatsState): TrendData[] => state.userTrend,
  problemTrend: (state: StatsState): TrendData[] => state.problemTrend,
  submissionTrend: (state: StatsState): TrendData[] => state.submissionTrend,
  loading: (state: StatsState): boolean => state.loading,
  lastUpdated: (state: StatsState): string | null => state.lastUpdated,
  
  // 计算属性
  userGrowthRate: (state: StatsState): number => {
    if (!state.userStats) return 0
    const { totalUsers, weekNewUsers } = state.userStats
    return totalUsers > 0 ? (weekNewUsers / totalUsers) * 100 : 0
  },
  
  problemSolveRate: (state: StatsState): number => {
    if (!state.problemStats) return 0
    const { totalSubmissions, totalAccepted } = state.problemStats
    return totalSubmissions > 0 ? (totalAccepted / totalSubmissions) * 100 : 0
  },
  
  averageProblemsPerSet: (state: StatsState): number => {
    if (!state.problemSetStats) return 0
    const { total, avgProblemCount } = state.problemSetStats
    return total > 0 ? avgProblemCount : 0
  }
}

const statsModule: Module<StatsState, RootState> = {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}

export default statsModule

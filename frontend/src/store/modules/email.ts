import api from '@/utils/api'
import type { Module } from 'vuex'
import type { RootState } from '@/store'
import type { ApiResponse } from '@/types'

// 邮件模板接口
interface EmailTemplate {
  id: number
  name: string
  type: 'verification' | 'notification' | 'welcome' | 'reset'
  subject: string
  content: string
  description?: string
  variables?: string[]
  isActive: boolean
  createdTime: string
  updatedTime: string
}

// 邮件发送记录接口
interface EmailRecord {
  id: number
  templateId?: number
  templateName?: string
  recipient: string
  subject: string
  content: string
  status: 'pending' | 'sent' | 'failed' | 'bounced'
  errorMessage?: string
  sentTime?: string
  createdTime: string
}

// 邮件配置接口
interface EmailSettings {
  smtpHost: string
  smtpPort: number
  smtpUsername: string
  smtpPassword: string
  fromName: string
  fromEmail: string
  enableSsl: boolean
  enableAuth: boolean
  maxRetries: number
  retryInterval: number
}

// 邮件统计接口
interface EmailStats {
  totalSent: number
  totalFailed: number
  successRate: number
  templateCount: number
  todaySent: number
  weekSent: number
  monthSent: number
  averageDeliveryTime: number
}

// 邮件发送请求接口
interface SendEmailRequest {
  to: string | string[]
  subject: string
  content: string
  templateId?: number
  variables?: Record<string, any>
  priority?: 'low' | 'normal' | 'high'
  scheduleTime?: string
}

// 定义 email 模块的状态类型
export interface EmailState {
  templates: EmailTemplate[]
  records: EmailRecord[]
  settings: EmailSettings | null
  stats: EmailStats | null
  loading: boolean
  sending: boolean
  pagination: {
    current: number
    size: number
    total: number
  }
}

const state: EmailState = {
  templates: [],
  records: [],
  settings: null,
  stats: null,
  loading: false,
  sending: false,
  pagination: {
    current: 1,
    size: 20,
    total: 0
  }
}

const mutations = {
  SET_TEMPLATES(state: EmailState, templates: EmailTemplate[]) {
    state.templates = templates
  },
  SET_RECORDS(state: EmailState, records: EmailRecord[]) {
    state.records = records
  },
  SET_SETTINGS(state: EmailState, settings: EmailSettings) {
    state.settings = settings
  },
  SET_STATS(state: EmailState, stats: EmailStats) {
    state.stats = stats
  },
  SET_LOADING(state: EmailState, loading: boolean) {
    state.loading = loading
  },
  SET_SENDING(state: EmailState, sending: boolean) {
    state.sending = sending
  },
  SET_PAGINATION(state: EmailState, pagination: Partial<EmailState['pagination']>) {
    state.pagination = { ...state.pagination, ...pagination }
  },
  ADD_TEMPLATE(state: EmailState, template: EmailTemplate) {
    state.templates.unshift(template)
  },
  UPDATE_TEMPLATE(state: EmailState, updatedTemplate: EmailTemplate) {
    const index = state.templates.findIndex(t => t.id === updatedTemplate.id)
    if (index !== -1) {
      state.templates.splice(index, 1, updatedTemplate)
    }
  },
  REMOVE_TEMPLATE(state: EmailState, templateId: number) {
    state.templates = state.templates.filter(t => t.id !== templateId)
  },
  ADD_RECORD(state: EmailState, record: EmailRecord) {
    state.records.unshift(record)
  }
}

const actions = {
  // 获取邮件模板列表
  async getTemplates({ commit }: any, params: { page?: number; size?: number; type?: string } = {}): Promise<ApiResponse<EmailTemplate[]>> {
    commit('SET_LOADING', true)
    try {
      const response = await api.get<EmailTemplate[]>('/admin/email/templates', { params })
      commit('SET_TEMPLATES', response.data)
      return response
    } finally {
      commit('SET_LOADING', false)
    }
  },

  // 创建邮件模板
  async createTemplate({ commit }: any, templateData: Partial<EmailTemplate>): Promise<ApiResponse<EmailTemplate>> {
    commit('SET_LOADING', true)
    try {
      const response = await api.post<EmailTemplate>('/admin/email/templates', templateData)
      commit('ADD_TEMPLATE', response.data)
      return response
    } finally {
      commit('SET_LOADING', false)
    }
  },

  // 更新邮件模板
  async updateTemplate({ commit }: any, { id, ...templateData }: Partial<EmailTemplate> & { id: number }): Promise<ApiResponse<EmailTemplate>> {
    commit('SET_LOADING', true)
    try {
      const response = await api.put<EmailTemplate>(`/admin/email/templates/${id}`, templateData)
      commit('UPDATE_TEMPLATE', response.data)
      return response
    } finally {
      commit('SET_LOADING', false)
    }
  },

  // 删除邮件模板
  async deleteTemplate({ commit }: any, templateId: number): Promise<ApiResponse<void>> {
    commit('SET_LOADING', true)
    try {
      const response = await api.delete<void>(`/admin/email/templates/${templateId}`)
      commit('REMOVE_TEMPLATE', templateId)
      return response
    } finally {
      commit('SET_LOADING', false)
    }
  },

  // 复制邮件模板
  async duplicateTemplate({ commit }: any, templateId: number): Promise<ApiResponse<EmailTemplate>> {
    commit('SET_LOADING', true)
    try {
      const response = await api.post<EmailTemplate>(`/admin/email/templates/${templateId}/duplicate`)
      commit('ADD_TEMPLATE', response.data)
      return response
    } finally {
      commit('SET_LOADING', false)
    }
  },

  // 获取邮件发送记录
  async getEmailHistory({ commit }: any, params: { page?: number; size?: number; status?: string; startTime?: string; endTime?: string } = {}): Promise<ApiResponse<{ records: EmailRecord[]; total: number }>> {
    commit('SET_LOADING', true)
    try {
      const response = await api.get<{ records: EmailRecord[]; total: number }>('/admin/email/records', { params })
      commit('SET_RECORDS', response.data.records)
      commit('SET_PAGINATION', {
        current: params.page || 1,
        size: params.size || 20,
        total: response.data.total
      })
      return response
    } finally {
      commit('SET_LOADING', false)
    }
  },

  // 发送邮件
  async sendEmail({ commit }: any, emailData: SendEmailRequest): Promise<ApiResponse<EmailRecord>> {
    commit('SET_SENDING', true)
    try {
      const response = await api.post<EmailRecord>('/admin/email/send', emailData)
      commit('ADD_RECORD', response.data)
      return response
    } finally {
      commit('SET_SENDING', false)
    }
  },

  // 发送测试邮件
  async sendTestEmail({ commit }: any, { template, recipient }: { template: string; recipient?: string }): Promise<ApiResponse<void>> {
    commit('SET_SENDING', true)
    try {
      const response = await api.post<void>('/admin/email/test', {
        template,
        recipient: recipient || '<EMAIL>'
      })
      return response
    } finally {
      commit('SET_SENDING', false)
    }
  },

  // 获取邮件配置
  async getSettings({ commit }: any): Promise<ApiResponse<EmailSettings>> {
    commit('SET_LOADING', true)
    try {
      const response = await api.get<EmailSettings>('/admin/email/settings')
      commit('SET_SETTINGS', response.data)
      return response
    } finally {
      commit('SET_LOADING', false)
    }
  },

  // 更新邮件配置
  async updateSettings({ commit }: any, settings: EmailSettings): Promise<ApiResponse<EmailSettings>> {
    commit('SET_LOADING', true)
    try {
      const response = await api.put<EmailSettings>('/admin/email/settings', settings)
      commit('SET_SETTINGS', response.data)
      return response
    } finally {
      commit('SET_LOADING', false)
    }
  },

  // 测试邮件连接
  async testConnection({ commit }: any, settings: EmailSettings): Promise<ApiResponse<{ success: boolean; message: string }>> {
    commit('SET_LOADING', true)
    try {
      const response = await api.post<{ success: boolean; message: string }>('/admin/email/test-connection', settings)
      return response
    } finally {
      commit('SET_LOADING', false)
    }
  },

  // 获取邮件统计
  async getStats({ commit }: any): Promise<ApiResponse<EmailStats>> {
    try {
      const response = await api.get<EmailStats>('/admin/email/stats')
      commit('SET_STATS', response.data)
      return response
    } catch (error) {
      console.error('获取邮件统计失败:', error)
      throw error
    }
  },

  // 重新发送失败的邮件
  async resendEmail({ commit }: any, recordId: number): Promise<ApiResponse<EmailRecord>> {
    commit('SET_SENDING', true)
    try {
      const response = await api.post<EmailRecord>(`/admin/email/records/${recordId}/resend`)
      return response
    } finally {
      commit('SET_SENDING', false)
    }
  },

  // 批量发送邮件
  async batchSendEmail({ commit }: any, { recipients, templateId, variables }: { recipients: string[]; templateId: number; variables?: Record<string, any> }): Promise<ApiResponse<{ success: number; failed: number; records: EmailRecord[] }>> {
    commit('SET_SENDING', true)
    try {
      const response = await api.post<{ success: number; failed: number; records: EmailRecord[] }>('/admin/email/batch-send', {
        recipients,
        templateId,
        variables
      })
      return response
    } finally {
      commit('SET_SENDING', false)
    }
  },

  // 预览邮件模板
  async previewTemplate({ commit }: any, { templateId, variables }: { templateId: number; variables?: Record<string, any> }): Promise<ApiResponse<{ subject: string; content: string }>> {
    try {
      const response = await api.post<{ subject: string; content: string }>(`/admin/email/templates/${templateId}/preview`, { variables })
      return response
    } catch (error) {
      console.error('预览邮件模板失败:', error)
      throw error
    }
  },

  // 获取邮件模板变量
  async getTemplateVariables({ commit }: any, templateId: number): Promise<ApiResponse<string[]>> {
    try {
      const response = await api.get<string[]>(`/admin/email/templates/${templateId}/variables`)
      return response
    } catch (error) {
      console.error('获取模板变量失败:', error)
      throw error
    }
  }
}

const getters = {
  templates: (state: EmailState): EmailTemplate[] => state.templates,
  records: (state: EmailState): EmailRecord[] => state.records,
  settings: (state: EmailState): EmailSettings | null => state.settings,
  stats: (state: EmailState): EmailStats | null => state.stats,
  loading: (state: EmailState): boolean => state.loading,
  sending: (state: EmailState): boolean => state.sending,
  pagination: (state: EmailState): EmailState['pagination'] => state.pagination,
  
  // 按类型分组的模板
  templatesByType: (state: EmailState) => (type: string): EmailTemplate[] => {
    return state.templates.filter(template => template.type === type)
  },
  
  // 活跃的模板
  activeTemplates: (state: EmailState): EmailTemplate[] => {
    return state.templates.filter(template => template.isActive)
  },
  
  // 最近的发送记录
  recentRecords: (state: EmailState): EmailRecord[] => {
    return state.records.slice(0, 10)
  },
  
  // 发送成功率
  successRate: (state: EmailState): number => {
    if (state.records.length === 0) return 0
    const successCount = state.records.filter(record => record.status === 'sent').length
    return Math.round((successCount / state.records.length) * 100)
  },
  
  // 今日发送数量
  todaySentCount: (state: EmailState): number => {
    const today = new Date().toDateString()
    return state.records.filter(record => {
      return record.sentTime && new Date(record.sentTime).toDateString() === today
    }).length
  }
}

const emailModule: Module<EmailState, RootState> = {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}

export default emailModule

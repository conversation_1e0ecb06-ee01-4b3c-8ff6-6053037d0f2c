import api from '@/utils/api'
import type { Module } from 'vuex'
import type { RootState } from '@/store'
import type { ProblemSet, PaginationParams, PaginationResponse, ApiResponse } from '@/types'

// 定义 problemset 模块的状态类型
export interface ProblemSetState {
  problemSets: ProblemSet[]
  recentProblemSets: ProblemSet[]
  currentProblemSet: ProblemSet | null
  loading: boolean
  pagination: {
    current: number
    size: number
    total: number
  }
}

const state: ProblemSetState = {
  problemSets: [],
  recentProblemSets: [],
  currentProblemSet: null,
  loading: false,
  pagination: {
    current: 1,
    size: 10,
    total: 0
  }
}

const mutations = {
  SET_PROBLEM_SETS(state: ProblemSetState, problemSets: ProblemSet[]) {
    state.problemSets = problemSets
  },
  SET_RECENT_PROBLEM_SETS(state: ProblemSetState, problemSets: ProblemSet[]) {
    state.recentProblemSets = problemSets
  },
  SET_CURRENT_PROBLEM_SET(state: ProblemSetState, problemSet: ProblemSet | null) {
    state.currentProblemSet = problemSet
  },
  SET_LOADING(state: ProblemSetState, loading: boolean) {
    state.loading = loading
  },
  SET_PAGINATION(state: ProblemSetState, pagination: Partial<ProblemSetState['pagination']>) {
    state.pagination = { ...state.pagination, ...pagination }
  }
}

const actions = {
  async fetchProblemSets({ commit }: any, params: PaginationParams = { page: 1, size: 10 }): Promise<ApiResponse<PaginationResponse<ProblemSet>>> {
    commit('SET_LOADING', true)
    try {
      const response = await api.get<PaginationResponse<ProblemSet>>('/problemsets', { params })
      commit('SET_PROBLEM_SETS', response.data.records)
      commit('SET_PAGINATION', {
        current: response.data.current,
        size: response.data.size,
        total: response.data.total
      })
      return response
    } finally {
      commit('SET_LOADING', false)
    }
  },

  async fetchRecentProblemSets({ commit }: any): Promise<ApiResponse<ProblemSet[]>> {
    try {
      const response = await api.get<ProblemSet[]>('/problemsets/recent')
      commit('SET_RECENT_PROBLEM_SETS', response.data)
      return response
    } catch (error) {
      console.error('获取最近题集失败:', error)
      throw error
    }
  },

  async fetchProblemSetById({ commit }: any, id: number): Promise<ApiResponse<ProblemSet>> {
    commit('SET_LOADING', true)
    try {
      const response = await api.get<ProblemSet>(`/problemsets/${id}`)
      commit('SET_CURRENT_PROBLEM_SET', response.data)
      return response
    } finally {
      commit('SET_LOADING', false)
    }
  },

  async createProblemSet({ commit }: any, problemSetData: Partial<ProblemSet>): Promise<ApiResponse<ProblemSet>> {
    commit('SET_LOADING', true)
    try {
      const response = await api.post<ProblemSet>('/problemsets', problemSetData)
      return response
    } finally {
      commit('SET_LOADING', false)
    }
  },

  async updateProblemSet({ commit }: any, { id, data }: { id: number; data: Partial<ProblemSet> }): Promise<ApiResponse<ProblemSet>> {
    commit('SET_LOADING', true)
    try {
      const response = await api.put<ProblemSet>(`/problemsets/${id}`, data)
      commit('SET_CURRENT_PROBLEM_SET', response.data)
      return response
    } finally {
      commit('SET_LOADING', false)
    }
  },

  async deleteProblemSet({ commit }: any, id: number): Promise<ApiResponse<void>> {
    commit('SET_LOADING', true)
    try {
      const response = await api.delete<void>(`/problemsets/${id}`)
      return response
    } finally {
      commit('SET_LOADING', false)
    }
  },

  async addProblemToProblemSet({ commit }: any, { problemSetId, problemId }: { problemSetId: string | number; problemId: number }): Promise<ApiResponse<void>> {
    try {
      const response = await api.post<void>(`/problemsets/${problemSetId}/problems`, { problemId })
      return response
    } catch (error) {
      console.error('添加题目到题集失败:', error)
      throw error
    }
  },

  async removeProblemFromProblemSet({ commit }: any, { problemSetId, problemId }: { problemSetId: string | number; problemId: number }): Promise<ApiResponse<void>> {
    try {
      const response = await api.delete<void>(`/problemsets/${problemSetId}/problems/${problemId}`)
      return response
    } catch (error) {
      console.error('从题集移除题目失败:', error)
      throw error
    }
  },

  async fetchProblemSetProblems({ commit }: any, problemSetId: number): Promise<ApiResponse<any[]>> {
    try {
      const response = await api.get<any[]>(`/problemsets/${problemSetId}/problems`)
      return response
    } catch (error) {
      console.error('获取题集题目失败:', error)
      throw error
    }
  },

  clearCurrentProblemSet({ commit }: any): void {
    commit('SET_CURRENT_PROBLEM_SET', null)
  }
}

const getters = {
  problemSets: (state: ProblemSetState): ProblemSet[] => state.problemSets,
  recentProblemSets: (state: ProblemSetState): ProblemSet[] => state.recentProblemSets,
  currentProblemSet: (state: ProblemSetState): ProblemSet | null => state.currentProblemSet,
  loading: (state: ProblemSetState): boolean => state.loading,
  pagination: (state: ProblemSetState): ProblemSetState['pagination'] => state.pagination,
  totalProblemSets: (state: ProblemSetState): number => state.pagination.total,
  hasMoreProblemSets: (state: ProblemSetState): boolean => {
    const { current, size, total } = state.pagination
    return current * size < total
  }
}

const problemsetModule: Module<ProblemSetState, RootState> = {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}

export default problemsetModule

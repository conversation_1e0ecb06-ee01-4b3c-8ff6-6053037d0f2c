/// <reference types="vuex" />
import { request } from '@/utils/api'
import { removeToken, setToken, getToken } from '@/utils/auth'
import type { Module } from 'vuex'
import type { RootState } from '@/store'
import type { User, LoginRequest, RegisterRequest, ApiResponse } from '@/types'

// 定义 auth 模块的状态类型
export interface AuthState {
  token: string | null
  user: User | null
}

const state: AuthState = {
  token: getToken(),
  user: null,
}

const mutations = {
  SET_TOKEN(state: AuthState, token: string | null) {
    state.token = token
  },
  SET_USER(state: AuthState, user: User | null) {
    state.user = user
  },
  CLEAR_AUTH(state: AuthState) {
    state.token = null
    state.user = null
  }
}

const actions = {
  // 登录
  async login({ commit }: { commit: Function }, loginData: LoginRequest): Promise<User> {
    const response = await request.post<{ token: string; user: User }>('/auth/login', loginData)
    const { token, user } = response.data

    commit('SET_TOKEN', token)
      commit('SET_USER', user)
    setToken(token)

    return user
  },

  // 直接设置认证信息（用于快速登录）
  async setAuth({ commit }: { commit: Function }, { token, user }: { token: string; user: User }) {
    commit('SET_TOKEN', token)
    commit('SET_USER', user)
    setToken(token)
  },

  // 注册
  async register({ commit }: { commit: Function }, registerData: RegisterRequest): Promise<ApiResponse<null>> {
    return await request.post<null>('/auth/register', registerData)
  },

  // 发送邮件验证码
  async sendEmailCode({ commit }: { commit: Function }, { email, type }: { email: string; type: 'register' | 'reset' | 'change' }): Promise<ApiResponse<null>> {
    return await request.post<null>('/auth/send-email-code', { email, type })
  },

  // 验证邮件验证码
  async verifyEmailCode({ commit }: { commit: Function }, { email, code, type }: { email: string; code: string; type: string }): Promise<ApiResponse<boolean>> {
    return await request.post<boolean>('/auth/verify-email-code', { email, code, type })
  },

  // 检查用户名可用性
  async checkUsername({ commit }: { commit: Function }, username: string): Promise<ApiResponse<{ available: boolean }>> {
    return await request.get<{ available: boolean }>(`/auth/check-username?username=${encodeURIComponent(username)}`)
  },

  // 检查邮箱可用性
  async checkEmail({ commit }: { commit: Function }, email: string): Promise<ApiResponse<{ available: boolean }>> {
    return await request.get<{ available: boolean }>(`/auth/check-email?email=${encodeURIComponent(email)}`)
  },

  // 登出
  async logout({ commit, state }: { commit: Function; state: AuthState }): Promise<void> {
    if (state.token) {
      try {
        await request.post('/auth/logout')
      } catch (error) {
        console.error('Logout API call failed, proceeding with client-side logout.', error)
      }
    }
    commit('CLEAR_AUTH')
    removeToken()
  },

  // 获取用户信息
  async getUserInfo({ commit, state }: { commit: Function; state: AuthState }): Promise<User | null> {
    if (!state.token) return null
    if (state.user) return state.user
    
    try {
      const response = await request.get<User>('/users/me')
      commit('SET_USER', response.data)
      return response.data
    } catch (error) {
      commit('CLEAR_AUTH')
      removeToken()
      return null
    }
  },

  // 刷新Token
  async refreshToken({ commit, state }: { commit: Function; state: AuthState }): Promise<string | null> {
    if (!state.token) return null
    
    try {
      const response = await request.post<{ token: string }>('/auth/refresh', { token: state.token })
      const { token } = response.data
      commit('SET_TOKEN', token)
      setToken(token)
      return token
    } catch (error) {
      commit('CLEAR_AUTH')
      removeToken()
      throw error
    }
  },

  // 更新用户信息
  async updateProfile({ commit }: { commit: Function }, profileData: Partial<User>): Promise<User> {
    const response = await request.put<User>('/user/profile', profileData)
    commit('SET_USER', response.data)
    return response.data
  },

  // 修改密码
  async changePassword({ commit }: { commit: Function }, passwordData: { oldPassword: string; newPassword: string }): Promise<ApiResponse<null>> {
    return await request.put<null>('/user/password', passwordData)
  },

  // 重置密码
  async resetPassword({ commit }: { commit: Function }, resetData: { email: string; code: string; newPassword: string }): Promise<ApiResponse<null>> {
    return await request.post<null>('/auth/reset-password', resetData)
  }
}

const getters = {
  isAuthenticated: (state: AuthState): boolean => !!state.token && !!state.user,
  currentUser: (state: AuthState): User | null => state.user,
  userRole: (state: AuthState): string | null => state.user?.role || null,
  isAdmin: (state: AuthState): boolean => state.user?.role === 'ADMIN'
}

const authModule: Module<AuthState, RootState> = {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}

export default authModule

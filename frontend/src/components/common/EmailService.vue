<template>
  <div class="email-service">
    <!-- 发送验证码组件 -->
    <div v-if="type === 'verification'" class="verification-code">
      <div class="input-group">
        <input
          v-model="email"
          type="email"
          class="form-control"
          :class="{ 'is-invalid': emailError }"
          placeholder="请输入邮箱地址"
          :disabled="disabled"
          @blur="validateEmail"
        />
        <button
          class="btn btn-outline-primary"
          type="button"
          :disabled="!canSendCode || sending"
          @click="sendCode"
        >
          <span v-if="sending" class="spinner-border spinner-border-sm me-1"></span>
          {{ codeButtonText }}
        </button>
      </div>
      <div v-if="emailError" class="invalid-feedback d-block">
        {{ emailError }}
      </div>
      <div v-if="successMessage" class="text-success small mt-1">
        {{ successMessage }}
      </div>
    </div>

    <!-- 邮件模板预览组件 -->
    <div v-if="type === 'preview'" class="email-preview">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h6 class="mb-0">
            <i class="bi bi-envelope me-2"></i>
            邮件预览
          </h6>
          <button class="btn btn-sm btn-outline-primary" @click="sendTestEmail">
            <i class="bi bi-send me-1"></i>
            发送测试邮件
          </button>
        </div>
        <div class="card-body">
          <div class="email-content" v-html="emailContent"></div>
        </div>
      </div>
    </div>

    <!-- 邮件发送状态组件 -->
    <div v-if="type === 'status'" class="email-status">
      <div class="d-flex align-items-center">
        <div class="status-icon me-3">
          <i v-if="status === 'sending'" class="bi bi-arrow-clockwise spin text-primary"></i>
          <i v-else-if="status === 'success'" class="bi bi-check-circle-fill text-success"></i>
          <i v-else-if="status === 'error'" class="bi bi-x-circle-fill text-danger"></i>
          <i v-else class="bi bi-envelope text-muted"></i>
        </div>
        <div class="status-content">
          <div class="status-title">{{ statusTitle }}</div>
          <div class="status-message text-muted small">{{ statusMessage }}</div>
        </div>
      </div>
    </div>

    <!-- 邮件历史记录组件 -->
    <div v-if="type === 'history'" class="email-history">
      <div class="d-flex justify-content-between align-items-center mb-3">
        <h6 class="mb-0">邮件发送记录</h6>
        <button class="btn btn-sm btn-outline-secondary" @click="refreshHistory">
          <i class="bi bi-arrow-clockwise me-1"></i>
          刷新
        </button>
      </div>
      <div v-if="loading" class="text-center py-3">
        <div class="spinner-border spinner-border-sm"></div>
        <span class="ms-2">加载中...</span>
      </div>
      <div v-else-if="emailHistory.length === 0" class="text-center py-3 text-muted">
        暂无邮件发送记录
      </div>
      <div v-else class="email-history-list">
        <div
          v-for="record in emailHistory"
          :key="record.id"
          class="email-record border-bottom py-2"
        >
          <div class="d-flex justify-content-between align-items-start">
            <div class="record-info">
              <div class="record-title">{{ record.subject }}</div>
              <div class="record-meta text-muted small">
                收件人: {{ record.recipient }} | 
                发送时间: {{ formatTime(record.sentTime) }}
              </div>
            </div>
            <div class="record-status">
              <span
                class="badge"
                :class="{
                  'bg-success': record.status === 'sent',
                  'bg-danger': record.status === 'failed',
                  'bg-warning': record.status === 'pending'
                }"
              >
                {{ getStatusText(record.status) }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { showSuccess, showError } from '@/utils/message'

// 定义组件名称
defineOptions({
  name: 'EmailService'
})

// 定义 Props 接口
interface Props {
  type: 'verification' | 'preview' | 'status' | 'history'
  modelValue?: string
  disabled?: boolean
  codeType?: 'register' | 'reset' | 'change'
  template?: string
  status?: 'idle' | 'sending' | 'success' | 'error'
}

// 定义 Emits 接口
interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'code-sent', data: { email: string; type: string }): void
  (e: 'test-sent', success: boolean): void
}

// 邮件记录接口
interface EmailRecord {
  id: number
  subject: string
  recipient: string
  status: 'sent' | 'failed' | 'pending'
  sentTime: string
}

const props = withDefaults(defineProps<Props>(), {
  type: 'verification',
  modelValue: '',
  disabled: false,
  codeType: 'register',
  template: '',
  status: 'idle'
})

const emit = defineEmits<Emits>()
const store = useStore()

// 响应式数据
const email = ref<string>(props.modelValue)
const emailError = ref<string>('')
const sending = ref<boolean>(false)
const countdown = ref<number>(0)
const successMessage = ref<string>('')
const loading = ref<boolean>(false)
const emailHistory = ref<EmailRecord[]>([])

// 计算属性
const canSendCode = computed((): boolean => {
  return email.value && 
         /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email.value) && 
         countdown.value === 0 && 
         !props.disabled
})

const codeButtonText = computed((): string => {
  if (sending.value) return '发送中...'
  if (countdown.value > 0) return `${countdown.value}s后重发`
  return '发送验证码'
})

const emailContent = computed((): string => {
  if (props.template) {
    return props.template
  }
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <div style="background: #f8f9fa; padding: 20px; text-align: center;">
        <h2 style="color: #333; margin: 0;">Code-Combined</h2>
      </div>
      <div style="padding: 30px 20px;">
        <h3 style="color: #333;">邮箱验证码</h3>
        <p>您好，</p>
        <p>您的验证码是：<strong style="color: #007bff; font-size: 18px;">123456</strong></p>
        <p>验证码有效期为10分钟，请及时使用。</p>
        <p>如果这不是您的操作，请忽略此邮件。</p>
      </div>
      <div style="background: #f8f9fa; padding: 20px; text-align: center; color: #666;">
        <p style="margin: 0;">此邮件由系统自动发送，请勿回复。</p>
      </div>
    </div>
  `
})

const statusTitle = computed((): string => {
  switch (props.status) {
    case 'sending': return '正在发送邮件...'
    case 'success': return '邮件发送成功'
    case 'error': return '邮件发送失败'
    default: return '准备发送邮件'
  }
})

const statusMessage = computed((): string => {
  switch (props.status) {
    case 'sending': return '请稍候，邮件正在发送中'
    case 'success': return '邮件已成功发送到目标邮箱'
    case 'error': return '邮件发送失败，请检查邮箱地址或稍后重试'
    default: return '点击发送按钮开始发送邮件'
  }
})

// 方法
const validateEmail = (): void => {
  emailError.value = ''
  if (!email.value) {
    emailError.value = '请输入邮箱地址'
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email.value)) {
    emailError.value = '邮箱格式不正确'
  }
  
  emit('update:modelValue', email.value)
}

const sendCode = async (): Promise<void> => {
  if (!canSendCode.value) return

  validateEmail()
  if (emailError.value) return

  sending.value = true
  successMessage.value = ''

  try {
    await store.dispatch('auth/sendEmailCode', {
      email: email.value,
      type: props.codeType
    })

    successMessage.value = '验证码已发送到您的邮箱'
    showSuccess('验证码发送成功')

    // 开始倒计时
    countdown.value = 60
    const timer = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(timer)
      }
    }, 1000)

    emit('code-sent', {
      email: email.value,
      type: props.codeType
    })
  } catch (error) {
    emailError.value = (error as Error).message || '发送失败'
    showError('验证码发送失败')
  } finally {
    sending.value = false
  }
}

const sendTestEmail = async (): Promise<void> => {
  try {
    await store.dispatch('email/sendTestEmail', {
      template: emailContent.value
    })
    showSuccess('测试邮件发送成功')
    emit('test-sent', true)
  } catch (error) {
    showError('测试邮件发送失败')
    emit('test-sent', false)
  }
}

const refreshHistory = async (): Promise<void> => {
  loading.value = true
  try {
    const response = await store.dispatch('email/getEmailHistory')
    emailHistory.value = response.data
  } catch (error) {
    showError('获取邮件记录失败')
  } finally {
    loading.value = false
  }
}

const formatTime = (time: string): string => {
  return new Date(time).toLocaleString('zh-CN')
}

const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    sent: '已发送',
    failed: '发送失败',
    pending: '发送中'
  }
  return statusMap[status] || status
}

// 生命周期
onMounted(() => {
  if (props.type === 'history') {
    refreshHistory()
  }
})
</script>

<style scoped>
.email-service {
  width: 100%;
}

.verification-code .input-group {
  margin-bottom: 0.5rem;
}

.email-preview .email-content {
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  padding: 1rem;
  background: #fff;
  min-height: 200px;
}

.status-icon {
  font-size: 1.5rem;
}

.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.email-record {
  padding: 0.75rem 0;
}

.email-record:last-child {
  border-bottom: none !important;
}

.record-title {
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.record-meta {
  font-size: 0.875rem;
}
</style>

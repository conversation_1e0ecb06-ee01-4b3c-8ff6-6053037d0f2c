<template>
  <div class="modal fade show" style="display: block;" tabindex="-1">
    <div class="modal-dialog modal-xl">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">题目详情</h5>
          <button type="button" class="btn-close" @click="$emit('close')"></button>
        </div>
        <div class="modal-body">
          <div class="row">
            <!-- 题目信息 -->
            <div class="col-md-8">
              <div class="card">
                <div class="card-header">
                  <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ problem.title }}</h5>
                    <div>
                      <span 
                        class="badge me-2" 
                        :class="getDifficultyClass(problem.difficulty)"
                      >
                        {{ getDifficultyText(problem.difficulty) }}
                      </span>
                      <span 
                        class="badge" 
                        :class="getStatusClass(problem.status)"
                      >
                        {{ getStatusText(problem.status) }}
                      </span>
                    </div>
                  </div>
                </div>
                <div class="card-body">
                  <!-- 题目描述 -->
                  <div class="mb-4">
                    <h6 class="text-primary">题目描述</h6>
                    <div class="border-start border-primary border-3 ps-3">
                      <p class="mb-0">{{ problem.description }}</p>
                    </div>
                  </div>

                  <!-- 输入输出说明 -->
                  <div class="row mb-4">
                    <div class="col-md-6">
                      <h6 class="text-primary">输入说明</h6>
                      <div class="bg-light p-3 rounded">
                        <p class="mb-0">{{ problem.inputDescription || '无特殊说明' }}</p>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <h6 class="text-primary">输出说明</h6>
                      <div class="bg-light p-3 rounded">
                        <p class="mb-0">{{ problem.outputDescription || '无特殊说明' }}</p>
                      </div>
                    </div>
                  </div>

                  <!-- 示例 -->
                  <div class="mb-4">
                    <h6 class="text-primary">示例</h6>
                    <div v-for="(example, index) in examples" :key="index" class="border rounded p-3 mb-3">
                      <strong>示例 {{ index + 1 }}</strong>
                      <div class="row mt-2">
                        <div class="col-md-6">
                          <label class="form-label small fw-bold">输入：</label>
                          <pre class="bg-light p-2 rounded"><code>{{ example.input }}</code></pre>
                        </div>
                        <div class="col-md-6">
                          <label class="form-label small fw-bold">输出：</label>
                          <pre class="bg-light p-2 rounded"><code>{{ example.output }}</code></pre>
                        </div>
                      </div>
                      <div v-if="example.explanation" class="mt-2">
                        <label class="form-label small fw-bold">解释：</label>
                        <p class="mb-0">{{ example.explanation }}</p>
                      </div>
                    </div>
                  </div>

                  <!-- 约束条件 -->
                  <div v-if="problem.constraints" class="mb-4">
                    <h6 class="text-primary">约束条件</h6>
                    <div class="bg-warning bg-opacity-10 border border-warning rounded p-3">
                      <pre class="mb-0">{{ problem.constraints }}</pre>
                    </div>
                  </div>

                  <!-- 提示 -->
                  <div v-if="problem.hint" class="mb-4">
                    <h6 class="text-primary">提示</h6>
                    <div class="bg-info bg-opacity-10 border border-info rounded p-3">
                      <p class="mb-0">{{ problem.hint }}</p>
                    </div>
                  </div>

                  <!-- 标签 -->
                  <div v-if="problem.tags" class="mb-4">
                    <h6 class="text-primary">标签</h6>
                    <div>
                      <span 
                        v-for="tag in problem.tags.split(',')" 
                        :key="tag"
                        class="badge bg-light text-dark me-1 mb-1"
                      >
                        {{ tag.trim() }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 题目统计和设置 -->
            <div class="col-md-4">
              <!-- 统计信息 -->
              <div class="card mb-3">
                <div class="card-header">
                  <h6 class="mb-0">统计信息</h6>
                </div>
                <div class="card-body">
                  <div class="row text-center">
                    <div class="col-6 mb-3">
                      <div class="h5 text-primary">{{ problem.viewCount || 0 }}</div>
                      <small class="text-muted">浏览次数</small>
                    </div>
                    <div class="col-6 mb-3">
                      <div class="h5 text-success">{{ problem.submissionCount || 0 }}</div>
                      <small class="text-muted">提交次数</small>
                    </div>
                    <div class="col-6">
                      <div class="h5 text-info">{{ problem.acceptedCount || 0 }}</div>
                      <small class="text-muted">通过次数</small>
                    </div>
                    <div class="col-6">
                      <div class="h5 text-warning">{{ acceptanceRate }}%</div>
                      <small class="text-muted">通过率</small>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 技术设置 -->
              <div class="card mb-3">
                <div class="card-header">
                  <h6 class="mb-0">技术设置</h6>
                </div>
                <div class="card-body">
                  <div class="mb-2">
                    <strong>时间限制：</strong>
                    <span class="text-muted">{{ problem.timeLimit || 1000 }} ms</span>
                  </div>
                  <div class="mb-2">
                    <strong>内存限制：</strong>
                    <span class="text-muted">{{ problem.memoryLimit || 256 }} MB</span>
                  </div>
                  <div class="mb-2">
                    <strong>支持语言：</strong>
                    <div class="mt-1">
                      <span 
                        v-for="lang in supportedLanguages" 
                        :key="lang"
                        class="badge bg-secondary me-1 mb-1"
                      >
                        {{ getLanguageName(lang) }}
                      </span>
                    </div>
                  </div>
                  <div class="mb-2">
                    <strong>创建者：</strong>
                    <span class="text-muted">{{ problem.creatorName || '未知' }}</span>
                  </div>
                  <div class="mb-2">
                    <strong>创建时间：</strong>
                    <span class="text-muted">{{ formatDate(problem.createdTime) }}</span>
                  </div>
                </div>
              </div>

              <!-- 功能设置 -->
              <div class="card mb-3">
                <div class="card-header">
                  <h6 class="mb-0">功能设置</h6>
                </div>
                <div class="card-body">
                  <div class="form-check mb-2">
                    <input 
                      class="form-check-input" 
                      type="checkbox" 
                      :checked="problem.allowDiscussion"
                      disabled
                    >
                    <label class="form-check-label">
                      允许讨论
                    </label>
                  </div>
                  <div class="form-check mb-2">
                    <input 
                      class="form-check-input" 
                      type="checkbox" 
                      :checked="problem.showTestCases"
                      disabled
                    >
                    <label class="form-check-label">
                      显示测试用例
                    </label>
                  </div>
                </div>
              </div>

              <!-- 最近提交 -->
              <div class="card">
                <div class="card-header">
                  <h6 class="mb-0">最近提交</h6>
                </div>
                <div class="card-body">
                  <div v-if="recentSubmissions.length === 0" class="text-center text-muted py-3">
                    <i class="bi bi-inbox"></i>
                    <p class="mb-0 small">暂无提交记录</p>
                  </div>
                  <div v-else>
                    <div 
                      v-for="submission in recentSubmissions" 
                      :key="submission.id"
                      class="d-flex justify-content-between align-items-center mb-2 pb-2 border-bottom"
                    >
                      <div>
                        <div class="small fw-bold">{{ submission.username }}</div>
                        <div class="small text-muted">{{ submission.language }}</div>
                      </div>
                      <div class="text-end">
                        <span 
                          class="badge" 
                          :class="submission.status === 'ACCEPTED' ? 'bg-success' : 'bg-danger'"
                        >
                          {{ submission.status === 'ACCEPTED' ? '通过' : '失败' }}
                        </span>
                        <div class="small text-muted">{{ formatTime(submission.submitTime) }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" @click="$emit('close')">
            关闭
          </button>
          <button type="button" class="btn btn-primary" @click="editProblem">
            编辑题目
          </button>
        </div>
      </div>
    </div>
  </div>
  <div class="modal-backdrop fade show"></div>
</template>

<script>
export default {
  name: 'ProblemDetailModal',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    problem: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      recentSubmissions: []
    }
  },
  computed: {
    examples() {
      return this.problem.examples || [
        {
          input: '示例输入',
          output: '示例输出',
          explanation: '示例解释'
        }
      ]
    },
    supportedLanguages() {
      return this.problem.supportedLanguages || ['python', 'java', 'cpp']
    },
    acceptanceRate() {
      if (!this.problem.submissionCount || this.problem.submissionCount === 0) return 0
      return Math.round((this.problem.acceptedCount / this.problem.submissionCount) * 100)
    }
  },
  methods: {
    getDifficultyClass(difficulty) {
      const classes = {
        EASY: 'bg-success',
        MEDIUM: 'bg-warning',
        HARD: 'bg-danger'
      }
      return classes[difficulty] || 'bg-secondary'
    },
    getDifficultyText(difficulty) {
      const texts = {
        EASY: '简单',
        MEDIUM: '中等',
        HARD: '困难'
      }
      return texts[difficulty] || '未知'
    },
    getStatusClass(status) {
      const classes = {
        DRAFT: 'bg-secondary',
        PUBLISHED: 'bg-success',
        ARCHIVED: 'bg-warning'
      }
      return classes[status] || 'bg-secondary'
    },
    getStatusText(status) {
      const texts = {
        DRAFT: '草稿',
        PUBLISHED: '已发布',
        ARCHIVED: '已归档'
      }
      return texts[status] || '未知'
    },
    getLanguageName(lang) {
      const names = {
        python: 'Python',
        java: 'Java',
        cpp: 'C++',
        c: 'C',
        javascript: 'JavaScript',
        go: 'Go',
        rust: 'Rust',
        kotlin: 'Kotlin'
      }
      return names[lang] || lang
    },
    formatDate(date) {
      if (!date) return '-'
      return new Date(date).toLocaleDateString('zh-CN')
    },
    formatTime(time) {
      const now = new Date()
      const diff = now - time
      const minutes = Math.floor(diff / (1000 * 60))
      const hours = Math.floor(diff / (1000 * 60 * 60))
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))

      if (minutes < 60) {
        return `${minutes}分钟前`
      } else if (hours < 24) {
        return `${hours}小时前`
      } else {
        return `${days}天前`
      }
    },
    editProblem() {
      this.$emit('close')
      this.$emit('edit', this.problem)
    },
    loadRecentSubmissions() {
      // Mock 最近提交数据
      this.recentSubmissions = [
        {
          id: 1,
          username: '张三',
          language: 'Python',
          status: 'ACCEPTED',
          submitTime: new Date(Date.now() - 30 * 60 * 1000)
        },
        {
          id: 2,
          username: '李四',
          language: 'Java',
          status: 'WRONG_ANSWER',
          submitTime: new Date(Date.now() - 2 * 60 * 60 * 1000)
        },
        {
          id: 3,
          username: '王五',
          language: 'C++',
          status: 'ACCEPTED',
          submitTime: new Date(Date.now() - 4 * 60 * 60 * 1000)
        }
      ]
    }
  },
  mounted() {
    this.loadRecentSubmissions()
  }
}
</script>

<style scoped>
.modal {
  background-color: rgba(0, 0, 0, 0.5);
}

.btn-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
  color: #000;
  text-shadow: 0 1px 0 #fff;
  opacity: 0.5;
}

.btn-close:hover {
  opacity: 0.75;
}

pre {
  font-size: 0.9rem;
  margin-bottom: 0;
}

.border-start {
  border-left-width: 3px !important;
}

.badge {
  font-size: 0.75rem;
}
</style>

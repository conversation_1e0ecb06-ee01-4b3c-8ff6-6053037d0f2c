<template>
  <div class="admin-users">
    <!-- 页面标题和操作 -->
    <div class="page-header d-flex justify-content-between align-items-center mb-4">
      <div>
        <h2>用户管理</h2>
        <p class="text-muted">管理系统中的所有用户账户</p>
      </div>
      <div>
        <button class="btn btn-primary" @click="showCreateModal = true">
          <i class="bi bi-person-plus"></i>
          创建用户
        </button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="row mb-4">
      <div class="col-md-3">
        <div class="card bg-primary text-white">
          <div class="card-body">
            <div class="d-flex justify-content-between">
              <div>
                <h4 class="mb-0">{{ statistics.totalUsers || 0 }}</h4>
                <p class="mb-0">总用户数</p>
              </div>
              <div class="align-self-center">
                <i class="bi bi-people fs-2"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card bg-success text-white">
          <div class="card-body">
            <div class="d-flex justify-content-between">
              <div>
                <h4 class="mb-0">{{ statistics.activeUsers || 0 }}</h4>
                <p class="mb-0">活跃用户</p>
              </div>
              <div class="align-self-center">
                <i class="bi bi-person-check fs-2"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card bg-info text-white">
          <div class="card-body">
            <div class="d-flex justify-content-between">
              <div>
                <h4 class="mb-0">{{ statistics.roleStatistics?.admin || 0 }}</h4>
                <p class="mb-0">管理员</p>
              </div>
              <div class="align-self-center">
                <i class="bi bi-shield-check fs-2"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card bg-warning text-white">
          <div class="card-body">
            <div class="d-flex justify-content-between">
              <div>
                <h4 class="mb-0">{{ statistics.recentRegistrations || 0 }}</h4>
                <p class="mb-0">本周新增</p>
              </div>
              <div class="align-self-center">
                <i class="bi bi-person-plus fs-2"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="card shadow mb-4">
      <div class="card-body">
        <div class="row">
          <div class="col-md-4">
            <div class="form-group">
              <label>搜索用户</label>
              <div class="input-group">
                <input
                  type="text"
                  class="form-control"
                  placeholder="用户名、邮箱或昵称"
                  v-model="searchQuery"
                  @input="debounceSearch"
                >
                <div class="input-group-append">
                  <span class="input-group-text">
                    <i class="bi bi-search"></i>
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label>角色筛选</label>
              <select class="form-control" v-model="roleFilter">
                <option value="">全部角色</option>
                <option value="admin">管理员</option>
                <option value="user">普通用户</option>
              </select>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label>状态筛选</label>
              <select class="form-control" v-model="statusFilter">
                <option value="">全部状态</option>
                <option value="1">启用</option>
                <option value="0">禁用</option>
              </select>
            </div>
          </div>
          <div class="col-md-4">
            <div class="form-group">
              <label>注册时间</label>
              <div class="row">
                <div class="col-6">
                  <input type="date" class="form-control" v-model="dateFrom">
                </div>
                <div class="col-6">
                  <input type="date" class="form-control" v-model="dateTo">
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="row mt-3">
          <div class="col-md-12">
            <button class="btn btn-outline-secondary me-2" @click="resetFilters">
              <i class="bi bi-arrow-clockwise"></i>
              重置筛选
            </button>
            <button class="btn btn-success me-2" @click="exportUsers">
              <i class="bi bi-download"></i>
              导出用户
            </button>
            <button
              class="btn btn-warning me-2"
              @click="batchToggleStatus(1)"
              :disabled="selectedUsers.length === 0"
            >
              <i class="bi bi-check-circle"></i>
              批量启用 ({{ selectedUsers.length }})
            </button>
            <button
              class="btn btn-secondary me-2"
              @click="batchToggleStatus(0)"
              :disabled="selectedUsers.length === 0"
            >
              <i class="bi bi-x-circle"></i>
              批量禁用 ({{ selectedUsers.length }})
            </button>
            <button
              class="btn btn-danger"
              @click="batchDeleteUsers"
              :disabled="selectedUsers.length === 0"
            >
              <i class="bi bi-trash"></i>
              批量删除 ({{ selectedUsers.length }})
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 用户列表 -->
    <div class="card shadow">
      <div class="card-header py-3">
        <div class="d-flex justify-content-between align-items-center">
          <h6 class="m-0 font-weight-bold text-primary">
            用户列表 ({{ filteredUsers.length }})
          </h6>
          <div class="btn-group" role="group">
            <button class="btn btn-sm btn-outline-secondary" @click="exportUsers">
              <i class="bi bi-download"></i>
              导出
            </button>
            <button class="btn btn-sm btn-outline-secondary" @click="refreshUsers">
              <i class="bi bi-arrow-clockwise"></i>
              刷新
            </button>
          </div>
        </div>
      </div>
      <div class="card-body">
        <div class="table-responsive">
          <table class="table table-bordered table-hover">
            <thead class="thead-light">
              <tr>
                <th>
                  <input type="checkbox" @change="toggleSelectAll" :checked="isAllSelected">
                </th>
                <th>用户信息</th>
                <th>角色</th>
                <th>状态</th>
                <th>积分</th>
                <th>解题数</th>
                <th>注册时间</th>
                <th>最后登录</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="user in paginatedUsers" :key="user.id">
                <td>
                  <input 
                    type="checkbox" 
                    :value="user.id" 
                    v-model="selectedUsers"
                  >
                </td>
                <td>
                  <div class="d-flex align-items-center">
                    <img 
                      :src="user.avatar || '/default-avatar.png'" 
                      class="rounded-circle me-3" 
                      width="40" 
                      height="40"
                      :alt="user.nickname"
                    >
                    <div>
                      <div class="fw-bold">{{ user.nickname || user.username }}</div>
                      <div class="text-muted small">{{ user.email }}</div>
                    </div>
                  </div>
                </td>
                <td>
                  <span 
                    class="badge" 
                    :class="user.role === 'admin' ? 'bg-danger' : 'bg-primary'"
                  >
                    {{ user.role === 'admin' ? '管理员' : '普通用户' }}
                  </span>
                </td>
                <td>
                  <span 
                    class="badge" 
                    :class="getStatusClass(user.status)"
                  >
                    {{ getStatusText(user.status) }}
                  </span>
                </td>
                <td>
                  <span class="badge bg-success">{{ (user.points || 0).toLocaleString() }}</span>
                </td>
                <td>
                  <span class="badge bg-info">{{ user.solvedCount || 0 }}</span>
                </td>
                <td>{{ formatDate(user.createTime) }}</td>
                <td>{{ formatDate(user.lastLoginTime) }}</td>
                <td>
                  <div class="btn-group" role="group">
                    <button 
                      class="btn btn-sm btn-outline-primary" 
                      @click="editUser(user)"
                      title="编辑"
                    >
                      <i class="bi bi-pencil"></i>
                    </button>
                    <button 
                      class="btn btn-sm btn-outline-info" 
                      @click="viewUser(user)"
                      title="查看详情"
                    >
                      <i class="bi bi-eye"></i>
                    </button>
                    <button
                      class="btn btn-sm"
                      :class="user.status === 0 ? 'btn-outline-success' : 'btn-outline-warning'"
                      @click="toggleUserStatus(user)"
                      :title="user.status === 0 ? '启用' : '禁用'"
                    >
                      <i :class="user.status === 0 ? 'bi bi-unlock' : 'bi bi-lock'"></i>
                    </button>
                    <button
                      class="btn btn-sm btn-outline-secondary"
                      @click="resetUserPassword(user)"
                      title="重置密码"
                    >
                      <i class="bi bi-key"></i>
                    </button>
                    <button 
                      class="btn btn-sm btn-outline-danger" 
                      @click="deleteUser(user)"
                      title="删除"
                    >
                      <i class="bi bi-trash"></i>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 分页 -->
        <nav aria-label="用户列表分页">
          <ul class="pagination justify-content-center">
            <li class="page-item" :class="{ disabled: currentPage === 1 }">
              <a class="page-link" @click="changePage(currentPage - 1)">上一页</a>
            </li>
            <li 
              v-for="page in visiblePages" 
              :key="page"
              class="page-item" 
              :class="{ active: page === currentPage }"
            >
              <a class="page-link" @click="changePage(page)">{{ page }}</a>
            </li>
            <li class="page-item" :class="{ disabled: currentPage === totalPages }">
              <a class="page-link" @click="changePage(currentPage + 1)">下一页</a>
            </li>
          </ul>
        </nav>
      </div>
    </div>

    <!-- 批量操作 -->
    <div v-if="selectedUsers.length > 0" class="fixed-bottom-actions">
      <div class="card shadow">
        <div class="card-body py-2">
          <div class="d-flex justify-content-between align-items-center">
            <span>已选择 {{ selectedUsers.length }} 个用户</span>
            <div class="btn-group">
              <button class="btn btn-sm btn-warning" @click="batchBan">
                <i class="bi bi-lock"></i>
                批量封禁
              </button>
              <button class="btn btn-sm btn-success" @click="batchUnban">
                <i class="bi bi-unlock"></i>
                批量解封
              </button>
              <button class="btn btn-sm btn-danger" @click="batchDelete">
                <i class="bi bi-trash"></i>
                批量删除
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建/编辑用户模态框 -->
    <UserModal 
      v-if="showCreateModal || showEditModal"
      :show="showCreateModal || showEditModal"
      :user="editingUser"
      :is-edit="showEditModal"
      @close="closeModal"
      @save="saveUser"
    />

    <!-- 用户详情模态框 -->
    <UserDetailModal 
      v-if="showDetailModal"
      :show="showDetailModal"
      :user="viewingUser"
      @close="showDetailModal = false"
    />
  </div>
</template>

<script>
import UserModal from './UserModal.vue'
import UserDetailModal from './UserDetailModal.vue'
import * as userAPI from '@/api/admin/users'

export default {
  name: 'AdminUsers',
  components: {
    UserModal,
    UserDetailModal
  },
  data() {
    return {
      users: [],
      total: 0,
      loading: false,
      statistics: {
        totalUsers: 0,
        activeUsers: 0,
        roleStatistics: {
          admin: 0,
          user: 0
        },
        statusStatistics: {
          enabled: 0,
          disabled: 0
        },
        recentRegistrations: 0
      },
      searchQuery: '',
      roleFilter: '',
      statusFilter: '',
      dateFrom: '',
      dateTo: '',
      selectedUsers: [],
      currentPage: 1,
      pageSize: 10,
      showCreateModal: false,
      showEditModal: false,
      showDetailModal: false,
      editingUser: null,
      viewingUser: null,
      searchTimeout: null
    }
  },
  computed: {
    paginatedUsers() {
      // 直接返回从API获取的用户列表，因为后端已经处理了分页和筛选
      return this.users
    },
    totalPages() {
      return Math.ceil(this.total / this.pageSize)
    },
    visiblePages() {
      const pages = []
      const start = Math.max(1, this.currentPage - 2)
      const end = Math.min(this.totalPages, this.currentPage + 2)
      
      for (let i = start; i <= end; i++) {
        pages.push(i)
      }
      return pages
    },
    isAllSelected() {
      return this.selectedUsers.length === this.paginatedUsers.length && this.paginatedUsers.length > 0
    }
  },
  methods: {
    async loadUsers() {
      try {
        this.loading = true

        const params = {
          current: this.currentPage,
          size: this.pageSize,
          keyword: this.searchQuery,
          role: this.roleFilter,
          status: this.statusFilter,
          startTime: this.dateFrom,
          endTime: this.dateTo
        }

        // 直接调用后端API
        const response = await userAPI.getUserList(params)

        if (response.code === 200) {
          this.users = response.data.records || []
          this.total = response.data.total || 0
        } else {
          this.$toast.error(response.message || '加载用户列表失败')
        }
      } catch (error) {
        console.error('加载用户列表失败:', error)
        this.$toast.error('加载用户列表失败')
      } finally {
        this.loading = false
      }
    },

    async loadStatistics() {
      try {
        const response = await userAPI.getUserStatistics()

        if (response.code === 200) {
          this.statistics = response.data
        }
      } catch (error) {
        console.error('加载统计数据失败:', error)
      }
    },

    handleSearch() {
      this.currentPage = 1
    },
    handleFilter() {
      this.currentPage = 1
    },
    changePage(page) {
      if (page >= 1 && page <= this.totalPages) {
        this.currentPage = page
      }
    },
    toggleSelectAll() {
      if (this.isAllSelected) {
        this.selectedUsers = []
      } else {
        this.selectedUsers = this.paginatedUsers.map(user => user.id)
      }
    },
    editUser(user) {
      this.editingUser = { ...user }
      this.showEditModal = true
    },
    viewUser(user) {
      this.viewingUser = user
      this.showDetailModal = true
    },
    deleteUser(user) {
      if (confirm(`确定要删除用户 ${user.nickname || user.username} 吗？`)) {
        this.users = this.users.filter(u => u.id !== user.id)
      }
    },
    async toggleUserStatus(user) {
      try {
        const newStatus = user.status === 0 ? 1 : 0
        const response = await userAPI.toggleUserStatus(user.id, newStatus)

        if (response.code === 200) {
          user.status = newStatus
          this.$toast.success(response.message || `用户${newStatus === 1 ? '启用' : '禁用'}成功`)
        } else {
          this.$toast.error(response.message || '操作失败')
        }
      } catch (error) {
        console.error('切换用户状态失败:', error)
        this.$toast.error('操作失败')
      }
    },
    closeModal() {
      this.showCreateModal = false
      this.showEditModal = false
      this.editingUser = null
    },
    closeUserModal() {
      this.showCreateModal = false
      this.showEditModal = false
      this.showDetailModal = false
      this.editingUser = null
      this.viewingUser = null
    },
    async saveUser(userData) {
      try {
        if (this.editingUser) {
          // 更新用户
          const response = await userAPI.updateUser(this.editingUser.id, userData)

          if (response.code === 200) {
            this.$toast.success('用户更新成功')
            this.loadUsers() // 重新加载用户列表
          } else {
            this.$toast.error(response.message || '用户更新失败')
          }
        } else {
          // 创建用户
          const response = await userAPI.createUser(userData)

          if (response.code === 200) {
            this.$toast.success('用户创建成功')
            this.loadUsers() // 重新加载用户列表
          } else {
            this.$toast.error(response.message || '用户创建失败')
          }
        }
        this.closeUserModal()
      } catch (error) {
        console.error('保存用户失败:', error)
        this.$toast.error(error.message || '操作失败')
      }
    },
    async batchToggleStatus(status) {
      if (this.selectedUsers.length === 0) {
        this.$toast.warning('请先选择要操作的用户')
        return
      }

      const action = status === 1 ? '启用' : '禁用'
      if (!confirm(`确定要${action}选中的 ${this.selectedUsers.length} 个用户吗？`)) {
        return
      }

      try {
        const response = await userAPI.batchToggleUserStatus(this.selectedUsers, status)

        if (response.code === 200) {
          this.$toast.success(response.message || `批量${action}成功`)
          this.selectedUsers = []
          this.loadUsers()
        } else {
          this.$toast.error(response.message || `批量${action}失败`)
        }
      } catch (error) {
        console.error(`批量${action}失败:`, error)
        this.$toast.error(`批量${action}失败`)
      }
    },
    async batchDeleteUsers() {
      if (this.selectedUsers.length === 0) {
        this.$toast.warning('请先选择要删除的用户')
        return
      }

      if (!confirm(`确定要删除选中的 ${this.selectedUsers.length} 个用户吗？此操作不可恢复！`)) {
        return
      }

      try {
        const response = await userAPI.batchDeleteUsers(this.selectedUsers)

        if (response.code === 200) {
          this.$toast.success(response.message || '批量删除成功')
          this.selectedUsers = []
          this.loadUsers()
          this.loadStatistics()
        } else {
          this.$toast.error(response.message || '批量删除失败')
        }
      } catch (error) {
        console.error('批量删除失败:', error)
        this.$toast.error('批量删除失败')
      }
    },
    async exportUsers() {
      try {
        const params = {
          keyword: this.searchQuery,
          role: this.roleFilter,
          status: this.statusFilter,
          startTime: this.dateFrom,
          endTime: this.dateTo
        }

        const response = await userAPI.exportUsers(params)

        if (response.code === 200) {
          const downloadUrl = response.data
          // 创建下载链接
          const link = document.createElement('a')
          link.href = downloadUrl
          link.download = `users_export_${new Date().getTime()}.xlsx`
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)

          this.$toast.success('用户数据导出成功')
        } else {
          this.$toast.error(response.message || '导出失败')
        }
      } catch (error) {
        console.error('导出用户数据失败:', error)
        this.$toast.error('导出失败')
      }
    },
    async resetUserPassword(user) {
      if (!confirm(`确定要重置用户 ${user.username} 的密码吗？`)) {
        return
      }

      try {
        const response = await userAPI.resetUserPassword(user.id)

        if (response.code === 200) {
          const newPassword = response.data
          this.$toast.success(`密码重置成功，新密码：${newPassword}`)

          // 显示新密码对话框
          alert(`用户 ${user.username} 的新密码是：${newPassword}\n请妥善保管并及时通知用户修改。`)
        } else {
          this.$toast.error(response.message || '密码重置失败')
        }
      } catch (error) {
        console.error('重置密码失败:', error)
        this.$toast.error('密码重置失败')
      }
    },
    debounceSearch() {
      clearTimeout(this.searchTimeout)
      this.searchTimeout = setTimeout(() => {
        this.currentPage = 1
        this.loadUsers()
      }, 500)
    },
    resetFilters() {
      this.searchQuery = ''
      this.roleFilter = ''
      this.statusFilter = ''
      this.dateFrom = ''
      this.dateTo = ''
      this.currentPage = 1
      this.loadUsers()
    },
    refreshUsers() {
      this.loadUsers()
      this.loadStatistics()
    },
    getStatusClass(status) {
      return status === 1 ? 'bg-success' : 'bg-danger'
    },
    getStatusText(status) {
      return status === 1 ? '启用' : '禁用'
    },
    formatDate(date) {
      if (!date) return '-'
      return new Date(date).toLocaleDateString('zh-CN')
    }
  },
  watch: {
    searchQuery() {
      this.currentPage = 1
      this.loadUsers()
    },
    roleFilter() {
      this.currentPage = 1
      this.loadUsers()
    },
    statusFilter() {
      this.currentPage = 1
      this.loadUsers()
    },
    dateFrom() {
      this.currentPage = 1
      this.loadUsers()
    },
    dateTo() {
      this.currentPage = 1
      this.loadUsers()
    },
    currentPage() {
      this.loadUsers()
    }
  },
  mounted() {
    this.loadUsers()
    this.loadStatistics()
  }
}
</script>

<style scoped>
.fixed-bottom-actions {
  position: fixed;
  bottom: 0;
  left: 280px;
  right: 0;
  z-index: 1000;
}

@media (max-width: 768px) {
  .fixed-bottom-actions {
    left: 0;
  }
}

.table th {
  border-top: none;
  font-weight: 600;
  background-color: #f8f9fc;
}

.page-link {
  cursor: pointer;
}

.btn-group .btn {
  border-radius: 0.25rem;
  margin-right: 0.25rem;
}

.btn-group .btn:last-child {
  margin-right: 0;
}
</style>

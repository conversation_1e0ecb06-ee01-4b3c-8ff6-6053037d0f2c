<template>
  <div class="modal fade show" style="display: block;" tabindex="-1">
    <div class="modal-dialog modal-xl">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">用户详情</h5>
          <button type="button" class="btn-close" @click="$emit('close')"></button>
        </div>
        <div class="modal-body">
          <div class="row">
            <!-- 用户基本信息 -->
            <div class="col-md-4">
              <div class="card">
                <div class="card-body text-center">
                  <img 
                    :src="user.avatar || '/default-avatar.png'" 
                    class="rounded-circle mb-3" 
                    width="100" 
                    height="100"
                    :alt="user.nickname"
                  >
                  <h5>{{ user.nickname || user.username }}</h5>
                  <p class="text-muted">{{ user.email }}</p>
                  <div class="mb-3">
                    <span 
                      class="badge" 
                      :class="user.role === 'admin' ? 'bg-danger' : 'bg-primary'"
                    >
                      {{ user.role === 'admin' ? '管理员' : '普通用户' }}
                    </span>
                    <span 
                      class="badge ms-2" 
                      :class="getStatusClass(user.status)"
                    >
                      {{ getStatusText(user.status) }}
                    </span>
                  </div>
                  
                  <div class="row text-center">
                    <div class="col-4">
                      <div class="fw-bold text-primary">{{ user.points.toLocaleString() }}</div>
                      <small class="text-muted">积分</small>
                    </div>
                    <div class="col-4">
                      <div class="fw-bold text-success">{{ user.solvedProblems || 0 }}</div>
                      <small class="text-muted">解题数</small>
                    </div>
                    <div class="col-4">
                      <div class="fw-bold text-info">{{ user.createdProblems || 0 }}</div>
                      <small class="text-muted">创建题目</small>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 个人信息 -->
              <div class="card mt-3">
                <div class="card-header">
                  <h6 class="mb-0">个人信息</h6>
                </div>
                <div class="card-body">
                  <div class="mb-2" v-if="user.bio">
                    <strong>简介：</strong>
                    <p class="mb-0">{{ user.bio }}</p>
                  </div>
                  <div class="mb-2" v-if="user.location">
                    <strong>所在地：</strong> {{ user.location }}
                  </div>
                  <div class="mb-2" v-if="user.company">
                    <strong>公司：</strong> {{ user.company }}
                  </div>
                  <div class="mb-2">
                    <strong>注册时间：</strong> {{ formatDate(user.createdTime) }}
                  </div>
                  <div class="mb-2">
                    <strong>最后登录：</strong> {{ formatDate(user.lastLoginTime) }}
                  </div>
                  
                  <!-- 社交链接 -->
                  <div v-if="user.github || user.website" class="mt-3">
                    <strong>社交链接：</strong>
                    <div class="mt-2">
                      <a 
                        v-if="user.github" 
                        :href="user.github" 
                        target="_blank" 
                        class="btn btn-sm btn-outline-dark me-2"
                      >
                        <i class="bi bi-github"></i> GitHub
                      </a>
                      <a 
                        v-if="user.website" 
                        :href="user.website" 
                        target="_blank" 
                        class="btn btn-sm btn-outline-primary"
                      >
                        <i class="bi bi-globe"></i> 网站
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 活动统计和详情 -->
            <div class="col-md-8">
              <!-- 统计图表 -->
              <div class="card mb-3">
                <div class="card-header">
                  <h6 class="mb-0">活动统计</h6>
                </div>
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-3 text-center">
                      <div class="h4 text-primary">{{ userStats.totalSubmissions }}</div>
                      <small class="text-muted">总提交数</small>
                    </div>
                    <div class="col-md-3 text-center">
                      <div class="h4 text-success">{{ userStats.acceptedSubmissions }}</div>
                      <small class="text-muted">通过提交</small>
                    </div>
                    <div class="col-md-3 text-center">
                      <div class="h4 text-info">{{ userStats.createdProblemSets }}</div>
                      <small class="text-muted">创建题集</small>
                    </div>
                    <div class="col-md-3 text-center">
                      <div class="h4 text-warning">{{ userStats.loginDays }}</div>
                      <small class="text-muted">登录天数</small>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 最近活动 -->
              <div class="card mb-3">
                <div class="card-header">
                  <h6 class="mb-0">最近活动</h6>
                </div>
                <div class="card-body">
                  <div class="activity-timeline">
                    <div 
                      v-for="activity in recentActivities" 
                      :key="activity.id"
                      class="activity-item"
                    >
                      <div class="activity-icon" :class="activity.iconClass">
                        <i :class="activity.icon"></i>
                      </div>
                      <div class="activity-content">
                        <div class="activity-text">{{ activity.text }}</div>
                        <div class="activity-time">{{ formatTime(activity.time) }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 解题记录 -->
              <div class="card">
                <div class="card-header">
                  <h6 class="mb-0">最近解题记录</h6>
                </div>
                <div class="card-body">
                  <div class="table-responsive">
                    <table class="table table-sm">
                      <thead>
                        <tr>
                          <th>题目</th>
                          <th>状态</th>
                          <th>语言</th>
                          <th>提交时间</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr v-for="submission in recentSubmissions" :key="submission.id">
                          <td>{{ submission.problemTitle }}</td>
                          <td>
                            <span 
                              class="badge" 
                              :class="submission.status === 'ACCEPTED' ? 'bg-success' : 'bg-danger'"
                            >
                              {{ submission.status === 'ACCEPTED' ? '通过' : '失败' }}
                            </span>
                          </td>
                          <td>{{ submission.language }}</td>
                          <td>{{ formatDate(submission.submitTime) }}</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" @click="$emit('close')">
            关闭
          </button>
          <button type="button" class="btn btn-primary" @click="editUser">
            编辑用户
          </button>
        </div>
      </div>
    </div>
  </div>
  <div class="modal-backdrop fade show"></div>
</template>

<script>
export default {
  name: 'UserDetailModal',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    user: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      userStats: {
        totalSubmissions: 0,
        acceptedSubmissions: 0,
        createdProblemSets: 0,
        loginDays: 0
      },
      recentActivities: [],
      recentSubmissions: []
    }
  },
  methods: {
    getStatusClass(status) {
      const classes = {
        active: 'bg-success',
        inactive: 'bg-warning',
        banned: 'bg-danger'
      }
      return classes[status] || 'bg-secondary'
    },
    getStatusText(status) {
      const texts = {
        active: '活跃',
        inactive: '未激活',
        banned: '已封禁'
      }
      return texts[status] || '未知'
    },
    formatDate(date) {
      if (!date) return '-'
      return new Date(date).toLocaleString('zh-CN')
    },
    formatTime(time) {
      const now = new Date()
      const diff = now - time
      const minutes = Math.floor(diff / (1000 * 60))
      const hours = Math.floor(diff / (1000 * 60 * 60))
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))

      if (minutes < 60) {
        return `${minutes}分钟前`
      } else if (hours < 24) {
        return `${hours}小时前`
      } else {
        return `${days}天前`
      }
    },
    editUser() {
      this.$emit('close')
      // 触发编辑事件
      this.$emit('edit', this.user)
    },
    loadUserData() {
      // Mock 用户统计数据
      this.userStats = {
        totalSubmissions: Math.floor(Math.random() * 500) + 50,
        acceptedSubmissions: Math.floor(Math.random() * 300) + 20,
        createdProblemSets: Math.floor(Math.random() * 10) + 1,
        loginDays: Math.floor(Math.random() * 365) + 30
      }

      // Mock 最近活动
      this.recentActivities = [
        {
          id: 1,
          text: '解决了题目《两数之和》',
          time: new Date(Date.now() - 2 * 60 * 60 * 1000),
          icon: 'bi bi-check-circle',
          iconClass: 'bg-success'
        },
        {
          id: 2,
          text: '创建了题集《算法入门》',
          time: new Date(Date.now() - 6 * 60 * 60 * 1000),
          icon: 'bi bi-collection',
          iconClass: 'bg-info'
        },
        {
          id: 3,
          text: '登录系统',
          time: new Date(Date.now() - 12 * 60 * 60 * 1000),
          icon: 'bi bi-box-arrow-in-right',
          iconClass: 'bg-primary'
        }
      ]

      // Mock 最近提交
      this.recentSubmissions = [
        {
          id: 1,
          problemTitle: '两数之和',
          status: 'ACCEPTED',
          language: 'Python',
          submitTime: new Date(Date.now() - 2 * 60 * 60 * 1000)
        },
        {
          id: 2,
          problemTitle: '三数之和',
          status: 'WRONG_ANSWER',
          language: 'Java',
          submitTime: new Date(Date.now() - 4 * 60 * 60 * 1000)
        },
        {
          id: 3,
          problemTitle: '反转链表',
          status: 'ACCEPTED',
          language: 'C++',
          submitTime: new Date(Date.now() - 8 * 60 * 60 * 1000)
        }
      ]
    }
  },
  mounted() {
    this.loadUserData()
  }
}
</script>

<style scoped>
.modal {
  background-color: rgba(0, 0, 0, 0.5);
}

.activity-timeline {
  max-height: 300px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #e3e6f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  color: white;
  font-size: 0.8rem;
}

.activity-content {
  flex: 1;
}

.activity-text {
  font-size: 0.9rem;
  color: #5a5c69;
  margin-bottom: 0.25rem;
}

.activity-time {
  font-size: 0.75rem;
  color: #858796;
}

.btn-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
  color: #000;
  text-shadow: 0 1px 0 #fff;
  opacity: 0.5;
}

.btn-close:hover {
  opacity: 0.75;
}
</style>

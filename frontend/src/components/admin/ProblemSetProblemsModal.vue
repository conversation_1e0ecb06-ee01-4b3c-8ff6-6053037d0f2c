<template>
  <div class="modal fade show" style="display: block;" tabindex="-1">
    <div class="modal-dialog modal-xl">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">
            管理题集题目 - {{ problemSet?.name }}
          </h5>
          <button type="button" class="btn-close" @click="$emit('close')"></button>
        </div>
        <div class="modal-body">
          <div class="row">
            <!-- 左侧：题集中的题目 -->
            <div class="col-md-6">
              <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                  <h6 class="mb-0">
                    <i class="bi bi-collection me-2"></i>
                    题集中的题目 ({{ selectedProblems.length }})
                  </h6>
                  <button 
                    class="btn btn-sm btn-outline-danger"
                    @click="removeSelectedProblems"
                    :disabled="selectedForRemoval.length === 0"
                  >
                    <i class="bi bi-trash me-1"></i>
                    移除选中
                  </button>
                </div>
                <div class="card-body p-0">
                  <div v-if="selectedProblems.length === 0" class="text-center text-muted py-5">
                    <i class="bi bi-inbox fs-1 mb-2"></i>
                    <p>题集中暂无题目</p>
                  </div>
                  <div v-else class="list-group list-group-flush" style="max-height: 500px; overflow-y: auto;">
                    <div 
                      v-for="(problem, index) in selectedProblems" 
                      :key="problem.id"
                      class="list-group-item d-flex justify-content-between align-items-center"
                    >
                      <div class="d-flex align-items-center">
                        <input 
                          type="checkbox" 
                          class="form-check-input me-3"
                          :value="problem.id"
                          v-model="selectedForRemoval"
                        >
                        <div class="me-3">
                          <span class="badge bg-secondary">{{ index + 1 }}</span>
                        </div>
                        <div>
                          <div class="fw-bold">{{ problem.title }}</div>
                          <div class="small text-muted">
                            <span 
                              class="badge me-1" 
                              :class="getDifficultyClass(problem.difficulty)"
                            >
                              {{ getDifficultyText(problem.difficulty) }}
                            </span>
                            <span v-for="tag in problem.tags.split(',').slice(0, 2)" :key="tag" class="badge bg-light text-dark me-1">
                              {{ tag.trim() }}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div class="btn-group">
                        <button 
                          class="btn btn-sm btn-outline-primary"
                          @click="moveUp(index)"
                          :disabled="index === 0"
                          title="上移"
                        >
                          <i class="bi bi-arrow-up"></i>
                        </button>
                        <button 
                          class="btn btn-sm btn-outline-primary"
                          @click="moveDown(index)"
                          :disabled="index === selectedProblems.length - 1"
                          title="下移"
                        >
                          <i class="bi bi-arrow-down"></i>
                        </button>
                        <button 
                          class="btn btn-sm btn-outline-danger"
                          @click="removeProblem(problem)"
                          title="移除"
                        >
                          <i class="bi bi-x"></i>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 右侧：可添加的题目 -->
            <div class="col-md-6">
              <div class="card h-100">
                <div class="card-header">
                  <h6 class="mb-0">
                    <i class="bi bi-plus-circle me-2"></i>
                    可添加的题目
                  </h6>
                  <!-- 搜索和筛选 -->
                  <div class="mt-2">
                    <div class="row g-2">
                      <div class="col-8">
                        <input 
                          type="text" 
                          class="form-control form-control-sm" 
                          placeholder="搜索题目..."
                          v-model="searchQuery"
                        >
                      </div>
                      <div class="col-4">
                        <select class="form-control form-control-sm" v-model="difficultyFilter">
                          <option value="">全部难度</option>
                          <option value="EASY">简单</option>
                          <option value="MEDIUM">中等</option>
                          <option value="HARD">困难</option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="card-body p-0">
                  <div class="d-flex justify-content-between align-items-center p-3 border-bottom">
                    <span class="text-muted small">找到 {{ filteredAvailableProblems.length }} 个题目</span>
                    <button 
                      class="btn btn-sm btn-primary"
                      @click="addSelectedProblems"
                      :disabled="selectedForAddition.length === 0"
                    >
                      <i class="bi bi-plus me-1"></i>
                      添加选中 ({{ selectedForAddition.length }})
                    </button>
                  </div>
                  <div class="list-group list-group-flush" style="max-height: 450px; overflow-y: auto;">
                    <div 
                      v-for="problem in filteredAvailableProblems" 
                      :key="problem.id"
                      class="list-group-item d-flex justify-content-between align-items-center"
                    >
                      <div class="d-flex align-items-center">
                        <input 
                          type="checkbox" 
                          class="form-check-input me-3"
                          :value="problem.id"
                          v-model="selectedForAddition"
                        >
                        <div>
                          <div class="fw-bold">{{ problem.title }}</div>
                          <div class="small text-muted">
                            <span 
                              class="badge me-1" 
                              :class="getDifficultyClass(problem.difficulty)"
                            >
                              {{ getDifficultyText(problem.difficulty) }}
                            </span>
                            <span v-for="tag in problem.tags.split(',').slice(0, 2)" :key="tag" class="badge bg-light text-dark me-1">
                              {{ tag.trim() }}
                            </span>
                          </div>
                          <div class="small text-muted">
                            通过率: {{ calculateAcceptanceRate(problem) }}%
                          </div>
                        </div>
                      </div>
                      <button 
                        class="btn btn-sm btn-outline-primary"
                        @click="addProblem(problem)"
                      >
                        <i class="bi bi-plus"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" @click="$emit('close')">
            关闭
          </button>
          <button type="button" class="btn btn-primary" @click="saveChanges">
            <i class="bi bi-check me-1"></i>
            保存更改
          </button>
        </div>
      </div>
    </div>
  </div>
  <div class="modal-backdrop fade show"></div>
</template>

<script>
export default {
  name: 'ProblemSetProblemsModal',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    problemSet: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      selectedProblems: [], // 题集中的题目
      availableProblems: [], // 可添加的题目
      selectedForRemoval: [], // 选中要移除的题目
      selectedForAddition: [], // 选中要添加的题目
      searchQuery: '',
      difficultyFilter: ''
    }
  },
  computed: {
    filteredAvailableProblems() {
      let filtered = this.availableProblems

      // 搜索过滤
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase()
        filtered = filtered.filter(problem => 
          problem.title.toLowerCase().includes(query) ||
          problem.tags.toLowerCase().includes(query)
        )
      }

      // 难度过滤
      if (this.difficultyFilter) {
        filtered = filtered.filter(problem => problem.difficulty === this.difficultyFilter)
      }

      // 排除已在题集中的题目
      const selectedIds = this.selectedProblems.map(p => p.id)
      filtered = filtered.filter(problem => !selectedIds.includes(problem.id))

      return filtered
    }
  },
  methods: {
    getDifficultyClass(difficulty) {
      const classes = {
        EASY: 'bg-success',
        MEDIUM: 'bg-warning',
        HARD: 'bg-danger'
      }
      return classes[difficulty] || 'bg-secondary'
    },
    getDifficultyText(difficulty) {
      const texts = {
        EASY: '简单',
        MEDIUM: '中等',
        HARD: '困难'
      }
      return texts[difficulty] || '未知'
    },
    calculateAcceptanceRate(problem) {
      if (!problem.submissionCount || problem.submissionCount === 0) return 0
      return Math.round((problem.acceptedCount / problem.submissionCount) * 100)
    },
    addProblem(problem) {
      this.selectedProblems.push(problem)
      this.selectedForAddition = this.selectedForAddition.filter(id => id !== problem.id)
    },
    addSelectedProblems() {
      const problemsToAdd = this.availableProblems.filter(p => 
        this.selectedForAddition.includes(p.id)
      )
      this.selectedProblems.push(...problemsToAdd)
      this.selectedForAddition = []
    },
    removeProblem(problem) {
      this.selectedProblems = this.selectedProblems.filter(p => p.id !== problem.id)
      this.selectedForRemoval = this.selectedForRemoval.filter(id => id !== problem.id)
    },
    removeSelectedProblems() {
      if (confirm(`确定要移除选中的 ${this.selectedForRemoval.length} 个题目吗？`)) {
        this.selectedProblems = this.selectedProblems.filter(p => 
          !this.selectedForRemoval.includes(p.id)
        )
        this.selectedForRemoval = []
      }
    },
    moveUp(index) {
      if (index > 0) {
        const temp = this.selectedProblems[index]
        this.selectedProblems[index] = this.selectedProblems[index - 1]
        this.selectedProblems[index - 1] = temp
      }
    },
    moveDown(index) {
      if (index < this.selectedProblems.length - 1) {
        const temp = this.selectedProblems[index]
        this.selectedProblems[index] = this.selectedProblems[index + 1]
        this.selectedProblems[index + 1] = temp
      }
    },
    saveChanges() {
      // 触发保存事件
      this.$emit('save', {
        problemSetId: this.problemSet.id,
        problems: this.selectedProblems
      })
      this.$emit('close')
    },
    loadData() {
      // Mock 数据加载
      this.loadSelectedProblems()
      this.loadAvailableProblems()
    },
    loadSelectedProblems() {
      // 加载题集中的题目
      // TODO: 调用后端API获取题集中的题目
    },
    loadAvailableProblems() {
      // 加载可添加的题目
      // TODO: 调用后端API获取可添加的题目
    }
  },
  mounted() {
    this.loadData()
  }
}
</script>

<style scoped>
.modal {
  background-color: rgba(0, 0, 0, 0.5);
}

.btn-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
  color: #000;
  text-shadow: 0 1px 0 #fff;
  opacity: 0.5;
}

.btn-close:hover {
  opacity: 0.75;
}

.list-group-item {
  border-left: none;
  border-right: none;
}

.list-group-item:first-child {
  border-top: none;
}

.list-group-item:last-child {
  border-bottom: none;
}

.badge {
  font-size: 0.7rem;
}

.btn-group .btn {
  border-radius: 0.25rem;
  margin-right: 0.25rem;
}

.btn-group .btn:last-child {
  margin-right: 0;
}
</style>

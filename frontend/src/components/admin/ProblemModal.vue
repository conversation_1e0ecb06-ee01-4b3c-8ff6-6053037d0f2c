<template>
  <div class="modal fade show" style="display: block;" tabindex="-1">
    <div class="modal-dialog modal-xl">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">
            {{ isEdit ? '编辑题目' : '创建题目' }}
          </h5>
          <button type="button" class="btn-close" @click="$emit('close')"></button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="handleSubmit">
            <div class="row">
              <!-- 基本信息 -->
              <div class="col-md-8">
                <h6 class="text-primary mb-3">基本信息</h6>
                
                <div class="mb-3">
                  <label class="form-label">题目标题 *</label>
                  <input 
                    type="text" 
                    class="form-control" 
                    v-model="formData.title"
                    required
                    placeholder="请输入题目标题"
                  >
                </div>

                <div class="mb-3">
                  <label class="form-label">题目描述 *</label>
                  <textarea 
                    class="form-control" 
                    rows="6"
                    v-model="formData.description"
                    required
                    placeholder="请输入题目描述，包括问题背景、要求等"
                  ></textarea>
                </div>

                <div class="mb-3">
                  <label class="form-label">输入说明</label>
                  <textarea 
                    class="form-control" 
                    rows="3"
                    v-model="formData.inputDescription"
                    placeholder="描述输入格式和约束条件"
                  ></textarea>
                </div>

                <div class="mb-3">
                  <label class="form-label">输出说明</label>
                  <textarea 
                    class="form-control" 
                    rows="3"
                    v-model="formData.outputDescription"
                    placeholder="描述输出格式和要求"
                  ></textarea>
                </div>

                <div class="mb-3">
                  <label class="form-label">示例</label>
                  <div v-for="(example, index) in formData.examples" :key="index" class="border rounded p-3 mb-2">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                      <strong>示例 {{ index + 1 }}</strong>
                      <button 
                        type="button" 
                        class="btn btn-sm btn-outline-danger"
                        @click="removeExample(index)"
                        v-if="formData.examples.length > 1"
                      >
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                    <div class="row">
                      <div class="col-md-6">
                        <label class="form-label small">输入</label>
                        <textarea 
                          class="form-control form-control-sm" 
                          rows="3"
                          v-model="example.input"
                          placeholder="示例输入"
                        ></textarea>
                      </div>
                      <div class="col-md-6">
                        <label class="form-label small">输出</label>
                        <textarea 
                          class="form-control form-control-sm" 
                          rows="3"
                          v-model="example.output"
                          placeholder="示例输出"
                        ></textarea>
                      </div>
                    </div>
                    <div class="mt-2">
                      <label class="form-label small">解释（可选）</label>
                      <input 
                        type="text" 
                        class="form-control form-control-sm" 
                        v-model="example.explanation"
                        placeholder="解释示例"
                      >
                    </div>
                  </div>
                  <button 
                    type="button" 
                    class="btn btn-sm btn-outline-primary"
                    @click="addExample"
                  >
                    <i class="bi bi-plus me-1"></i>
                    添加示例
                  </button>
                </div>

                <div class="mb-3">
                  <label class="form-label">提示</label>
                  <textarea 
                    class="form-control" 
                    rows="3"
                    v-model="formData.hint"
                    placeholder="给解题者的提示（可选）"
                  ></textarea>
                </div>
              </div>

              <!-- 设置选项 -->
              <div class="col-md-4">
                <h6 class="text-primary mb-3">设置选项</h6>

                <div class="mb-3">
                  <label class="form-label">难度等级 *</label>
                  <select class="form-control" v-model="formData.difficulty" required>
                    <option value="">请选择难度</option>
                    <option value="EASY">简单</option>
                    <option value="MEDIUM">中等</option>
                    <option value="HARD">困难</option>
                  </select>
                </div>

                <div class="mb-3">
                  <label class="form-label">标签</label>
                  <input 
                    type="text" 
                    class="form-control" 
                    v-model="formData.tags"
                    placeholder="用逗号分隔，如：数组,动态规划"
                  >
                  <div class="form-text">用逗号分隔多个标签</div>
                </div>

                <div class="mb-3">
                  <label class="form-label">状态</label>
                  <select class="form-control" v-model="formData.status">
                    <option value="DRAFT">草稿</option>
                    <option value="PUBLISHED">已发布</option>
                    <option value="ARCHIVED">已归档</option>
                  </select>
                </div>

                <div class="mb-3">
                  <label class="form-label">时间限制 (ms)</label>
                  <input 
                    type="number" 
                    class="form-control" 
                    v-model.number="formData.timeLimit"
                    min="100"
                    step="100"
                  >
                </div>

                <div class="mb-3">
                  <label class="form-label">内存限制 (MB)</label>
                  <input 
                    type="number" 
                    class="form-control" 
                    v-model.number="formData.memoryLimit"
                    min="64"
                    step="64"
                  >
                </div>

                <div class="mb-3">
                  <label class="form-label">支持的编程语言</label>
                  <div class="form-check-group border rounded p-2" style="max-height: 150px; overflow-y: auto;">
                    <div class="form-check" v-for="lang in availableLanguages" :key="lang.value">
                      <input 
                        class="form-check-input" 
                        type="checkbox" 
                        :value="lang.value"
                        v-model="formData.supportedLanguages"
                        :id="'lang-' + lang.value"
                      >
                      <label class="form-check-label" :for="'lang-' + lang.value">
                        {{ lang.label }}
                      </label>
                    </div>
                  </div>
                </div>

                <div class="mb-3">
                  <div class="form-check">
                    <input 
                      class="form-check-input" 
                      type="checkbox" 
                      v-model="formData.allowDiscussion"
                      id="allowDiscussion"
                    >
                    <label class="form-check-label" for="allowDiscussion">
                      允许讨论
                    </label>
                  </div>
                </div>

                <div class="mb-3">
                  <div class="form-check">
                    <input 
                      class="form-check-input" 
                      type="checkbox" 
                      v-model="formData.showTestCases"
                      id="showTestCases"
                    >
                    <label class="form-check-label" for="showTestCases">
                      显示测试用例
                    </label>
                  </div>
                </div>
              </div>
            </div>

            <!-- 约束条件 -->
            <div class="row">
              <div class="col-12">
                <h6 class="text-primary mb-3">约束条件</h6>
                <div class="mb-3">
                  <label class="form-label">数据范围和约束</label>
                  <textarea 
                    class="form-control" 
                    rows="4"
                    v-model="formData.constraints"
                    placeholder="描述输入数据的范围和约束条件，如：1 ≤ n ≤ 10^5"
                  ></textarea>
                </div>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" @click="$emit('close')">
            取消
          </button>
          <button 
            type="button" 
            class="btn btn-primary" 
            @click="handleSubmit"
            :disabled="!isFormValid"
          >
            {{ isEdit ? '保存修改' : '创建题目' }}
          </button>
        </div>
      </div>
    </div>
  </div>
  <div class="modal-backdrop fade show"></div>
</template>

<script>
export default {
  name: 'ProblemModal',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    problem: {
      type: Object,
      default: null
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      formData: {
        title: '',
        description: '',
        inputDescription: '',
        outputDescription: '',
        examples: [
          { input: '', output: '', explanation: '' }
        ],
        hint: '',
        difficulty: '',
        tags: '',
        status: 'DRAFT',
        timeLimit: 1000,
        memoryLimit: 256,
        supportedLanguages: ['python', 'java', 'cpp'],
        allowDiscussion: true,
        showTestCases: false,
        constraints: ''
      },
      availableLanguages: [
        { value: 'python', label: 'Python' },
        { value: 'java', label: 'Java' },
        { value: 'cpp', label: 'C++' },
        { value: 'c', label: 'C' },
        { value: 'javascript', label: 'JavaScript' },
        { value: 'go', label: 'Go' },
        { value: 'rust', label: 'Rust' },
        { value: 'kotlin', label: 'Kotlin' }
      ]
    }
  },
  computed: {
    isFormValid() {
      return this.formData.title.trim() && 
             this.formData.description.trim() && 
             this.formData.difficulty &&
             this.formData.examples.some(ex => ex.input.trim() && ex.output.trim())
    }
  },
  watch: {
    problem: {
      immediate: true,
      handler(newProblem) {
        if (newProblem) {
          this.formData = {
            ...this.formData,
            ...newProblem,
            examples: newProblem.examples || [{ input: '', output: '', explanation: '' }],
            supportedLanguages: newProblem.supportedLanguages || ['python', 'java', 'cpp']
          }
        } else {
          this.resetForm()
        }
      }
    }
  },
  methods: {
    resetForm() {
      this.formData = {
        title: '',
        description: '',
        inputDescription: '',
        outputDescription: '',
        examples: [
          { input: '', output: '', explanation: '' }
        ],
        hint: '',
        difficulty: '',
        tags: '',
        status: 'DRAFT',
        timeLimit: 1000,
        memoryLimit: 256,
        supportedLanguages: ['python', 'java', 'cpp'],
        allowDiscussion: true,
        showTestCases: false,
        constraints: ''
      }
    },
    addExample() {
      this.formData.examples.push({
        input: '',
        output: '',
        explanation: ''
      })
    },
    removeExample(index) {
      if (this.formData.examples.length > 1) {
        this.formData.examples.splice(index, 1)
      }
    },
    handleSubmit() {
      if (!this.isFormValid) {
        return
      }

      const problemData = {
        ...this.formData,
        // 清理空的示例
        examples: this.formData.examples.filter(ex => ex.input.trim() && ex.output.trim())
      }

      this.$emit('save', problemData)
    }
  }
}
</script>

<style scoped>
.modal {
  background-color: rgba(0, 0, 0, 0.5);
}

.form-label {
  font-weight: 600;
  color: #495057;
}

.text-primary {
  color: #007bff !important;
}

.btn-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
  color: #000;
  text-shadow: 0 1px 0 #fff;
  opacity: 0.5;
}

.btn-close:hover {
  opacity: 0.75;
}

.form-check-group {
  background-color: #f8f9fa;
}

.form-check {
  margin-bottom: 0.5rem;
}

.form-check:last-child {
  margin-bottom: 0;
}
</style>

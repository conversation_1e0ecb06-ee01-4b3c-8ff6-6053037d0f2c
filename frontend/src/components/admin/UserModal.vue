<template>
  <div class="modal fade show" style="display: block;" tabindex="-1">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">
            {{ isEdit ? '编辑用户' : '创建用户' }}
          </h5>
          <button type="button" class="btn-close" @click="$emit('close')"></button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="handleSubmit">
            <div class="row">
              <!-- 基本信息 -->
              <div class="col-md-6">
                <h6 class="text-primary mb-3">基本信息</h6>
                
                <div class="mb-3">
                  <label class="form-label">用户名 *</label>
                  <input 
                    type="text" 
                    class="form-control" 
                    v-model="formData.username"
                    :disabled="isEdit"
                    required
                  >
                  <div class="form-text">用户名创建后不可修改</div>
                </div>

                <div class="mb-3">
                  <label class="form-label">邮箱 *</label>
                  <input 
                    type="email" 
                    class="form-control" 
                    v-model="formData.email"
                    required
                  >
                </div>

                <div class="mb-3">
                  <label class="form-label">昵称</label>
                  <input 
                    type="text" 
                    class="form-control" 
                    v-model="formData.nickname"
                  >
                </div>

                <div class="mb-3" v-if="!isEdit">
                  <label class="form-label">密码 *</label>
                  <input 
                    type="password" 
                    class="form-control" 
                    v-model="formData.password"
                    required
                  >
                </div>

                <div class="mb-3" v-if="!isEdit">
                  <label class="form-label">确认密码 *</label>
                  <input 
                    type="password" 
                    class="form-control" 
                    v-model="formData.confirmPassword"
                    required
                  >
                  <div v-if="formData.password !== formData.confirmPassword" class="text-danger small">
                    密码不一致
                  </div>
                </div>
              </div>

              <!-- 详细信息 -->
              <div class="col-md-6">
                <h6 class="text-primary mb-3">详细信息</h6>

                <div class="mb-3">
                  <label class="form-label">角色</label>
                  <select class="form-control" v-model="formData.role">
                    <option value="user">普通用户</option>
                    <option value="admin">管理员</option>
                  </select>
                </div>

                <div class="mb-3">
                  <label class="form-label">状态</label>
                  <select class="form-control" v-model="formData.status">
                    <option value="active">活跃</option>
                    <option value="inactive">未激活</option>
                    <option value="banned">已封禁</option>
                  </select>
                </div>

                <div class="mb-3">
                  <label class="form-label">积分</label>
                  <input 
                    type="number" 
                    class="form-control" 
                    v-model.number="formData.points"
                    min="0"
                  >
                </div>

                <div class="mb-3">
                  <label class="form-label">个人简介</label>
                  <textarea 
                    class="form-control" 
                    rows="3"
                    v-model="formData.bio"
                    placeholder="用户的个人简介..."
                  ></textarea>
                </div>

                <div class="mb-3">
                  <label class="form-label">所在地</label>
                  <input 
                    type="text" 
                    class="form-control" 
                    v-model="formData.location"
                    placeholder="如：北京市"
                  >
                </div>

                <div class="mb-3">
                  <label class="form-label">公司</label>
                  <input 
                    type="text" 
                    class="form-control" 
                    v-model="formData.company"
                    placeholder="如：阿里巴巴"
                  >
                </div>
              </div>
            </div>

            <!-- 社交链接 -->
            <div class="row">
              <div class="col-12">
                <h6 class="text-primary mb-3">社交链接</h6>
                <div class="row">
                  <div class="col-md-6">
                    <div class="mb-3">
                      <label class="form-label">GitHub</label>
                      <input 
                        type="url" 
                        class="form-control" 
                        v-model="formData.github"
                        placeholder="https://github.com/username"
                      >
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="mb-3">
                      <label class="form-label">个人网站</label>
                      <input 
                        type="url" 
                        class="form-control" 
                        v-model="formData.website"
                        placeholder="https://example.com"
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" @click="$emit('close')">
            取消
          </button>
          <button 
            type="button" 
            class="btn btn-primary" 
            @click="handleSubmit"
            :disabled="!isFormValid"
          >
            {{ isEdit ? '保存修改' : '创建用户' }}
          </button>
        </div>
      </div>
    </div>
  </div>
  <div class="modal-backdrop fade show"></div>
</template>

<script>
export default {
  name: 'UserModal',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    user: {
      type: Object,
      default: null
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      formData: {
        username: '',
        email: '',
        nickname: '',
        password: '',
        confirmPassword: '',
        role: 'user',
        status: 'active',
        points: 0,
        bio: '',
        location: '',
        company: '',
        github: '',
        website: ''
      }
    }
  },
  computed: {
    isFormValid() {
      if (!this.formData.username || !this.formData.email) {
        return false
      }
      
      if (!this.isEdit) {
        if (!this.formData.password || !this.formData.confirmPassword) {
          return false
        }
        if (this.formData.password !== this.formData.confirmPassword) {
          return false
        }
      }
      
      return true
    }
  },
  watch: {
    user: {
      immediate: true,
      handler(newUser) {
        if (newUser) {
          this.formData = {
            ...this.formData,
            ...newUser,
            password: '',
            confirmPassword: ''
          }
        } else {
          this.resetForm()
        }
      }
    }
  },
  methods: {
    resetForm() {
      this.formData = {
        username: '',
        email: '',
        nickname: '',
        password: '',
        confirmPassword: '',
        role: 'user',
        status: 'active',
        points: 0,
        bio: '',
        location: '',
        company: '',
        github: '',
        website: ''
      }
    },
    handleSubmit() {
      if (!this.isFormValid) {
        return
      }

      const userData = { ...this.formData }
      delete userData.confirmPassword

      this.$emit('save', userData)
    }
  }
}
</script>

<style scoped>
.modal {
  background-color: rgba(0, 0, 0, 0.5);
}

.form-label {
  font-weight: 600;
  color: #495057;
}

.text-primary {
  color: #007bff !important;
}

.btn-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
  color: #000;
  text-shadow: 0 1px 0 #fff;
  opacity: 0.5;
}

.btn-close:hover {
  opacity: 0.75;
}
</style>

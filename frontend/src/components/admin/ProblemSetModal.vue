<template>
  <div class="modal fade show" style="display: block;" tabindex="-1">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">
            {{ isEdit ? '编辑题集' : '创建题集' }}
          </h5>
          <button type="button" class="btn-close" @click="$emit('close')"></button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="handleSubmit">
            <div class="row">
              <!-- 基本信息 -->
              <div class="col-md-8">
                <h6 class="text-primary mb-3">基本信息</h6>
                
                <div class="mb-3">
                  <label class="form-label">题集名称 *</label>
                  <input 
                    type="text" 
                    class="form-control" 
                    v-model="formData.name"
                    required
                    placeholder="请输入题集名称"
                  >
                </div>

                <div class="mb-3">
                  <label class="form-label">题集描述 *</label>
                  <textarea 
                    class="form-control" 
                    rows="4"
                    v-model="formData.description"
                    required
                    placeholder="请输入题集描述，包括学习目标、适用人群等"
                  ></textarea>
                </div>

                <div class="mb-3">
                  <label class="form-label">标签</label>
                  <input 
                    type="text" 
                    class="form-control" 
                    v-model="formData.tags"
                    placeholder="用逗号分隔，如：算法基础,数据结构,入门"
                  >
                  <div class="form-text">用逗号分隔多个标签</div>
                </div>

                <div class="mb-3">
                  <label class="form-label">学习路径</label>
                  <textarea 
                    class="form-control" 
                    rows="3"
                    v-model="formData.learningPath"
                    placeholder="建议的学习顺序和方法"
                  ></textarea>
                </div>
              </div>

              <!-- 设置选项 -->
              <div class="col-md-4">
                <h6 class="text-primary mb-3">设置选项</h6>

                <div class="mb-3">
                  <label class="form-label">可见性</label>
                  <div class="form-check">
                    <input 
                      class="form-check-input" 
                      type="radio" 
                      name="visibility"
                      value="true"
                      v-model="formData.isPublic"
                      id="public"
                    >
                    <label class="form-check-label" for="public">
                      <i class="bi bi-unlock me-1"></i>
                      公开
                    </label>
                    <div class="form-text small">所有用户都可以查看</div>
                  </div>
                  <div class="form-check">
                    <input 
                      class="form-check-input" 
                      type="radio" 
                      name="visibility"
                      value="false"
                      v-model="formData.isPublic"
                      id="private"
                    >
                    <label class="form-check-label" for="private">
                      <i class="bi bi-lock me-1"></i>
                      私有
                    </label>
                    <div class="form-text small">仅创建者可见</div>
                  </div>
                </div>

                <div class="mb-3">
                  <label class="form-label">状态</label>
                  <select class="form-control" v-model="formData.status">
                    <option value="DRAFT">草稿</option>
                    <option value="PUBLISHED">已发布</option>
                    <option value="ARCHIVED">已归档</option>
                  </select>
                </div>

                <div class="mb-3">
                  <label class="form-label">难度等级</label>
                  <select class="form-control" v-model="formData.difficulty">
                    <option value="BEGINNER">初学者</option>
                    <option value="INTERMEDIATE">中级</option>
                    <option value="ADVANCED">高级</option>
                    <option value="EXPERT">专家</option>
                  </select>
                </div>

                <div class="mb-3">
                  <label class="form-label">预计完成时间</label>
                  <div class="input-group">
                    <input 
                      type="number" 
                      class="form-control" 
                      v-model.number="formData.estimatedHours"
                      min="1"
                      placeholder="小时"
                    >
                    <span class="input-group-text">小时</span>
                  </div>
                </div>

                <div class="mb-3">
                  <div class="form-check">
                    <input 
                      class="form-check-input" 
                      type="checkbox" 
                      v-model="formData.allowComments"
                      id="allowComments"
                    >
                    <label class="form-check-label" for="allowComments">
                      允许评论
                    </label>
                  </div>
                </div>

                <div class="mb-3">
                  <div class="form-check">
                    <input 
                      class="form-check-input" 
                      type="checkbox" 
                      v-model="formData.allowFork"
                      id="allowFork"
                    >
                    <label class="form-check-label" for="allowFork">
                      允许复制
                    </label>
                  </div>
                </div>
              </div>
            </div>

            <!-- 封面图片 -->
            <div class="row">
              <div class="col-12">
                <h6 class="text-primary mb-3">封面设置</h6>
                <div class="mb-3">
                  <label class="form-label">封面图片 URL</label>
                  <input 
                    type="url" 
                    class="form-control" 
                    v-model="formData.coverImage"
                    placeholder="https://example.com/cover.jpg"
                  >
                  <div class="form-text">建议尺寸：1200x600 像素</div>
                </div>
                
                <div v-if="formData.coverImage" class="mb-3">
                  <label class="form-label">预览</label>
                  <div class="border rounded p-2">
                    <img 
                      :src="formData.coverImage" 
                      class="img-fluid rounded"
                      style="max-height: 200px;"
                      @error="handleImageError"
                      alt="封面预览"
                    >
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" @click="$emit('close')">
            取消
          </button>
          <button 
            type="button" 
            class="btn btn-primary" 
            @click="handleSubmit"
            :disabled="!isFormValid"
          >
            {{ isEdit ? '保存修改' : '创建题集' }}
          </button>
        </div>
      </div>
    </div>
  </div>
  <div class="modal-backdrop fade show"></div>
</template>

<script>
export default {
  name: 'ProblemSetModal',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    problemSet: {
      type: Object,
      default: null
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      formData: {
        name: '',
        description: '',
        tags: '',
        learningPath: '',
        isPublic: 'true',
        status: 'DRAFT',
        difficulty: 'BEGINNER',
        estimatedHours: 10,
        allowComments: true,
        allowFork: true,
        coverImage: ''
      }
    }
  },
  computed: {
    isFormValid() {
      return this.formData.name.trim() && this.formData.description.trim()
    }
  },
  watch: {
    problemSet: {
      immediate: true,
      handler(newProblemSet) {
        if (newProblemSet) {
          this.formData = {
            ...this.formData,
            ...newProblemSet,
            isPublic: newProblemSet.isPublic ? 'true' : 'false'
          }
        } else {
          this.resetForm()
        }
      }
    }
  },
  methods: {
    resetForm() {
      this.formData = {
        name: '',
        description: '',
        tags: '',
        learningPath: '',
        isPublic: 'true',
        status: 'DRAFT',
        difficulty: 'BEGINNER',
        estimatedHours: 10,
        allowComments: true,
        allowFork: true,
        coverImage: ''
      }
    },
    handleSubmit() {
      if (!this.isFormValid) {
        return
      }

      const problemSetData = {
        ...this.formData,
        isPublic: this.formData.isPublic === 'true'
      }

      this.$emit('save', problemSetData)
    },
    handleImageError() {
      this.formData.coverImage = ''
    }
  }
}
</script>

<style scoped>
.modal {
  background-color: rgba(0, 0, 0, 0.5);
}

.form-label {
  font-weight: 600;
  color: #495057;
}

.text-primary {
  color: #007bff !important;
}

.btn-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
  color: #000;
  text-shadow: 0 1px 0 #fff;
  opacity: 0.5;
}

.btn-close:hover {
  opacity: 0.75;
}

.form-check {
  margin-bottom: 0.5rem;
}

.form-check .form-text {
  margin-left: 1.5rem;
  margin-top: 0.25rem;
}
</style>

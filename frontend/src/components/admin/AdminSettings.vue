<template>
  <div class="admin-settings">
    <!-- 页面标题 -->
    <div class="page-header mb-4">
      <h2>系统设置</h2>
      <p class="text-muted">管理系统的各项配置和参数</p>
    </div>

    <div class="row">
      <!-- 基本设置 -->
      <div class="col-lg-6 mb-4">
        <div class="card shadow">
          <div class="card-header">
            <h6 class="m-0 font-weight-bold text-primary">基本设置</h6>
          </div>
          <div class="card-body">
            <form @submit.prevent="saveBasicSettings">
              <div class="mb-3">
                <label class="form-label">网站名称</label>
                <input 
                  type="text" 
                  class="form-control" 
                  v-model="basicSettings.siteName"
                >
              </div>
              <div class="mb-3">
                <label class="form-label">网站描述</label>
                <textarea 
                  class="form-control" 
                  rows="3"
                  v-model="basicSettings.siteDescription"
                ></textarea>
              </div>
              <div class="mb-3">
                <label class="form-label">网站关键词</label>
                <input 
                  type="text" 
                  class="form-control" 
                  v-model="basicSettings.siteKeywords"
                  placeholder="用逗号分隔"
                >
              </div>
              <div class="mb-3">
                <label class="form-label">联系邮箱</label>
                <input 
                  type="email" 
                  class="form-control" 
                  v-model="basicSettings.contactEmail"
                >
              </div>
              <div class="mb-3">
                <div class="form-check">
                  <input 
                    class="form-check-input" 
                    type="checkbox" 
                    v-model="basicSettings.allowRegistration"
                    id="allowRegistration"
                  >
                  <label class="form-check-label" for="allowRegistration">
                    允许用户注册
                  </label>
                </div>
              </div>
              <div class="mb-3">
                <div class="form-check">
                  <input 
                    class="form-check-input" 
                    type="checkbox" 
                    v-model="basicSettings.requireEmailVerification"
                    id="requireEmailVerification"
                  >
                  <label class="form-check-label" for="requireEmailVerification">
                    注册需要邮箱验证
                  </label>
                </div>
              </div>
              <button type="submit" class="btn btn-primary">保存设置</button>
            </form>
          </div>
        </div>
      </div>

      <!-- 用户设置 -->
      <div class="col-lg-6 mb-4">
        <div class="card shadow">
          <div class="card-header">
            <h6 class="m-0 font-weight-bold text-primary">用户设置</h6>
          </div>
          <div class="card-body">
            <form @submit.prevent="saveUserSettings">
              <div class="mb-3">
                <label class="form-label">默认用户角色</label>
                <select class="form-control" v-model="userSettings.defaultRole">
                  <option value="user">普通用户</option>
                  <option value="admin">管理员</option>
                </select>
              </div>
              <div class="mb-3">
                <label class="form-label">新用户初始积分</label>
                <input 
                  type="number" 
                  class="form-control" 
                  v-model.number="userSettings.initialPoints"
                  min="0"
                >
              </div>
              <div class="mb-3">
                <label class="form-label">每日登录奖励积分</label>
                <input 
                  type="number" 
                  class="form-control" 
                  v-model.number="userSettings.dailyLoginPoints"
                  min="0"
                >
              </div>
              <div class="mb-3">
                <label class="form-label">解题奖励积分</label>
                <input 
                  type="number" 
                  class="form-control" 
                  v-model.number="userSettings.solvePoints"
                  min="0"
                >
              </div>
              <div class="mb-3">
                <label class="form-label">创建题目奖励积分</label>
                <input 
                  type="number" 
                  class="form-control" 
                  v-model.number="userSettings.createProblemPoints"
                  min="0"
                >
              </div>
              <div class="mb-3">
                <div class="form-check">
                  <input 
                    class="form-check-input" 
                    type="checkbox" 
                    v-model="userSettings.enablePointsSystem"
                    id="enablePointsSystem"
                  >
                  <label class="form-check-label" for="enablePointsSystem">
                    启用积分系统
                  </label>
                </div>
              </div>
              <button type="submit" class="btn btn-primary">保存设置</button>
            </form>
          </div>
        </div>
      </div>

      <!-- 题目设置 -->
      <div class="col-lg-6 mb-4">
        <div class="card shadow">
          <div class="card-header">
            <h6 class="m-0 font-weight-bold text-primary">题目设置</h6>
          </div>
          <div class="card-body">
            <form @submit.prevent="saveProblemSettings">
              <div class="mb-3">
                <label class="form-label">默认时间限制 (ms)</label>
                <input 
                  type="number" 
                  class="form-control" 
                  v-model.number="problemSettings.defaultTimeLimit"
                  min="100"
                >
              </div>
              <div class="mb-3">
                <label class="form-label">默认内存限制 (MB)</label>
                <input 
                  type="number" 
                  class="form-control" 
                  v-model.number="problemSettings.defaultMemoryLimit"
                  min="64"
                >
              </div>
              <div class="mb-3">
                <label class="form-label">支持的编程语言</label>
                <div class="form-check-group">
                  <div class="form-check" v-for="lang in availableLanguages" :key="lang.value">
                    <input 
                      class="form-check-input" 
                      type="checkbox" 
                      :value="lang.value"
                      v-model="problemSettings.supportedLanguages"
                      :id="'lang-' + lang.value"
                    >
                    <label class="form-check-label" :for="'lang-' + lang.value">
                      {{ lang.label }}
                    </label>
                  </div>
                </div>
              </div>
              <div class="mb-3">
                <div class="form-check">
                  <input 
                    class="form-check-input" 
                    type="checkbox" 
                    v-model="problemSettings.requireApproval"
                    id="requireApproval"
                  >
                  <label class="form-check-label" for="requireApproval">
                    新题目需要审核
                  </label>
                </div>
              </div>
              <div class="mb-3">
                <div class="form-check">
                  <input 
                    class="form-check-input" 
                    type="checkbox" 
                    v-model="problemSettings.allowUserSubmit"
                    id="allowUserSubmit"
                  >
                  <label class="form-check-label" for="allowUserSubmit">
                    允许用户提交题目
                  </label>
                </div>
              </div>
              <button type="submit" class="btn btn-primary">保存设置</button>
            </form>
          </div>
        </div>
      </div>

      <!-- 系统维护 -->
      <div class="col-lg-6 mb-4">
        <div class="card shadow">
          <div class="card-header">
            <h6 class="m-0 font-weight-bold text-primary">系统维护</h6>
          </div>
          <div class="card-body">
            <div class="mb-3">
              <label class="form-label">系统状态</label>
              <div class="d-flex align-items-center">
                <span class="badge bg-success me-2">正常运行</span>
                <small class="text-muted">上次重启: {{ lastRestart }}</small>
              </div>
            </div>
            
            <div class="mb-3">
              <label class="form-label">数据库状态</label>
              <div class="d-flex align-items-center">
                <span class="badge bg-success me-2">连接正常</span>
                <small class="text-muted">响应时间: {{ dbResponseTime }}ms</small>
              </div>
            </div>

            <div class="mb-3">
              <label class="form-label">缓存状态</label>
              <div class="d-flex align-items-center">
                <span class="badge bg-success me-2">运行正常</span>
                <small class="text-muted">命中率: {{ cacheHitRate }}%</small>
              </div>
            </div>

            <hr>

            <div class="mb-3">
              <button class="btn btn-warning me-2" @click="clearCache">
                <i class="bi bi-arrow-clockwise"></i>
                清理缓存
              </button>
              <button class="btn btn-info me-2" @click="backupData">
                <i class="bi bi-download"></i>
                备份数据
              </button>
              <button class="btn btn-secondary" @click="viewLogs">
                <i class="bi bi-file-text"></i>
                查看日志
              </button>
            </div>

            <div class="mb-3">
              <div class="form-check">
                <input 
                  class="form-check-input" 
                  type="checkbox" 
                  v-model="maintenanceSettings.maintenanceMode"
                  id="maintenanceMode"
                >
                <label class="form-check-label" for="maintenanceMode">
                  维护模式
                </label>
              </div>
              <small class="text-muted">启用后，普通用户将无法访问系统</small>
            </div>

            <div v-if="maintenanceSettings.maintenanceMode" class="mb-3">
              <label class="form-label">维护公告</label>
              <textarea 
                class="form-control" 
                rows="3"
                v-model="maintenanceSettings.maintenanceMessage"
                placeholder="系统正在维护中，预计恢复时间..."
              ></textarea>
            </div>

            <button class="btn btn-primary" @click="saveMaintenanceSettings">
              保存维护设置
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 系统信息 -->
    <div class="row">
      <div class="col-12">
        <div class="card shadow">
          <div class="card-header">
            <h6 class="m-0 font-weight-bold text-primary">系统信息</h6>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-3">
                <div class="text-center">
                  <div class="h4 text-primary">{{ systemInfo.version }}</div>
                  <small class="text-muted">系统版本</small>
                </div>
              </div>
              <div class="col-md-3">
                <div class="text-center">
                  <div class="h4 text-success">{{ systemInfo.uptime }}</div>
                  <small class="text-muted">运行时间</small>
                </div>
              </div>
              <div class="col-md-3">
                <div class="text-center">
                  <div class="h4 text-info">{{ systemInfo.memoryUsage }}%</div>
                  <small class="text-muted">内存使用率</small>
                </div>
              </div>
              <div class="col-md-3">
                <div class="text-center">
                  <div class="h4 text-warning">{{ systemInfo.diskUsage }}%</div>
                  <small class="text-muted">磁盘使用率</small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AdminSettings',
  data() {
    return {
      basicSettings: {
        siteName: 'Code-Combined',
        siteDescription: '专业的算法题集学习平台',
        siteKeywords: '算法,编程,题集,学习',
        contactEmail: '<EMAIL>',
        allowRegistration: true,
        requireEmailVerification: true
      },
      userSettings: {
        defaultRole: 'user',
        initialPoints: 100,
        dailyLoginPoints: 10,
        solvePoints: 20,
        createProblemPoints: 50,
        enablePointsSystem: true
      },
      problemSettings: {
        defaultTimeLimit: 1000,
        defaultMemoryLimit: 256,
        supportedLanguages: ['python', 'java', 'cpp', 'javascript'],
        requireApproval: false,
        allowUserSubmit: true
      },
      maintenanceSettings: {
        maintenanceMode: false,
        maintenanceMessage: '系统正在维护中，预计30分钟后恢复正常。'
      },
      availableLanguages: [
        { value: 'python', label: 'Python' },
        { value: 'java', label: 'Java' },
        { value: 'cpp', label: 'C++' },
        { value: 'c', label: 'C' },
        { value: 'javascript', label: 'JavaScript' },
        { value: 'go', label: 'Go' },
        { value: 'rust', label: 'Rust' },
        { value: 'kotlin', label: 'Kotlin' }
      ],
      systemInfo: {
        version: 'v1.0.0',
        uptime: '15天 8小时',
        memoryUsage: 68,
        diskUsage: 45
      },
      lastRestart: '2025-06-09 14:30:00',
      dbResponseTime: 12,
      cacheHitRate: 95.8
    }
  },
  methods: {
    saveBasicSettings() {
      // Mock 保存基本设置
      this.$toast.success('基本设置保存成功')
    },
    saveUserSettings() {
      // Mock 保存用户设置
      this.$toast.success('用户设置保存成功')
    },
    saveProblemSettings() {
      // Mock 保存题目设置
      this.$toast.success('题目设置保存成功')
    },
    saveMaintenanceSettings() {
      // Mock 保存维护设置
      this.$toast.success('维护设置保存成功')
    },
    clearCache() {
      if (confirm('确定要清理系统缓存吗？')) {
        // Mock 清理缓存
        this.$toast.success('缓存清理成功')
      }
    },
    backupData() {
      if (confirm('确定要备份系统数据吗？')) {
        // Mock 备份数据
        this.$toast.success('数据备份已开始，请稍后查看备份文件')
      }
    },
    viewLogs() {
      // Mock 查看日志
      alert('日志查看功能开发中...')
    }
  },
  mounted() {
    // 模拟加载系统信息
    setInterval(() => {
      this.systemInfo.memoryUsage = Math.floor(Math.random() * 20) + 60
      this.dbResponseTime = Math.floor(Math.random() * 10) + 8
      this.cacheHitRate = (Math.random() * 5 + 93).toFixed(1)
    }, 5000)
  }
}
</script>

<style scoped>
.form-check-group {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  padding: 0.75rem;
}

.form-check {
  margin-bottom: 0.5rem;
}

.form-check:last-child {
  margin-bottom: 0;
}

.card-header h6 {
  color: #5a5c69;
}

.badge {
  font-size: 0.8rem;
}

.text-center .h4 {
  margin-bottom: 0.25rem;
}
</style>

<template>
  <div class="admin-overview">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>管理控制台</h2>
      <p class="text-muted">系统数据概览和快速操作</p>
    </div>

    <!-- 统计卡片 -->
    <div class="row mb-4">
      <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
          <div class="card-body">
            <div class="row no-gutters align-items-center">
              <div class="col mr-2">
                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                  总用户数
                </div>
                <div class="h5 mb-0 font-weight-bold text-gray-800">
                  {{ stats.totalUsers.toLocaleString() }}
                </div>
              </div>
              <div class="col-auto">
                <i class="bi bi-people fa-2x text-gray-300"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
          <div class="card-body">
            <div class="row no-gutters align-items-center">
              <div class="col mr-2">
                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                  总题目数
                </div>
                <div class="h5 mb-0 font-weight-bold text-gray-800">
                  {{ stats.totalProblems.toLocaleString() }}
                </div>
              </div>
              <div class="col-auto">
                <i class="bi bi-code-square fa-2x text-gray-300"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
          <div class="card-body">
            <div class="row no-gutters align-items-center">
              <div class="col mr-2">
                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                  总题集数
                </div>
                <div class="h5 mb-0 font-weight-bold text-gray-800">
                  {{ stats.totalProblemSets.toLocaleString() }}
                </div>
              </div>
              <div class="col-auto">
                <i class="bi bi-collection fa-2x text-gray-300"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
          <div class="card-body">
            <div class="row no-gutters align-items-center">
              <div class="col mr-2">
                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                  今日活跃用户
                </div>
                <div class="h5 mb-0 font-weight-bold text-gray-800">
                  {{ stats.activeUsers.toLocaleString() }}
                </div>
              </div>
              <div class="col-auto">
                <i class="bi bi-graph-up fa-2x text-gray-300"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表和最近活动 -->
    <div class="row">
      <!-- 用户增长图表 -->
      <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
          <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">用户增长趋势</h6>
            <div class="dropdown no-arrow">
              <a class="dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                <i class="bi bi-three-dots-vertical text-gray-400"></i>
              </a>
              <div class="dropdown-menu dropdown-menu-right shadow">
                <a class="dropdown-item" href="#">导出数据</a>
                <a class="dropdown-item" href="#">查看详情</a>
              </div>
            </div>
          </div>
          <div class="card-body">
            <div class="chart-area">
              <canvas id="userGrowthChart" width="400" height="200"></canvas>
            </div>
          </div>
        </div>
      </div>

      <!-- 最近活动 -->
      <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
          <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">最近活动</h6>
          </div>
          <div class="card-body">
            <div class="activity-list">
              <div 
                v-for="activity in recentActivities" 
                :key="activity.id"
                class="activity-item"
              >
                <div class="activity-icon" :class="activity.iconClass">
                  <i :class="activity.icon"></i>
                </div>
                <div class="activity-content">
                  <div class="activity-text">{{ activity.text }}</div>
                  <div class="activity-time">{{ formatTime(activity.time) }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="row">
      <div class="col-12">
        <div class="card shadow mb-4">
          <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">快速操作</h6>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-3 mb-3">
                <button class="btn btn-primary btn-block" @click="createUser">
                  <i class="bi bi-person-plus"></i>
                  创建用户
                </button>
              </div>
              <div class="col-md-3 mb-3">
                <button class="btn btn-success btn-block" @click="createProblem">
                  <i class="bi bi-plus-circle"></i>
                  创建题目
                </button>
              </div>
              <div class="col-md-3 mb-3">
                <button class="btn btn-info btn-block" @click="createProblemSet">
                  <i class="bi bi-collection-fill"></i>
                  创建题集
                </button>
              </div>
              <div class="col-md-3 mb-3">
                <button class="btn btn-warning btn-block" @click="exportData">
                  <i class="bi bi-download"></i>
                  导出数据
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AdminOverview',
  data() {
    return {
      stats: {
        totalUsers: 1248,
        totalProblems: 856,
        totalProblemSets: 124,
        activeUsers: 89
      },
      recentActivities: [
        {
          id: 1,
          text: '用户 张三 创建了新题目《两数之和》',
          time: new Date(Date.now() - 5 * 60 * 1000),
          icon: 'bi bi-plus-circle',
          iconClass: 'bg-success'
        },
        {
          id: 2,
          text: '管理员 李四 审核通过了题集《算法基础》',
          time: new Date(Date.now() - 15 * 60 * 1000),
          icon: 'bi bi-check-circle',
          iconClass: 'bg-primary'
        },
        {
          id: 3,
          text: '用户 王五 提交了题目反馈',
          time: new Date(Date.now() - 30 * 60 * 1000),
          icon: 'bi bi-chat-dots',
          iconClass: 'bg-info'
        },
        {
          id: 4,
          text: '系统自动备份数据完成',
          time: new Date(Date.now() - 2 * 60 * 60 * 1000),
          icon: 'bi bi-shield-check',
          iconClass: 'bg-warning'
        },
        {
          id: 5,
          text: '新用户 赵六 完成注册',
          time: new Date(Date.now() - 3 * 60 * 60 * 1000),
          icon: 'bi bi-person-plus',
          iconClass: 'bg-secondary'
        }
      ]
    }
  },
  methods: {
    formatTime(time) {
      const now = new Date()
      const diff = now - time
      const minutes = Math.floor(diff / (1000 * 60))
      const hours = Math.floor(diff / (1000 * 60 * 60))
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))

      if (minutes < 60) {
        return `${minutes}分钟前`
      } else if (hours < 24) {
        return `${hours}小时前`
      } else {
        return `${days}天前`
      }
    },
    createUser() {
      this.$emit('switch-tab', 'users')
    },
    createProblem() {
      this.$emit('switch-tab', 'problems')
    },
    createProblemSet() {
      this.$emit('switch-tab', 'problemsets')
    },
    exportData() {
      // 导出功能
      alert('数据导出功能开发中...')
    }
  },
  mounted() {
    this.loadStats()
  },
  methods: {
    ...this.methods,
    loadStats() {
      // 加载统计数据
      // TODO: 调用后端API获取真实统计数据
    }
  }
}
</script>

<style scoped>
.page-header {
  margin-bottom: 2rem;
}

.border-left-primary {
  border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
  border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
  border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
  border-left: 0.25rem solid #f6c23e !important;
}

.text-xs {
  font-size: 0.7rem;
}

.activity-list {
  max-height: 400px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #e3e6f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  color: white;
}

.activity-content {
  flex: 1;
}

.activity-text {
  font-size: 0.9rem;
  color: #5a5c69;
  margin-bottom: 0.25rem;
}

.activity-time {
  font-size: 0.75rem;
  color: #858796;
}

.btn-block {
  width: 100%;
}

.chart-area {
  position: relative;
  height: 200px;
}
</style>

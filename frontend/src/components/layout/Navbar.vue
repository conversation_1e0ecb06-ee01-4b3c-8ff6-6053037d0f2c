<template>
  <header class="navbar-container">
    <div class="container-fluid d-flex align-items-center justify-content-between">
      <div class="d-flex align-items-center">
        <!-- Logo -->
        <router-link class="navbar-brand" to="/">
          <img src="/logo.svg" alt="Code-Combined Logo" class="logo-img">
        </router-link>

        <!-- 桌面端导航菜单 -->
        <nav class="nav-links d-none d-md-flex">
          <router-link class="nav-item" to="/problems">题目</router-link>
          <router-link class="nav-item" to="/problemsets">题集</router-link>
          <router-link class="nav-item" to="/dashboard">学习中心</router-link>
        </nav>

        <!-- 移动端菜单按钮 -->
        <button
          class="mobile-menu-btn d-md-none ms-3"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#mobileNavMenu"
          aria-controls="mobileNavMenu"
          aria-expanded="false"
          aria-label="Toggle navigation"
        >
          <i class="bi bi-list"></i>
        </button>
      </div>

      <!-- 右侧操作区 -->
      <div class="nav-actions d-flex align-items-center">
        <template v-if="isAuthenticated">
          <!-- 创建按钮 -->
          <div class="dropdown me-3">
            <a href="#" class="nav-action-item" role="button" data-bs-toggle="dropdown" aria-expanded="false">
              <i class="bi bi-plus-lg"></i>
              <i class="bi bi-caret-down-fill caret"></i>
            </a>
            <ul class="dropdown-menu dropdown-menu-end">
              <li><h6 class="dropdown-header">创建</h6></li>
              <li>
                <router-link class="dropdown-item" to="/problems/create">新题目</router-link>
              </li>
              <li>
                <router-link class="dropdown-item" to="/problemsets/create">新题集</router-link>
              </li>
            </ul>
          </div>
          
          <!-- 用户菜单 -->
          <div class="dropdown">
            <a href="#" class="nav-action-item" role="button" data-bs-toggle="dropdown" aria-expanded="false">
              <img :src="user?.avatar || 'https://i.pravatar.cc/40'" alt="User Avatar" class="user-avatar rounded-circle">
              <i class="bi bi-caret-down-fill caret"></i>
            </a>
            <ul class="dropdown-menu dropdown-menu-end">
              <li>
                <div class="px-3 py-2 border-bottom">
                  <div class="small">Signed in as</div>
                  <div class="fw-bold">{{ user?.nickname || user?.username }}</div>
                </div>
              </li>
              <li>
                <router-link class="dropdown-item" to="/profile">个人主页</router-link>
              </li>
              <li v-if="isAdmin">
                <router-link class="dropdown-item" to="/admin">管理后台</router-link>
              </li>
              <li><hr class="dropdown-divider my-1"></li>
              <li>
                <a class="dropdown-item" href="#" @click.prevent="handleLogout">退出登录</a>
              </li>
            </ul>
          </div>
        </template>
        
        <template v-else>
          <router-link to="/login" class="btn btn-link text-white text-decoration-none me-2">
            登录
          </router-link>
          <router-link to="/register" class="btn btn-outline-light btn-sm">
            注册
          </router-link>
        </template>
      </div>
    </div>

    <!-- 移动端折叠菜单 -->
    <div class="collapse d-md-none" id="mobileNavMenu">
      <div class="container-fluid">
        <div class="mobile-nav-content py-3">
          <!-- 移动端导航链接 -->
          <div class="mobile-nav-links mb-3">
            <router-link class="mobile-nav-item" to="/problems" @click="closeMobileMenu">
              <i class="bi bi-puzzle me-2"></i>题目
            </router-link>
            <router-link class="mobile-nav-item" to="/problemsets" @click="closeMobileMenu">
              <i class="bi bi-collection me-2"></i>题集
            </router-link>
            <router-link class="mobile-nav-item" to="/dashboard" @click="closeMobileMenu">
              <i class="bi bi-speedometer2 me-2"></i>学习中心
            </router-link>
          </div>

          <!-- 移动端用户操作 -->
          <div class="mobile-user-actions">
            <template v-if="isAuthenticated">
              <div class="mobile-user-info mb-3 p-3 bg-light rounded">
                <div class="d-flex align-items-center">
                  <img :src="user?.avatar || 'https://i.pravatar.cc/40'" alt="User Avatar" class="user-avatar rounded-circle me-3">
                  <div>
                    <div class="fw-bold">{{ user?.nickname || user?.username }}</div>
                    <div class="small text-muted">{{ user?.email }}</div>
                  </div>
                </div>
              </div>

              <div class="mobile-action-links">
                <router-link class="mobile-action-item" to="/profile" @click="closeMobileMenu">
                  <i class="bi bi-person me-2"></i>个人主页
                </router-link>
                <router-link class="mobile-action-item" to="/problems/create" @click="closeMobileMenu">
                  <i class="bi bi-plus-circle me-2"></i>创建题目
                </router-link>
                <router-link class="mobile-action-item" to="/problemsets/create" @click="closeMobileMenu">
                  <i class="bi bi-folder-plus me-2"></i>创建题集
                </router-link>
                <router-link v-if="isAdmin" class="mobile-action-item" to="/admin" @click="closeMobileMenu">
                  <i class="bi bi-shield-check me-2"></i>管理后台
                </router-link>
                <a class="mobile-action-item text-danger" href="#" @click.prevent="handleLogout">
                  <i class="bi bi-box-arrow-right me-2"></i>退出登录
                </a>
              </div>
            </template>

            <template v-else>
              <div class="mobile-auth-links">
                <router-link class="mobile-action-item" to="/login" @click="closeMobileMenu">
                  <i class="bi bi-box-arrow-in-right me-2"></i>登录
                </router-link>
                <router-link class="mobile-action-item" to="/register" @click="closeMobileMenu">
                  <i class="bi bi-person-plus me-2"></i>注册
                </router-link>
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { showSuccess } from '@/utils/message'

const store = useStore()
const router = useRouter()

const isAuthenticated = computed(() => store.getters['auth/isAuthenticated'])
const user = computed(() => store.getters['auth/user'])
const isAdmin = computed(() => store.getters['auth/isAdmin'])

const handleLogout = () => {
  store.dispatch('auth/logout')
  router.push('/')
  showSuccess('您已成功退出登录')
  closeMobileMenu()
}

// 关闭移动端菜单
const closeMobileMenu = () => {
  const mobileMenu = document.getElementById('mobileNavMenu')
  if (mobileMenu && mobileMenu.classList.contains('show')) {
    // 使用 Bootstrap 的 Collapse API 关闭菜单
    const bsCollapse = new (window as any).bootstrap.Collapse(mobileMenu, {
      toggle: false
    })
    bsCollapse.hide()
  }
}
</script>

<style scoped>
.navbar-container {
  background: #fff;
  color: var(--bs-dark);
  padding: 1rem 0;
  border-bottom: 1px solid var(--bs-border);
  box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.03);
  position: sticky;
  top: 0;
  z-index: 100;
}

.navbar-brand .logo-img {
  height: 32px;
  margin-right: 8px;
  vertical-align: middle;
  
}

.nav-links {
  gap: 1rem;
  margin-left: 1rem;
}
.nav-links .nav-item {
  color: var(--bs-dark);
  font-weight: 600;
  font-size: 1rem;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: var(--bs-radius);
  transition: background 0.15s, color 0.15s;
}
.nav-links .nav-item:hover, .nav-links .nav-item.router-link-exact-active {
  background: #e7f1ff;
  color: var(--bs-primary);
}

.nav-actions {
  gap: 1rem;
}

.nav-action-item {
  color: var(--bs-dark);
  text-decoration: none;
  display: flex;
  align-items: center;
}
.nav-action-item .bi-plus-lg {
  font-size: 1.2rem;
}
.nav-action-item .caret {
  width: 8px;
  margin-left: 4px;
  opacity: 0.8;
}

.user-avatar {
  width: 28px;
  height: 28px;
  object-fit: cover;
  border-radius: 50%;
  border: 1.5px solid var(--bs-border);
}

.dropdown-menu {
  background: #fff;
  border-radius: var(--bs-radius);
  box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.08);
  border: 1px solid var(--bs-border);
  font-size: 1rem;
}
.dropdown-item {
  color: var(--bs-dark);
  padding: 0.5rem 1.25rem;
  border-radius: var(--bs-radius);
}
.dropdown-item:hover {
  background: #e7f1ff;
  color: var(--bs-primary);
}
.dropdown-header {
  font-size: 12px;
}

.btn-outline-light {
  border-color: var(--bs-primary);
  color: var(--bs-primary);
  background: #fff;
  font-weight: 600;
  border-radius: var(--bs-radius);
  transition: background 0.15s, color 0.15s;
}

/* 移动端菜单样式 */
.mobile-menu-btn {
  background: none;
  border: none;
  color: var(--bs-dark);
  font-size: 1.5rem;
  padding: 0.5rem;
  border-radius: var(--bs-radius);
  transition: background 0.15s;
}
.mobile-menu-btn:hover {
  background: #e7f1ff;
  color: var(--bs-primary);
}

.mobile-nav-content {
  border-top: 1px solid var(--bs-border);
  background: #fff;
}

.mobile-nav-links {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.mobile-nav-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: var(--bs-dark);
  text-decoration: none;
  border-radius: var(--bs-radius);
  font-weight: 600;
  transition: background 0.15s, color 0.15s;
}
.mobile-nav-item:hover, .mobile-nav-item.router-link-exact-active {
  background: #e7f1ff;
  color: var(--bs-primary);
}

.mobile-user-info {
  border: 1px solid var(--bs-border);
}

.mobile-action-links {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.mobile-action-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: var(--bs-dark);
  text-decoration: none;
  border-radius: var(--bs-radius);
  transition: background 0.15s, color 0.15s;
}
.mobile-action-item:hover {
  background: #e7f1ff;
  color: var(--bs-primary);
}
.mobile-action-item.text-danger:hover {
  background: #f8d7da;
  color: #dc3545;
}

.mobile-auth-links {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}
.btn-outline-light:hover {
  border-color: var(--bs-primary-hover);
  background: #e7f1ff;
  color: var(--bs-primary-hover);
}
.btn-link.text-white {
  color: var(--bs-primary) !important;
  font-weight: 600;
  text-decoration: underline;
}
.btn-link.text-white:hover {
  color: var(--bs-primary-hover) !important;
}
</style>

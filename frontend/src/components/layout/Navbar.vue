<template>
  <header class="navbar-container">
    <div class="container-fluid d-flex align-items-center justify-content-between">
      <div class="d-flex align-items-center">
        <!-- Logo -->
        <router-link class="navbar-brand" to="/">
          <img src="/logo.svg" alt="Code-Combined Logo" class="logo-img">
        </router-link>

        <!-- 导航菜单 -->
        <nav class="nav-links d-none d-md-flex">
          <router-link class="nav-item" to="/problems">题目</router-link>
          <router-link class="nav-item" to="/problemsets">题集</router-link>
          <router-link class="nav-item" to="/dashboard">学习中心</router-link>
        </nav>
      </div>

      <!-- 右侧操作区 -->
      <div class="nav-actions d-flex align-items-center">
        <template v-if="isAuthenticated">
          <!-- 创建按钮 -->
          <div class="dropdown me-3">
            <a href="#" class="nav-action-item" role="button" data-bs-toggle="dropdown" aria-expanded="false">
              <i class="bi bi-plus-lg"></i>
              <i class="bi bi-caret-down-fill caret"></i>
            </a>
            <ul class="dropdown-menu dropdown-menu-end">
              <li><h6 class="dropdown-header">创建</h6></li>
              <li>
                <router-link class="dropdown-item" to="/problems/create">新题目</router-link>
              </li>
              <li>
                <router-link class="dropdown-item" to="/problemsets/create">新题集</router-link>
              </li>
            </ul>
          </div>
          
          <!-- 用户菜单 -->
          <div class="dropdown">
            <a href="#" class="nav-action-item" role="button" data-bs-toggle="dropdown" aria-expanded="false">
              <img :src="user?.avatar || 'https://i.pravatar.cc/40'" alt="User Avatar" class="user-avatar rounded-circle">
              <i class="bi bi-caret-down-fill caret"></i>
            </a>
            <ul class="dropdown-menu dropdown-menu-end">
              <li>
                <div class="px-3 py-2 border-bottom">
                  <div class="small">Signed in as</div>
                  <div class="fw-bold">{{ user?.nickname || user?.username }}</div>
                </div>
              </li>
              <li>
                <router-link class="dropdown-item" to="/profile">个人主页</router-link>
              </li>
              <li v-if="isAdmin">
                <router-link class="dropdown-item" to="/admin">管理后台</router-link>
              </li>
              <li><hr class="dropdown-divider my-1"></li>
              <li>
                <a class="dropdown-item" href="#" @click.prevent="handleLogout">退出登录</a>
              </li>
            </ul>
          </div>
        </template>
        
        <template v-else>
          <router-link to="/login" class="btn btn-link text-white text-decoration-none me-2">
            登录
          </router-link>
          <router-link to="/register" class="btn btn-outline-light btn-sm">
            注册
          </router-link>
        </template>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { showSuccess } from '@/utils/message'

const store = useStore()
const router = useRouter()

const isAuthenticated = computed(() => store.getters['auth/isAuthenticated'])
const user = computed(() => store.getters['auth/user'])
const isAdmin = computed(() => store.getters['auth/isAdmin'])

const handleLogout = () => {
  store.dispatch('auth/logout')
  router.push('/')
  showSuccess('您已成功退出登录')
}
</script>

<style scoped>
.navbar-container {
  background: #fff;
  color: var(--bs-dark);
  padding: 1rem 0;
  border-bottom: 1px solid var(--bs-border);
  box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.03);
  position: sticky;
  top: 0;
  z-index: 100;
}

.navbar-brand .logo-img {
  height: 32px;
  margin-right: 8px;
  vertical-align: middle;
  
}

.nav-links {
  gap: 1rem;
  margin-left: 1rem;
}
.nav-links .nav-item {
  color: var(--bs-dark);
  font-weight: 600;
  font-size: 1rem;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: var(--bs-radius);
  transition: background 0.15s, color 0.15s;
}
.nav-links .nav-item:hover, .nav-links .nav-item.router-link-exact-active {
  background: #e7f1ff;
  color: var(--bs-primary);
}

.nav-actions {
  gap: 1rem;
}

.nav-action-item {
  color: var(--bs-dark);
  text-decoration: none;
  display: flex;
  align-items: center;
}
.nav-action-item .bi-plus-lg {
  font-size: 1.2rem;
}
.nav-action-item .caret {
  width: 8px;
  margin-left: 4px;
  opacity: 0.8;
}

.user-avatar {
  width: 28px;
  height: 28px;
  object-fit: cover;
  border-radius: 50%;
  border: 1.5px solid var(--bs-border);
}

.dropdown-menu {
  background: #fff;
  border-radius: var(--bs-radius);
  box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.08);
  border: 1px solid var(--bs-border);
  font-size: 1rem;
}
.dropdown-item {
  color: var(--bs-dark);
  padding: 0.5rem 1.25rem;
  border-radius: var(--bs-radius);
}
.dropdown-item:hover {
  background: #e7f1ff;
  color: var(--bs-primary);
}
.dropdown-header {
  font-size: 12px;
}

.btn-outline-light {
  border-color: var(--bs-primary);
  color: var(--bs-primary);
  background: #fff;
  font-weight: 600;
  border-radius: var(--bs-radius);
  transition: background 0.15s, color 0.15s;
}
.btn-outline-light:hover {
  border-color: var(--bs-primary-hover);
  background: #e7f1ff;
  color: var(--bs-primary-hover);
}
.btn-link.text-white {
  color: var(--bs-primary) !important;
  font-weight: 600;
  text-decoration: underline;
}
.btn-link.text-white:hover {
  color: var(--bs-primary-hover) !important;
}
</style>

<template>
  <footer class="footer-apple">
    <div class="footer-container">
      <div class="footer-main">
        <div class="footer-brand">
          <div class="footer-logo-row">
            <img src="/logo.svg" alt="logo" class="footer-logo" />
            <span class="footer-title">Code-Combined</span>
          </div>
          <div class="footer-desc">专业的算法题集管理平台，帮助你更好地组织和学习算法题目。</div>
          <div class="footer-social">
            <a href="#" class="footer-social-link" aria-label="GitHub">
              <i class="bi bi-github"></i>
            </a>
            <a href="#" class="footer-social-link" aria-label="Email">
              <i class="bi bi-envelope"></i>
            </a>
            <a href="#" class="footer-social-link" aria-label="Twitter">
              <i class="bi bi-twitter"></i>
            </a>
          </div>
        </div>
        <div class="footer-links">
          <div class="footer-link-group">
            <div class="footer-link-title">快速链接</div>
            <router-link to="/problemsets" class="footer-link">题集列表</router-link>
            <router-link to="/problems" class="footer-link">题目列表</router-link>
            <a href="#" class="footer-link">使用帮助</a>
            <a href="#" class="footer-link">API文档</a>
          </div>
          <div class="footer-link-group">
            <div class="footer-link-title">关于我们</div>
            <a href="#" class="footer-link">团队介绍</a>
            <a href="#" class="footer-link">联系我们</a>
            <a href="#" class="footer-link">隐私政策</a>
            <a href="#" class="footer-link">服务条款</a>
          </div>
        </div>
      </div>
      <div class="footer-divider"></div>
      <div class="footer-bottom">
        <div class="footer-copyright">
          &copy; 2025 Code-Combined. All rights reserved.
        </div>
        <div class="footer-powered">
          Built with Vue 3 &amp; Spring Boot
        </div>
      </div>
    </div>
  </footer>
</template>

<script>
export default {
  name: 'Footer'
}
</script>

<style scoped>
.footer-apple {
  background: #fff;
  border-top-left-radius: 28px;
  border-top-right-radius: 28px;
  box-shadow: 0 -2px 16px 0 rgba(60,60,60,0.06);
  margin-top: 64px;
  padding: 0;
}
.footer-container {
  max-width: 1100px;
  margin: 0 auto;
  padding: 36px 24px 18px 24px;
  display: flex;
  flex-direction: column;
  gap: 0;
}
.footer-main {
  display: flex;
  flex-wrap: wrap;
  gap: 32px;
  justify-content: space-between;
}
.footer-brand {
  min-width: 220px;
  max-width: 340px;
  flex: 1 1 220px;
  display: flex;
  flex-direction: column;
  gap: 18px;
}
.footer-logo-row {
  display: flex;
  align-items: center;
  gap: 10px;
}
.footer-logo {
  width: 38px;
  height: 38px;
  border-radius: 12px;
}
.footer-title {
  font-size: 1.35rem;
  font-weight: 700;
  color: #111;
  letter-spacing: 0.01em;
}
.footer-desc {
  color: #888;
  font-size: 1rem;
  margin-bottom: 2px;
}
.footer-social {
  display: flex;
  gap: 18px;
  margin-top: 2px;
}
.footer-social-link {
  color: #b0b3b8;
  font-size: 1.35rem;
  transition: color 0.18s;
}
.footer-social-link:hover {
  color: #007aff;
}
.footer-links {
  display: flex;
  gap: 48px;
  flex: 2 1 320px;
  justify-content: flex-end;
}
.footer-link-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
  min-width: 120px;
}
.footer-link-title {
  font-size: 1.05rem;
  font-weight: 600;
  color: #222;
  margin-bottom: 6px;
}
.footer-link {
  color: #888;
  text-decoration: none;
  font-size: 1rem;
  padding: 2px 0;
  border-radius: 8px;
  transition: color 0.18s, background 0.18s;
  display: block;
}
.footer-link:hover {
  color: #007aff;
  background: #f1f2f6;
}
.footer-divider {
  height: 1px;
  background: linear-gradient(90deg, #f1f2f6 0%, #e5e9f2 100%);
  margin: 32px 0 18px 0;
  border-radius: 1px;
}
.footer-bottom {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
  font-size: 0.98rem;
  color: #b0b3b8;
}
.footer-powered {
  text-align: right;
}
@media (max-width: 800px) {
  .footer-main {
    flex-direction: column;
    gap: 24px;
  }
  .footer-links {
    flex-direction: column;
    gap: 18px;
    justify-content: flex-start;
  }
  .footer-bottom {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
  }
}
</style>

<template>
  <div class="edit-profile-tab">
    <form @submit.prevent="handleSubmit">
      <div class="row">
        <!-- 基本信息 -->
        <div class="col-lg-6">
          <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-transparent border-0">
              <h5 class="mb-0">
                <i class="bi bi-person me-2"></i>
                基本信息
              </h5>
            </div>
            <div class="card-body">
              <!-- 昵称 -->
              <div class="mb-3">
                <label for="nickname" class="form-label">昵称</label>
                <input
                  id="nickname"
                  v-model="form.nickname"
                  type="text"
                  class="form-control"
                  :class="{ 'is-invalid': errors.nickname }"
                  placeholder="请输入昵称"
                  maxlength="50"
                >
                <div v-if="errors.nickname" class="invalid-feedback">
                  {{ errors.nickname }}
                </div>
              </div>

              <!-- 个人简介 -->
              <div class="mb-3">
                <label for="bio" class="form-label">个人简介</label>
                <textarea
                  id="bio"
                  v-model="form.bio"
                  class="form-control"
                  rows="4"
                  placeholder="介绍一下自己..."
                  maxlength="200"
                ></textarea>
                <div class="form-text">
                  {{ form.bio.length }}/200 字符
                </div>
              </div>

              <!-- 性别 -->
              <div class="mb-3">
                <label class="form-label">性别</label>
                <div class="row">
                  <div class="col-4">
                    <div class="form-check">
                      <input
                        id="male"
                        v-model="form.gender"
                        type="radio"
                        value="MALE"
                        class="form-check-input"
                        name="gender"
                      >
                      <label for="male" class="form-check-label">男</label>
                    </div>
                  </div>
                  <div class="col-4">
                    <div class="form-check">
                      <input
                        id="female"
                        v-model="form.gender"
                        type="radio"
                        value="FEMALE"
                        class="form-check-input"
                        name="gender"
                      >
                      <label for="female" class="form-check-label">女</label>
                    </div>
                  </div>
                  <div class="col-4">
                    <div class="form-check">
                      <input
                        id="other"
                        v-model="form.gender"
                        type="radio"
                        value="OTHER"
                        class="form-check-input"
                        name="gender"
                      >
                      <label for="other" class="form-check-label">其他</label>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 生日 -->
              <div class="mb-3">
                <label for="birthday" class="form-label">生日</label>
                <input
                  id="birthday"
                  v-model="form.birthday"
                  type="date"
                  class="form-control"
                >
              </div>
            </div>
          </div>
        </div>

        <!-- 联系信息 -->
        <div class="col-lg-6">
          <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-transparent border-0">
              <h5 class="mb-0">
                <i class="bi bi-telephone me-2"></i>
                联系信息
              </h5>
            </div>
            <div class="card-body">
              <!-- 邮箱（只读） -->
              <div class="mb-3">
                <label for="email" class="form-label">邮箱</label>
                <input
                  id="email"
                  :value="profile.email"
                  type="email"
                  class="form-control"
                  readonly
                  disabled
                >
                <div class="form-text">
                  邮箱地址不可修改
                </div>
              </div>

              <!-- 手机号 -->
              <div class="mb-3">
                <label for="phone" class="form-label">手机号</label>
                <input
                  id="phone"
                  v-model="form.phone"
                  type="tel"
                  class="form-control"
                  placeholder="请输入手机号"
                >
              </div>

              <!-- 所在地 -->
              <div class="mb-3">
                <label for="location" class="form-label">所在地</label>
                <input
                  id="location"
                  v-model="form.location"
                  type="text"
                  class="form-control"
                  placeholder="如：北京市"
                >
              </div>

              <!-- 个人网站 -->
              <div class="mb-3">
                <label for="website" class="form-label">个人网站</label>
                <input
                  id="website"
                  v-model="form.website"
                  type="url"
                  class="form-control"
                  placeholder="https://example.com"
                >
              </div>
            </div>
          </div>
        </div>

        <!-- 工作信息 -->
        <div class="col-12">
          <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-transparent border-0">
              <h5 class="mb-0">
                <i class="bi bi-briefcase me-2"></i>
                工作信息
              </h5>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-6">
                  <!-- 公司 -->
                  <div class="mb-3">
                    <label for="company" class="form-label">公司</label>
                    <input
                      id="company"
                      v-model="form.company"
                      type="text"
                      class="form-control"
                      placeholder="请输入公司名称"
                    >
                  </div>
                </div>
                <div class="col-md-6">
                  <!-- 职位 -->
                  <div class="mb-3">
                    <label for="position" class="form-label">职位</label>
                    <input
                      id="position"
                      v-model="form.position"
                      type="text"
                      class="form-control"
                      placeholder="请输入职位"
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 社交链接 -->
        <div class="col-12">
          <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-transparent border-0">
              <h5 class="mb-0">
                <i class="bi bi-link-45deg me-2"></i>
                社交链接
              </h5>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-6">
                  <!-- GitHub -->
                  <div class="mb-3">
                    <label for="github" class="form-label">
                      <i class="bi bi-github me-1"></i>
                      GitHub
                    </label>
                    <input
                      id="github"
                      v-model="form.github"
                      type="url"
                      class="form-control"
                      placeholder="https://github.com/username"
                    >
                  </div>
                </div>
                <div class="col-md-6">
                  <!-- LeetCode -->
                  <div class="mb-3">
                    <label for="leetcode" class="form-label">
                      <i class="bi bi-code-square me-1"></i>
                      LeetCode
                    </label>
                    <input
                      id="leetcode"
                      v-model="socialLinks.leetcode"
                      type="url"
                      class="form-control"
                      placeholder="https://leetcode.cn/u/username"
                    >
                  </div>
                </div>
                <div class="col-md-6">
                  <!-- Codeforces -->
                  <div class="mb-3">
                    <label for="codeforces" class="form-label">
                      <i class="bi bi-trophy me-1"></i>
                      Codeforces
                    </label>
                    <input
                      id="codeforces"
                      v-model="socialLinks.codeforces"
                      type="url"
                      class="form-control"
                      placeholder="https://codeforces.com/profile/username"
                    >
                  </div>
                </div>
                <div class="col-md-6">
                  <!-- AtCoder -->
                  <div class="mb-3">
                    <label for="atcoder" class="form-label">
                      <i class="bi bi-award me-1"></i>
                      AtCoder
                    </label>
                    <input
                      id="atcoder"
                      v-model="socialLinks.atcoder"
                      type="url"
                      class="form-control"
                      placeholder="https://atcoder.jp/users/username"
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="d-flex justify-content-end gap-3">
        <button 
          type="button" 
          @click="resetForm"
          class="btn btn-outline-secondary"
        >
          <i class="bi bi-arrow-clockwise me-2"></i>
          重置
        </button>
        <button 
          type="submit" 
          class="btn btn-primary"
          :disabled="updating"
        >
          <span v-if="updating" class="spinner-border spinner-border-sm me-2"></span>
          <i v-else class="bi bi-check-circle me-2"></i>
          {{ updating ? '保存中...' : '保存更改' }}
        </button>
      </div>
    </form>
  </div>
</template>

<script>
import { reactive, computed, watch } from 'vue'
import { useStore } from 'vuex'
import { showSuccess, showError } from '@/utils/message'

export default {
  name: 'EditProfileTab',
  props: {
    profile: {
      type: Object,
      required: true
    }
  },
  emits: ['updated'],
  setup(props, { emit }) {
    const store = useStore()

    const form = reactive({
      nickname: '',
      bio: '',
      gender: '',
      birthday: '',
      phone: '',
      location: '',
      website: '',
      company: '',
      position: '',
      github: ''
    })

    const socialLinks = reactive({
      leetcode: '',
      codeforces: '',
      atcoder: ''
    })

    const errors = reactive({})
    const updating = computed(() => store.getters['user/updating'])

    // 初始化表单数据
    const initForm = () => {
      form.nickname = props.profile.nickname || ''
      form.bio = props.profile.bio || ''
      form.gender = props.profile.gender || ''
      form.birthday = props.profile.birthday || ''
      form.phone = props.profile.phone || ''
      form.location = props.profile.location || ''
      form.website = props.profile.website || ''
      form.company = props.profile.company || ''
      form.position = props.profile.position || ''
      form.github = props.profile.github || ''

      // 社交链接
      if (props.profile.socialLinks) {
        socialLinks.leetcode = props.profile.socialLinks.leetcode || ''
        socialLinks.codeforces = props.profile.socialLinks.codeforces || ''
        socialLinks.atcoder = props.profile.socialLinks.atcoder || ''
      }
    }

    const validateForm = () => {
      Object.keys(errors).forEach(key => delete errors[key])

      if (!form.nickname.trim()) {
        errors.nickname = '请输入昵称'
      } else if (form.nickname.length > 50) {
        errors.nickname = '昵称不能超过50个字符'
      }

      if (form.bio.length > 200) {
        errors.bio = '个人简介不能超过200个字符'
      }

      return Object.keys(errors).length === 0
    }

    const handleSubmit = async () => {
      if (!validateForm()) return

      try {
        // 更新基本信息
        await store.dispatch('user/updateProfile', {
          nickname: form.nickname,
          bio: form.bio,
          gender: form.gender,
          birthday: form.birthday,
          phone: form.phone,
          location: form.location,
          website: form.website,
          company: form.company,
          position: form.position,
          github: form.github
        })

        // 更新社交链接
        await store.dispatch('user/updateSocialLinks', socialLinks)

        showSuccess('个人资料更新成功')
        emit('updated', props.profile)
      } catch (error) {
        showError(error.message || '更新失败')
      }
    }

    const resetForm = () => {
      initForm()
      Object.keys(errors).forEach(key => delete errors[key])
    }

    // 监听 profile 变化
    watch(() => props.profile, initForm, { immediate: true })

    return {
      form,
      socialLinks,
      errors,
      updating,
      handleSubmit,
      resetForm
    }
  }
}
</script>

<style scoped>
.form-check {
  padding: 0.5rem 0.75rem;
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
}

.form-check:hover {
  border-color: #007bff;
  background-color: #f8f9fa;
}

.form-check-input:checked + .form-check-label {
  color: #007bff;
  font-weight: 500;
}

.is-invalid {
  border-color: #dc3545;
}

.invalid-feedback {
  display: block;
}

.form-text {
  font-size: 0.875rem;
  color: #6c757d;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .d-flex.gap-3 {
    flex-direction: column;
  }
  
  .d-flex.gap-3 .btn {
    margin-bottom: 0.5rem;
  }
}
</style>

<template>
  <div class="activities-tab">
    <!-- 活动统计 -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="card border-0 shadow-sm">
          <div class="card-body">
            <div class="row text-center">
              <div class="col-md-3 col-6">
                <div class="activity-stat">
                  <div class="stat-number text-primary">{{ todayActivities }}</div>
                  <div class="stat-label">今日活动</div>
                </div>
              </div>
              <div class="col-md-3 col-6">
                <div class="activity-stat">
                  <div class="stat-number text-success">{{ weekActivities }}</div>
                  <div class="stat-label">本周活动</div>
                </div>
              </div>
              <div class="col-md-3 col-6">
                <div class="activity-stat">
                  <div class="stat-number text-warning">{{ monthActivities }}</div>
                  <div class="stat-label">本月活动</div>
                </div>
              </div>
              <div class="col-md-3 col-6">
                <div class="activity-stat">
                  <div class="stat-number text-info">{{ totalPoints }}</div>
                  <div class="stat-label">总积分</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 活动记录列表 -->
    <div class="card border-0 shadow-sm">
      <div class="card-header bg-transparent border-0">
        <div class="d-flex justify-content-between align-items-center">
          <h5 class="mb-0">
            <i class="bi bi-clock-history me-2"></i>
            最近活动
          </h5>
          <div class="activity-filters">
            <select v-model="filterType" class="form-select form-select-sm">
              <option value="">全部活动</option>
              <option value="SOLVE_PROBLEM">解决题目</option>
              <option value="CREATE_PROBLEM">创建题目</option>
              <option value="CREATE_PROBLEMSET">创建题集</option>
              <option value="LOGIN">登录系统</option>
            </select>
          </div>
        </div>
      </div>
      
      <div class="card-body">
        <!-- 加载状态 -->
        <div v-if="loading" class="text-center py-4">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
          </div>
          <p class="mt-2 text-muted">加载活动记录中...</p>
        </div>

        <!-- 活动列表 -->
        <div v-else-if="filteredActivities.length > 0" class="activities-list">
          <div 
            v-for="activity in filteredActivities" 
            :key="activity.id"
            class="activity-item"
          >
            <div class="activity-icon">
              <i :class="[getActivityIcon(activity.type), getActivityColor(activity.type)]"></i>
            </div>
            
            <div class="activity-content">
              <div class="activity-main">
                <span class="activity-description">{{ activity.description }}</span>
                <span v-if="activity.detail" class="activity-detail">{{ activity.detail }}</span>
              </div>
              
              <div class="activity-meta">
                <span class="activity-time">{{ formatActivityTime(activity.timestamp) }}</span>
                <span v-if="activity.points > 0" class="activity-points">
                  +{{ activity.points }} 积分
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-else class="text-center py-4">
          <i class="bi bi-clock-history text-muted" style="font-size: 3rem;"></i>
          <h6 class="mt-3 text-muted">暂无活动记录</h6>
          <p class="text-muted">开始使用平台功能，活动记录将在这里显示</p>
        </div>

        <!-- 分页 -->
        <div v-if="pagination.pages > 1" class="d-flex justify-content-center mt-4">
          <nav aria-label="活动记录分页">
            <ul class="pagination pagination-sm">
              <li class="page-item" :class="{ disabled: pagination.current === 1 }">
                <button 
                  @click="changePage(pagination.current - 1)"
                  class="page-link"
                  :disabled="pagination.current === 1"
                >
                  上一页
                </button>
              </li>
              
              <li 
                v-for="page in visiblePages" 
                :key="page"
                class="page-item"
                :class="{ active: page === pagination.current }"
              >
                <button 
                  v-if="page !== '...'"
                  @click="changePage(page)"
                  class="page-link"
                >
                  {{ page }}
                </button>
                <span v-else class="page-link">...</span>
              </li>
              
              <li class="page-item" :class="{ disabled: pagination.current === pagination.pages }">
                <button 
                  @click="changePage(pagination.current + 1)"
                  class="page-link"
                  :disabled="pagination.current === pagination.pages"
                >
                  下一页
                </button>
              </li>
            </ul>
          </nav>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { useStore } from 'vuex'
import { showError } from '@/utils/message'

export default {
  name: 'ActivitiesTab',
  setup() {
    const store = useStore()
    
    const filterType = ref('')

    const activities = computed(() => store.getters['user/activities'])
    const pagination = computed(() => store.getters['user/activitiesPagination'])
    const loading = computed(() => store.getters['user/loading'])

    const filteredActivities = computed(() => {
      if (!filterType.value) return activities.value
      return activities.value.filter(activity => activity.type === filterType.value)
    })

    // 统计数据
    const todayActivities = computed(() => {
      const today = new Date().toDateString()
      return activities.value.filter(activity => 
        new Date(activity.timestamp).toDateString() === today
      ).length
    })

    const weekActivities = computed(() => {
      const weekAgo = new Date()
      weekAgo.setDate(weekAgo.getDate() - 7)
      return activities.value.filter(activity => 
        new Date(activity.timestamp) >= weekAgo
      ).length
    })

    const monthActivities = computed(() => {
      const monthAgo = new Date()
      monthAgo.setMonth(monthAgo.getMonth() - 1)
      return activities.value.filter(activity => 
        new Date(activity.timestamp) >= monthAgo
      ).length
    })

    const totalPoints = computed(() => {
      return activities.value.reduce((total, activity) => total + (activity.points || 0), 0)
    })

    const visiblePages = computed(() => {
      const current = pagination.value.current
      const total = pagination.value.pages
      const delta = 2
      const range = []
      const rangeWithDots = []

      for (let i = Math.max(2, current - delta); i <= Math.min(total - 1, current + delta); i++) {
        range.push(i)
      }

      if (current - delta > 2) {
        rangeWithDots.push(1, '...')
      } else {
        rangeWithDots.push(1)
      }

      rangeWithDots.push(...range)

      if (current + delta < total - 1) {
        rangeWithDots.push('...', total)
      } else {
        rangeWithDots.push(total)
      }

      return rangeWithDots.filter((item, index, arr) => arr.indexOf(item) === index && item !== 1 || index === 0)
    })

    const getActivityIcon = (type) => {
      const icons = {
        'SOLVE_PROBLEM': 'bi-check-circle',
        'CREATE_PROBLEM': 'bi-plus-circle',
        'CREATE_PROBLEMSET': 'bi-collection',
        'LOGIN': 'bi-box-arrow-in-right',
        'UPDATE_PROFILE': 'bi-person-gear'
      }
      return icons[type] || 'bi-circle'
    }

    const getActivityColor = (type) => {
      const colors = {
        'SOLVE_PROBLEM': 'text-success',
        'CREATE_PROBLEM': 'text-primary',
        'CREATE_PROBLEMSET': 'text-warning',
        'LOGIN': 'text-info',
        'UPDATE_PROFILE': 'text-secondary'
      }
      return colors[type] || 'text-muted'
    }

    const formatActivityTime = (timestamp) => {
      const date = new Date(timestamp)
      const now = new Date()
      const diffTime = Math.abs(now - date)
      const diffMinutes = Math.ceil(diffTime / (1000 * 60))
      const diffHours = Math.ceil(diffTime / (1000 * 60 * 60))
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

      if (diffMinutes < 60) {
        return `${diffMinutes} 分钟前`
      } else if (diffHours < 24) {
        return `${diffHours} 小时前`
      } else if (diffDays < 7) {
        return `${diffDays} 天前`
      } else {
        return date.toLocaleDateString('zh-CN')
      }
    }

    const fetchActivities = async (page = 1) => {
      try {
        await store.dispatch('user/fetchActivities', { current: page, size: 10 })
      } catch (error) {
        showError(error.message || '获取活动记录失败')
      }
    }

    const changePage = (page) => {
      if (page < 1 || page > pagination.value.pages) return
      fetchActivities(page)
    }

    // 监听筛选类型变化
    watch(filterType, () => {
      // 筛选是在前端进行的，不需要重新请求
    })

    onMounted(() => {
      fetchActivities()
    })

    return {
      filterType,
      activities,
      filteredActivities,
      pagination,
      loading,
      todayActivities,
      weekActivities,
      monthActivities,
      totalPoints,
      visiblePages,
      getActivityIcon,
      getActivityColor,
      formatActivityTime,
      changePage
    }
  }
}
</script>

<style scoped>
.activity-stat {
  padding: 1rem 0;
}

.activity-stat .stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.activity-stat .stat-label {
  font-size: 0.875rem;
  color: #6c757d;
}

.activity-filters .form-select {
  width: auto;
  min-width: 120px;
}

.activities-list {
  max-height: 600px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  padding: 1rem 0;
  border-bottom: 1px solid #f8f9fa;
  transition: background-color 0.2s ease;
}

.activity-item:hover {
  background-color: #f8f9fa;
  margin: 0 -1rem;
  padding-left: 1rem;
  padding-right: 1rem;
  border-radius: 0.5rem;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  flex-shrink: 0;
}

.activity-icon i {
  font-size: 1.1rem;
}

.activity-content {
  flex: 1;
  min-width: 0;
}

.activity-main {
  margin-bottom: 0.25rem;
}

.activity-description {
  font-weight: 500;
  color: #2c3e50;
}

.activity-detail {
  color: #007bff;
  margin-left: 0.5rem;
  font-weight: 500;
}

.activity-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.activity-time {
  font-size: 0.875rem;
  color: #6c757d;
}

.activity-points {
  font-size: 0.875rem;
  color: #28a745;
  font-weight: 500;
}

.pagination .page-link {
  border-radius: 0.375rem;
  margin: 0 2px;
  border: 1px solid #dee2e6;
}

.pagination .page-item.active .page-link {
  background-color: #007bff;
  border-color: #007bff;
}

/* 滚动条样式 */
.activities-list::-webkit-scrollbar {
  width: 6px;
}

.activities-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.activities-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.activities-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .activity-item {
    padding: 0.75rem 0;
  }
  
  .activity-icon {
    width: 36px;
    height: 36px;
    margin-right: 0.75rem;
  }
  
  .activity-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
  
  .activity-stat .stat-number {
    font-size: 1.25rem;
  }
}
</style>

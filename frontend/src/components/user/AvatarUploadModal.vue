<template>
  <!-- 头像上传模态框 -->
  <div 
    class="modal fade" 
    id="avatarUploadModal" 
    tabindex="-1" 
    aria-labelledby="avatarUploadModalLabel" 
    aria-hidden="true"
    data-bs-backdrop="static"
  >
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="avatarUploadModalLabel">
            <i class="bi bi-camera me-2"></i>
            更换头像
          </h5>
          <button 
            type="button" 
            class="btn-close" 
            @click="closeModal"
            aria-label="Close"
          ></button>
        </div>
        
        <div class="modal-body">
          <!-- 当前头像 -->
          <div class="current-avatar mb-4">
            <div class="text-center">
              <img 
                :src="currentAvatar" 
                alt="当前头像"
                class="current-avatar-img"
              >
              <p class="mt-2 text-muted">当前头像</p>
            </div>
          </div>

          <!-- 上传方式选择 -->
          <div class="upload-methods">
            <div class="row g-3">
              <!-- 文件上传 -->
              <div class="col-md-6">
                <div class="upload-method" :class="{ active: uploadMethod === 'file' }">
                  <input
                    id="upload-file"
                    v-model="uploadMethod"
                    type="radio"
                    value="file"
                    class="method-radio"
                    name="uploadMethod"
                  >
                  <label for="upload-file" class="method-label">
                    <i class="bi bi-cloud-upload method-icon"></i>
                    <div class="method-title">上传图片</div>
                    <div class="method-description">从本地选择图片文件</div>
                  </label>
                </div>
              </div>

              <!-- 随机头像 -->
              <div class="col-md-6">
                <div class="upload-method" :class="{ active: uploadMethod === 'random' }">
                  <input
                    id="upload-random"
                    v-model="uploadMethod"
                    type="radio"
                    value="random"
                    class="method-radio"
                    name="uploadMethod"
                  >
                  <label for="upload-random" class="method-label">
                    <i class="bi bi-shuffle method-icon"></i>
                    <div class="method-title">随机头像</div>
                    <div class="method-description">生成随机卡通头像</div>
                  </label>
                </div>
              </div>
            </div>
          </div>

          <!-- 文件上传区域 -->
          <div v-if="uploadMethod === 'file'" class="file-upload-area mt-4">
            <div 
              class="upload-dropzone"
              :class="{ 'dragover': isDragOver }"
              @drop="handleDrop"
              @dragover.prevent="isDragOver = true"
              @dragleave="isDragOver = false"
              @click="triggerFileInput"
            >
              <input
                ref="fileInput"
                type="file"
                accept="image/*"
                class="file-input"
                @change="handleFileSelect"
              >
              
              <div v-if="!selectedFile" class="upload-placeholder">
                <i class="bi bi-cloud-upload upload-icon"></i>
                <p class="upload-text">点击选择图片或拖拽到此处</p>
                <p class="upload-hint">支持 JPG、PNG、GIF 格式，最大 5MB</p>
              </div>
              
              <div v-else class="file-preview">
                <img :src="previewUrl" alt="预览" class="preview-image">
                <div class="file-info">
                  <p class="file-name">{{ selectedFile.name }}</p>
                  <p class="file-size">{{ formatFileSize(selectedFile.size) }}</p>
                </div>
                <button 
                  @click.stop="clearFile"
                  class="btn btn-sm btn-outline-danger"
                >
                  <i class="bi bi-trash"></i>
                </button>
              </div>
            </div>
          </div>

          <!-- 随机头像选择 -->
          <div v-if="uploadMethod === 'random'" class="random-avatars mt-4">
            <div class="avatars-grid">
              <div 
                v-for="(avatar, index) in randomAvatars" 
                :key="index"
                class="avatar-option"
                :class="{ selected: selectedRandomAvatar === avatar }"
                @click="selectRandomAvatar(avatar)"
              >
                <img :src="avatar" :alt="`随机头像 ${index + 1}`" class="avatar-option-img">
              </div>
            </div>
            <div class="text-center mt-3">
              <button 
                @click="generateRandomAvatars"
                class="btn btn-outline-primary btn-sm"
              >
                <i class="bi bi-arrow-clockwise me-1"></i>
                换一批
              </button>
            </div>
          </div>
        </div>
        
        <div class="modal-footer">
          <button 
            type="button" 
            class="btn btn-secondary" 
            @click="closeModal"
          >
            取消
          </button>
          <button 
            type="button" 
            class="btn btn-primary" 
            :disabled="!canUpload || uploading"
            @click="handleUpload"
          >
            <span v-if="uploading" class="spinner-border spinner-border-sm me-2"></span>
            <i v-else class="bi bi-check-circle me-2"></i>
            {{ uploading ? '上传中...' : '确认更换' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useStore } from 'vuex'
import { showSuccess, showError } from '@/utils/message'

export default {
  name: 'AvatarUploadModal',
  props: {
    currentAvatar: {
      type: String,
      default: ''
    }
  },
  emits: ['uploaded', 'close'],
  setup(props, { emit }) {
    const store = useStore()
    
    const uploadMethod = ref('file')
    const selectedFile = ref(null)
    const previewUrl = ref('')
    const isDragOver = ref(false)
    const uploading = ref(false)
    const fileInput = ref(null)
    const selectedRandomAvatar = ref('')
    const randomAvatars = ref([])

    const canUpload = computed(() => {
      return (uploadMethod.value === 'file' && selectedFile.value) ||
             (uploadMethod.value === 'random' && selectedRandomAvatar.value)
    })

    const generateRandomAvatars = () => {
      randomAvatars.value = []
      for (let i = 0; i < 8; i++) {
        const seed = Math.random().toString(36).substring(7)
        randomAvatars.value.push(`https://api.dicebear.com/7.x/avataaars/svg?seed=${seed}`)
      }
    }

    const triggerFileInput = () => {
      fileInput.value?.click()
    }

    const handleFileSelect = (event) => {
      const file = event.target.files[0]
      if (file) {
        validateAndSetFile(file)
      }
    }

    const handleDrop = (event) => {
      event.preventDefault()
      isDragOver.value = false
      
      const file = event.dataTransfer.files[0]
      if (file) {
        validateAndSetFile(file)
      }
    }

    const validateAndSetFile = (file) => {
      // 验证文件类型
      if (!file.type.startsWith('image/')) {
        showError('请选择图片文件')
        return
      }

      // 验证文件大小 (5MB)
      if (file.size > 5 * 1024 * 1024) {
        showError('图片大小不能超过 5MB')
        return
      }

      selectedFile.value = file
      
      // 生成预览
      const reader = new FileReader()
      reader.onload = (e) => {
        previewUrl.value = e.target.result
      }
      reader.readAsDataURL(file)
    }

    const clearFile = () => {
      selectedFile.value = null
      previewUrl.value = ''
      if (fileInput.value) {
        fileInput.value.value = ''
      }
    }

    const selectRandomAvatar = (avatar) => {
      selectedRandomAvatar.value = avatar
    }

    const formatFileSize = (bytes) => {
      if (bytes === 0) return '0 Bytes'
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    const handleUpload = async () => {
      uploading.value = true
      
      try {
        let avatarUrl = ''
        
        if (uploadMethod.value === 'file' && selectedFile.value) {
          // 模拟文件上传
          const formData = new FormData()
          formData.append('avatar', selectedFile.value)
          
          const response = await store.dispatch('user/uploadAvatar', formData)
          avatarUrl = response
        } else if (uploadMethod.value === 'random' && selectedRandomAvatar.value) {
          // 使用选中的随机头像
          avatarUrl = selectedRandomAvatar.value
          
          // 模拟上传随机头像
          await store.dispatch('user/uploadAvatar', new FormData())
          store.commit('user/UPDATE_AVATAR', avatarUrl)
        }

        showSuccess('头像更换成功')
        emit('uploaded', avatarUrl)
        closeModal()
      } catch (error) {
        showError(error.message || '头像上传失败')
      } finally {
        uploading.value = false
      }
    }

    const closeModal = () => {
      // 重置状态
      uploadMethod.value = 'file'
      clearFile()
      selectedRandomAvatar.value = ''
      isDragOver.value = false
      
      emit('close')
    }

    onMounted(() => {
      generateRandomAvatars()
      
      // 显示模态框
      const modal = new window.bootstrap.Modal(document.getElementById('avatarUploadModal'))
      modal.show()
      
      // 监听模态框关闭事件
      document.getElementById('avatarUploadModal').addEventListener('hidden.bs.modal', closeModal)
    })

    onUnmounted(() => {
      document.getElementById('avatarUploadModal')?.removeEventListener('hidden.bs.modal', closeModal)
    })

    return {
      uploadMethod,
      selectedFile,
      previewUrl,
      isDragOver,
      uploading,
      fileInput,
      selectedRandomAvatar,
      randomAvatars,
      canUpload,
      generateRandomAvatars,
      triggerFileInput,
      handleFileSelect,
      handleDrop,
      clearFile,
      selectRandomAvatar,
      formatFileSize,
      handleUpload,
      closeModal
    }
  }
}
</script>

<style scoped>
.current-avatar-img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #e9ecef;
}

.upload-methods {
  margin-bottom: 1rem;
}

.upload-method {
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
}

.upload-method.active {
  transform: scale(1.02);
}

.method-radio {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}

.method-label {
  display: block;
  padding: 1.5rem;
  border: 2px solid #e9ecef;
  border-radius: 0.75rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.method-label:hover {
  border-color: #007bff;
  background-color: #f8f9fa;
}

.upload-method.active .method-label {
  border-color: #007bff;
  background-color: #e7f3ff;
}

.method-icon {
  font-size: 2rem;
  color: #007bff;
  margin-bottom: 0.5rem;
  display: block;
}

.method-title {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.method-description {
  font-size: 0.875rem;
  color: #6c757d;
}

.upload-dropzone {
  border: 2px dashed #dee2e6;
  border-radius: 0.75rem;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.upload-dropzone:hover,
.upload-dropzone.dragover {
  border-color: #007bff;
  background-color: #f8f9fa;
}

.file-input {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}

.upload-icon {
  font-size: 3rem;
  color: #007bff;
  margin-bottom: 1rem;
  display: block;
}

.upload-text {
  font-size: 1.1rem;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.upload-hint {
  font-size: 0.875rem;
  color: #6c757d;
}

.file-preview {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 0.5rem;
}

.preview-image {
  width: 60px;
  height: 60px;
  border-radius: 0.5rem;
  object-fit: cover;
}

.file-info {
  flex: 1;
  text-align: left;
}

.file-name {
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.file-size {
  font-size: 0.875rem;
  color: #6c757d;
  margin: 0;
}

.avatars-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
  margin-bottom: 1rem;
}

.avatar-option {
  position: relative;
  cursor: pointer;
  border-radius: 0.5rem;
  overflow: hidden;
  transition: all 0.2s ease;
  border: 3px solid transparent;
}

.avatar-option:hover {
  transform: scale(1.05);
}

.avatar-option.selected {
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.avatar-option-img {
  width: 100%;
  height: 80px;
  object-fit: cover;
  display: block;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .method-label {
    padding: 1rem;
  }
  
  .method-icon {
    font-size: 1.5rem;
  }
  
  .upload-dropzone {
    padding: 1.5rem;
  }
  
  .upload-icon {
    font-size: 2rem;
  }
  
  .avatars-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .avatar-option-img {
    height: 60px;
  }
}
</style>

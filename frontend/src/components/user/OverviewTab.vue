<template>
  <div class="overview-tab">
    <!-- 统计卡片 -->
    <div class="row g-4 mb-4">
      <div class="col-md-3 col-sm-6">
        <div class="stat-card">
          <div class="stat-icon bg-primary">
            <i class="bi bi-puzzle"></i>
          </div>
          <div class="stat-content">
            <h3>{{ profile.stats.problemsSolved }}</h3>
            <p>已解决题目</p>
          </div>
        </div>
      </div>
      
      <div class="col-md-3 col-sm-6">
        <div class="stat-card">
          <div class="stat-icon bg-success">
            <i class="bi bi-check-circle"></i>
          </div>
          <div class="stat-content">
            <h3>{{ profile.stats.acceptanceRate }}%</h3>
            <p>通过率</p>
          </div>
        </div>
      </div>
      
      <div class="col-md-3 col-sm-6">
        <div class="stat-card">
          <div class="stat-icon bg-warning">
            <i class="bi bi-collection"></i>
          </div>
          <div class="stat-content">
            <h3>{{ profile.stats.problemSetsCreated }}</h3>
            <p>创建题集</p>
          </div>
        </div>
      </div>
      
      <div class="col-md-3 col-sm-6">
        <div class="stat-card">
          <div class="stat-icon bg-info">
            <i class="bi bi-fire"></i>
          </div>
          <div class="stat-content">
            <h3>{{ profile.stats.streak }}</h3>
            <p>连续天数</p>
          </div>
        </div>
      </div>
    </div>

    <div class="row">
      <!-- 解题统计图表 -->
      <div class="col-lg-8 mb-4">
        <div class="card border-0 shadow-sm">
          <div class="card-header bg-transparent border-0">
            <h5 class="mb-0">
              <i class="bi bi-bar-chart me-2"></i>
              解题统计
            </h5>
          </div>
          <div class="card-body">
            <!-- 提交统计 -->
            <div class="submission-stats mb-4">
              <div class="row text-center">
                <div class="col-4">
                  <div class="stat-number text-primary">{{ profile.stats.totalSubmissions }}</div>
                  <div class="stat-label">总提交</div>
                </div>
                <div class="col-4">
                  <div class="stat-number text-success">{{ profile.stats.acceptedSubmissions }}</div>
                  <div class="stat-label">通过提交</div>
                </div>
                <div class="col-4">
                  <div class="stat-number text-danger">{{ profile.stats.totalSubmissions - profile.stats.acceptedSubmissions }}</div>
                  <div class="stat-label">失败提交</div>
                </div>
              </div>
            </div>

            <!-- 通过率环形图 -->
            <div class="acceptance-chart">
              <div class="chart-container">
                <div class="progress-ring">
                  <svg width="120" height="120">
                    <circle
                      cx="60"
                      cy="60"
                      r="50"
                      stroke="#e9ecef"
                      stroke-width="8"
                      fill="transparent"
                    />
                    <circle
                      cx="60"
                      cy="60"
                      r="50"
                      stroke="#28a745"
                      stroke-width="8"
                      fill="transparent"
                      :stroke-dasharray="circumference"
                      :stroke-dashoffset="strokeDashoffset"
                      transform="rotate(-90 60 60)"
                      class="progress-circle"
                    />
                  </svg>
                  <div class="chart-center">
                    <div class="percentage">{{ profile.stats.acceptanceRate }}%</div>
                    <div class="label">通过率</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 排名和积分 -->
      <div class="col-lg-4 mb-4">
        <div class="card border-0 shadow-sm">
          <div class="card-header bg-transparent border-0">
            <h5 class="mb-0">
              <i class="bi bi-trophy me-2"></i>
              排名信息
            </h5>
          </div>
          <div class="card-body">
            <div class="ranking-info">
              <div class="rank-item">
                <div class="rank-label">全站排名</div>
                <div class="rank-value">#{{ profile.stats.ranking }}</div>
              </div>
              
              <div class="rank-item">
                <div class="rank-label">总积分</div>
                <div class="rank-value">{{ profile.stats.points }}</div>
              </div>
              
              <div class="rank-item">
                <div class="rank-label">登录次数</div>
                <div class="rank-value">{{ profile.loginCount }}</div>
              </div>
              
              <div class="rank-item">
                <div class="rank-label">最后登录</div>
                <div class="rank-value small">{{ formatLastLogin(profile.lastLoginTime) }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 社交链接 -->
        <div class="card border-0 shadow-sm mt-4">
          <div class="card-header bg-transparent border-0">
            <h5 class="mb-0">
              <i class="bi bi-link-45deg me-2"></i>
              社交链接
            </h5>
          </div>
          <div class="card-body">
            <div class="social-links">
              <a 
                v-if="profile.socialLinks.github"
                :href="profile.socialLinks.github"
                target="_blank"
                class="social-link"
              >
                <i class="bi bi-github"></i>
                <span>GitHub</span>
              </a>
              
              <a 
                v-if="profile.socialLinks.leetcode"
                :href="profile.socialLinks.leetcode"
                target="_blank"
                class="social-link"
              >
                <i class="bi bi-code-square"></i>
                <span>LeetCode</span>
              </a>
              
              <a 
                v-if="profile.socialLinks.codeforces"
                :href="profile.socialLinks.codeforces"
                target="_blank"
                class="social-link"
              >
                <i class="bi bi-trophy"></i>
                <span>Codeforces</span>
              </a>
              
              <a 
                v-if="profile.website"
                :href="profile.website"
                target="_blank"
                class="social-link"
              >
                <i class="bi bi-globe"></i>
                <span>个人网站</span>
              </a>
              
              <div v-if="!hasSocialLinks" class="text-center text-muted">
                <i class="bi bi-link-45deg"></i>
                <p class="mb-0 mt-2">暂无社交链接</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'

export default {
  name: 'OverviewTab',
  props: {
    profile: {
      type: Object,
      required: true
    }
  },
  setup(props) {
    const circumference = 2 * Math.PI * 50 // r = 50
    
    const strokeDashoffset = computed(() => {
      const progress = props.profile.stats.acceptanceRate / 100
      return circumference * (1 - progress)
    })

    const hasSocialLinks = computed(() => {
      const links = props.profile.socialLinks
      return links.github || links.leetcode || links.codeforces || props.profile.website
    })

    const formatLastLogin = (dateString) => {
      if (!dateString) return '-'
      const date = new Date(dateString)
      const now = new Date()
      const diffTime = Math.abs(now - date)
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
      
      if (diffDays === 1) return '今天'
      if (diffDays === 2) return '昨天'
      if (diffDays <= 7) return `${diffDays - 1} 天前`
      
      return date.toLocaleDateString('zh-CN')
    }

    return {
      circumference,
      strokeDashoffset,
      hasSocialLinks,
      formatLastLogin
    }
  }
}
</script>

<style scoped>
.stat-card {
  background: white;
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  color: white;
  font-size: 1.5rem;
}

.stat-content h3 {
  margin: 0;
  font-size: 2rem;
  font-weight: 700;
  color: #2c3e50;
}

.stat-content p {
  margin: 0;
  color: #6c757d;
  font-size: 0.9rem;
}

.submission-stats .stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.submission-stats .stat-label {
  font-size: 0.875rem;
  color: #6c757d;
}

.acceptance-chart {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
}

.chart-container {
  position: relative;
}

.progress-ring {
  position: relative;
}

.progress-circle {
  transition: stroke-dashoffset 0.5s ease-in-out;
}

.chart-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.chart-center .percentage {
  font-size: 1.25rem;
  font-weight: 700;
  color: #28a745;
}

.chart-center .label {
  font-size: 0.75rem;
  color: #6c757d;
}

.ranking-info {
  space-y: 1rem;
}

.rank-item {
  display: flex;
  justify-content: between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #f8f9fa;
}

.rank-item:last-child {
  border-bottom: none;
}

.rank-label {
  color: #6c757d;
  font-size: 0.9rem;
}

.rank-value {
  font-weight: 600;
  color: #2c3e50;
}

.social-links {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.social-link {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  border: 1px solid #e9ecef;
  border-radius: 0.5rem;
  text-decoration: none;
  color: #495057;
  transition: all 0.2s ease;
}

.social-link:hover {
  border-color: #007bff;
  background-color: #f8f9fa;
  color: #007bff;
  transform: translateX(4px);
}

.social-link i {
  margin-right: 0.75rem;
  font-size: 1.1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stat-card {
    padding: 1rem;
  }
  
  .stat-icon {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }
  
  .stat-content h3 {
    font-size: 1.5rem;
  }
  
  .chart-center .percentage {
    font-size: 1rem;
  }
}
</style>

<template>
  <div class="settings-tab">
    <form @submit.prevent="handleSubmit">
      <div class="row">
        <!-- 外观设置 -->
        <div class="col-lg-6">
          <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-transparent border-0">
              <h5 class="mb-0">
                <i class="bi bi-palette me-2"></i>
                外观设置
              </h5>
            </div>
            <div class="card-body">
              <!-- 主题设置 -->
              <div class="mb-4">
                <label class="form-label fw-medium">主题模式</label>
                <div class="theme-options">
                  <div class="row g-3">
                    <div class="col-4">
                      <div class="theme-option" :class="{ active: preferences.theme === 'light' }">
                        <input
                          id="theme-light"
                          v-model="preferences.theme"
                          type="radio"
                          value="light"
                          class="theme-radio"
                          name="theme"
                        >
                        <label for="theme-light" class="theme-label">
                          <div class="theme-preview light">
                            <div class="theme-header"></div>
                            <div class="theme-content">
                              <div class="theme-sidebar"></div>
                              <div class="theme-main"></div>
                            </div>
                          </div>
                          <span class="theme-name">浅色</span>
                        </label>
                      </div>
                    </div>
                    
                    <div class="col-4">
                      <div class="theme-option" :class="{ active: preferences.theme === 'dark' }">
                        <input
                          id="theme-dark"
                          v-model="preferences.theme"
                          type="radio"
                          value="dark"
                          class="theme-radio"
                          name="theme"
                        >
                        <label for="theme-dark" class="theme-label">
                          <div class="theme-preview dark">
                            <div class="theme-header"></div>
                            <div class="theme-content">
                              <div class="theme-sidebar"></div>
                              <div class="theme-main"></div>
                            </div>
                          </div>
                          <span class="theme-name">深色</span>
                        </label>
                      </div>
                    </div>
                    
                    <div class="col-4">
                      <div class="theme-option" :class="{ active: preferences.theme === 'auto' }">
                        <input
                          id="theme-auto"
                          v-model="preferences.theme"
                          type="radio"
                          value="auto"
                          class="theme-radio"
                          name="theme"
                        >
                        <label for="theme-auto" class="theme-label">
                          <div class="theme-preview auto">
                            <div class="theme-header"></div>
                            <div class="theme-content">
                              <div class="theme-sidebar"></div>
                              <div class="theme-main"></div>
                            </div>
                          </div>
                          <span class="theme-name">自动</span>
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 语言设置 -->
              <div class="mb-3">
                <label for="language" class="form-label fw-medium">界面语言</label>
                <select
                  id="language"
                  v-model="preferences.language"
                  class="form-select"
                >
                  <option value="zh-CN">简体中文</option>
                  <option value="zh-TW">繁體中文</option>
                  <option value="en-US">English</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        <!-- 通知设置 -->
        <div class="col-lg-6">
          <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-transparent border-0">
              <h5 class="mb-0">
                <i class="bi bi-bell me-2"></i>
                通知设置
              </h5>
            </div>
            <div class="card-body">
              <!-- 邮件通知 -->
              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-title">邮件通知</div>
                  <div class="setting-description">接收重要活动和更新的邮件通知</div>
                </div>
                <div class="setting-control">
                  <div class="form-check form-switch">
                    <input
                      id="emailNotifications"
                      v-model="preferences.emailNotifications"
                      type="checkbox"
                      class="form-check-input"
                      role="switch"
                    >
                    <label for="emailNotifications" class="form-check-label"></label>
                  </div>
                </div>
              </div>

              <!-- 浏览器通知 -->
              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-title">浏览器通知</div>
                  <div class="setting-description">在浏览器中显示实时通知</div>
                </div>
                <div class="setting-control">
                  <div class="form-check form-switch">
                    <input
                      id="browserNotifications"
                      v-model="preferences.browserNotifications"
                      type="checkbox"
                      class="form-check-input"
                      role="switch"
                    >
                    <label for="browserNotifications" class="form-check-label"></label>
                  </div>
                </div>
              </div>

              <!-- 周报 -->
              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-title">周报推送</div>
                  <div class="setting-description">每周接收学习进度和统计报告</div>
                </div>
                <div class="setting-control">
                  <div class="form-check form-switch">
                    <input
                      id="weeklyReport"
                      v-model="preferences.weeklyReport"
                      type="checkbox"
                      class="form-check-input"
                      role="switch"
                    >
                    <label for="weeklyReport" class="form-check-label"></label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 隐私设置 -->
        <div class="col-12">
          <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-transparent border-0">
              <h5 class="mb-0">
                <i class="bi bi-shield-lock me-2"></i>
                隐私设置
              </h5>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-6">
                  <!-- 公开资料 -->
                  <div class="setting-item">
                    <div class="setting-info">
                      <div class="setting-title">公开个人资料</div>
                      <div class="setting-description">允许其他用户查看你的个人资料</div>
                    </div>
                    <div class="setting-control">
                      <div class="form-check form-switch">
                        <input
                          id="publicProfile"
                          v-model="preferences.publicProfile"
                          type="checkbox"
                          class="form-check-input"
                          role="switch"
                        >
                        <label for="publicProfile" class="form-check-label"></label>
                      </div>
                    </div>
                  </div>

                  <!-- 显示邮箱 -->
                  <div class="setting-item">
                    <div class="setting-info">
                      <div class="setting-title">显示邮箱地址</div>
                      <div class="setting-description">在个人资料中显示邮箱地址</div>
                    </div>
                    <div class="setting-control">
                      <div class="form-check form-switch">
                        <input
                          id="showEmail"
                          v-model="preferences.showEmail"
                          type="checkbox"
                          class="form-check-input"
                          role="switch"
                        >
                        <label for="showEmail" class="form-check-label"></label>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="col-md-6">
                  <!-- 显示统计 -->
                  <div class="setting-item">
                    <div class="setting-info">
                      <div class="setting-title">显示统计数据</div>
                      <div class="setting-description">在个人资料中显示解题统计</div>
                    </div>
                    <div class="setting-control">
                      <div class="form-check form-switch">
                        <input
                          id="showStats"
                          v-model="preferences.showStats"
                          type="checkbox"
                          class="form-check-input"
                          role="switch"
                        >
                        <label for="showStats" class="form-check-label"></label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="d-flex justify-content-end gap-3">
        <button 
          type="button" 
          @click="resetSettings"
          class="btn btn-outline-secondary"
        >
          <i class="bi bi-arrow-clockwise me-2"></i>
          重置
        </button>
        <button 
          type="submit" 
          class="btn btn-primary"
          :disabled="updating"
        >
          <span v-if="updating" class="spinner-border spinner-border-sm me-2"></span>
          <i v-else class="bi bi-check-circle me-2"></i>
          {{ updating ? '保存中...' : '保存设置' }}
        </button>
      </div>
    </form>
  </div>
</template>

<script>
import { reactive, computed, watch } from 'vue'
import { useStore } from 'vuex'
import { showSuccess, showError } from '@/utils/message'

export default {
  name: 'SettingsTab',
  props: {
    profile: {
      type: Object,
      required: true
    }
  },
  emits: ['updated'],
  setup(props, { emit }) {
    const store = useStore()

    const preferences = reactive({
      theme: 'light',
      language: 'zh-CN',
      emailNotifications: true,
      browserNotifications: false,
      weeklyReport: true,
      publicProfile: true,
      showEmail: false,
      showStats: true
    })

    const updating = computed(() => store.getters['user/updating'])

    // 初始化设置
    const initSettings = () => {
      if (props.profile.preferences) {
        Object.assign(preferences, props.profile.preferences)
      }
    }

    const handleSubmit = async () => {
      try {
        await store.dispatch('user/updatePreferences', preferences)
        showSuccess('偏好设置更新成功')
        emit('updated', preferences)
      } catch (error) {
        showError(error.message || '更新失败')
      }
    }

    const resetSettings = () => {
      initSettings()
    }

    // 监听 profile 变化
    watch(() => props.profile, initSettings, { immediate: true })

    return {
      preferences,
      updating,
      handleSubmit,
      resetSettings
    }
  }
}
</script>

<style scoped>
.theme-options {
  margin-top: 0.5rem;
}

.theme-option {
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
}

.theme-option.active {
  transform: scale(1.05);
}

.theme-radio {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}

.theme-label {
  display: block;
  cursor: pointer;
  text-align: center;
}

.theme-preview {
  width: 100%;
  height: 60px;
  border-radius: 0.5rem;
  border: 2px solid #dee2e6;
  overflow: hidden;
  margin-bottom: 0.5rem;
  transition: all 0.2s ease;
}

.theme-option.active .theme-preview {
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.theme-preview.light {
  background: #ffffff;
}

.theme-preview.dark {
  background: #2c3e50;
}

.theme-preview.auto {
  background: linear-gradient(90deg, #ffffff 50%, #2c3e50 50%);
}

.theme-header {
  height: 12px;
  background: rgba(0, 123, 255, 0.1);
}

.theme-content {
  display: flex;
  height: 48px;
}

.theme-sidebar {
  width: 30%;
  background: rgba(0, 123, 255, 0.05);
}

.theme-main {
  flex: 1;
  background: rgba(0, 123, 255, 0.02);
}

.theme-preview.dark .theme-header {
  background: rgba(255, 255, 255, 0.1);
}

.theme-preview.dark .theme-sidebar {
  background: rgba(255, 255, 255, 0.05);
}

.theme-preview.dark .theme-main {
  background: rgba(255, 255, 255, 0.02);
}

.theme-name {
  font-size: 0.875rem;
  color: #6c757d;
  font-weight: 500;
}

.theme-option.active .theme-name {
  color: #007bff;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  border-bottom: 1px solid #f8f9fa;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-info {
  flex: 1;
}

.setting-title {
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.setting-description {
  font-size: 0.875rem;
  color: #6c757d;
}

.setting-control {
  margin-left: 1rem;
}

.form-check-input:checked {
  background-color: #007bff;
  border-color: #007bff;
}

.form-check-input:focus {
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .theme-preview {
    height: 50px;
  }
  
  .setting-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .setting-control {
    margin-left: 0;
    align-self: flex-end;
  }
  
  .d-flex.gap-3 {
    flex-direction: column;
  }
  
  .d-flex.gap-3 .btn {
    margin-bottom: 0.5rem;
  }
}
</style>

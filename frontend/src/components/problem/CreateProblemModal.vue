<template>
  <div class="apple-modal-mask" @click.self="$emit('close')">
    <div class="apple-modal-card">
      <div class="apple-modal-header">
        <span>新增题目</span>
        <button class="apple-modal-close" @click="$emit('close')">
          <i class="bi bi-x-lg"></i>
        </button>
      </div>
      <form @submit.prevent="handleSubmit">
        <div class="form-row">
          <label for="title" class="form-label">题目标题<span class="text-danger">*</span></label>
          <input id="title" v-model="form.title" type="text" class="apple-input" :class="{ 'is-invalid': errors.title }" placeholder="请输入题目标题" maxlength="100" required>
        </div>
        <div v-if="errors.title" class="apple-error">{{ errors.title }}</div>

        <div class="form-row">
          <label for="link" class="form-label">题目链接<span class="text-danger">*</span></label>
          <input id="link" v-model="form.link" type="url" class="apple-input" :class="{ 'is-invalid': errors.link }" placeholder="请输入题目原始链接" required>
        </div>
        <div v-if="errors.link" class="apple-error">{{ errors.link }}</div>

        <div class="form-row">
          <label for="source" class="form-label">题目来源<span class="text-danger">*</span></label>
          <select id="source" v-model="form.source" class="apple-input" required>
            <option value="">请选择</option>
            <option value="LeetCode">LeetCode</option>
            <option value="牛客网">牛客网</option>
            <option value="洛谷">洛谷</option>
            <option value="Codeforces">Codeforces</option>
            <option value="自创">自创</option>
            <option value="其他">其他</option>
          </select>
        </div>
        <div v-if="errors.source" class="apple-error">{{ errors.source }}</div>

        <div class="form-row">
          <label for="image" class="form-label">题目图片</label>
          <input id="image" v-model="form.image" type="url" class="apple-input" placeholder="图片链接（可选）">
        </div>

        <div class="form-row">
          <label class="form-label">题目难度<span class="text-danger">*</span></label>
          <div class="apple-radio-group">
            <label class="apple-radio">
              <input type="radio" v-model="form.difficulty" value="EASY" required>
              <span class="apple-radio-label easy">简单</span>
            </label>
            <label class="apple-radio">
              <input type="radio" v-model="form.difficulty" value="MEDIUM">
              <span class="apple-radio-label medium">中等</span>
            </label>
            <label class="apple-radio">
              <input type="radio" v-model="form.difficulty" value="HARD">
              <span class="apple-radio-label hard">困难</span>
            </label>
          </div>
        </div>
        <div v-if="errors.difficulty" class="apple-error">{{ errors.difficulty }}</div>

        <div class="form-row">
          <label for="problemSet" class="form-label">归属题集<span class="text-danger">*</span></label>
          <select id="problemSet" v-model="form.problemSetId" class="apple-input" required>
            <option value="">请选择题集</option>
            <option v-for="ps in availableProblemSets" :key="ps.id" :value="ps.id">{{ ps.name }}</option>
          </select>
        </div>
        <div v-if="errors.problemSetId" class="apple-error">{{ errors.problemSetId }}</div>

        <button type="submit" class="apple-btn mt-3" :disabled="loading">
          <span v-if="loading" class="spinner-border spinner-border-sm me-2"></span>
          新增题目
        </button>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { useStore } from 'vuex'
import { showSuccess, showError } from '@/utils/message'

const store = useStore()
const loading = ref(false)
const availableProblemSets = ref<any[]>([])

const form = reactive({
  title: '',
  link: '',
  source: '',
  image: '',
  difficulty: '',
  problemSetId: ''
})
const errors = reactive<any>({})

const validateForm = () => {
  Object.keys(errors).forEach(key => delete errors[key])
  if (!form.title.trim()) errors.title = '请输入题目标题'
  if (!form.link.trim()) errors.link = '请输入题目链接'
  if (!form.source) errors.source = '请选择题目来源'
  if (!form.difficulty) errors.difficulty = '请选择题目难度'
  if (!form.problemSetId) errors.problemSetId = '请选择归属题集'
  return Object.keys(errors).length === 0
}

const handleSubmit = async () => {
  if (!validateForm()) return
  loading.value = true
  try {
    await store.dispatch('problem/createProblem', { ...form })
    showSuccess('题目新增成功！')
    // 通知父组件刷新
    emit('created')
  } catch (e: any) {
    showError(e.message || '新增失败')
  } finally {
    loading.value = false
  }
}

const emit = defineEmits(['close', 'created'])

const loadAvailableProblemSets = async () => {
  try {
    await store.dispatch('problemset/fetchProblemSets', { current: 1, size: 100 })
    availableProblemSets.value = store.getters['problemset/problemSets'] || []
  } catch {}
}

onMounted(() => {
  loadAvailableProblemSets()
})
</script>

<style scoped>
.apple-modal-mask {
  position: fixed;
  z-index: 2000;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.18);
  display: flex;
  align-items: center;
  justify-content: center;
}
.apple-modal-card {
  background: #fff;
  border-radius: 28px;
  box-shadow: 0 8px 32px 0 rgba(60,60,60,0.13);
  width: 98vw;
  max-width: 480px;
  min-width: 320px;
  padding: 38px 32px 28px 32px;
  position: relative;
  animation: modalIn 0.18s cubic-bezier(.4,1.6,.6,1) both;
}
@keyframes modalIn {
  0% { transform: translateY(40px) scale(0.98); opacity: 0; }
  100% { transform: none; opacity: 1; }
}
.apple-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 1.25rem;
  font-weight: 700;
  color: #111;
  margin-bottom: 18px;
}
.apple-modal-close {
  background: none;
  border: none;
  font-size: 1.3rem;
  color: #888;
  cursor: pointer;
  border-radius: 50%;
  padding: 2px 8px;
  transition: background 0.18s;
}
.apple-modal-close:hover {
  background: #f1f2f6;
  color: #007aff;
}
.form-row {
  display: flex;
  align-items: center;
  gap: 18px;
  margin-bottom: 1.2rem;
}
.form-label {
  min-width: 80px;
  text-align: right;
  font-weight: 500;
  color: #222;
  font-size: 1rem;
  letter-spacing: 0.01em;
}
.apple-input {
  flex: 1 1 0%;
  background: #f6f7fa;
  border: none;
  border-radius: 16px;
  padding: 13px 16px;
  font-size: 1.08rem;
  color: #222;
  outline: none;
  box-shadow: 0 1px 4px 0 rgba(60,60,60,0.04);
  transition: box-shadow 0.18s, background 0.18s;
}
.apple-input:focus {
  background: #fff;
  box-shadow: 0 0 0 2px #007aff33;
}
.apple-input::placeholder {
  color: #b0b3b8;
  font-weight: 400;
}
.is-invalid {
  box-shadow: 0 0 0 2px #ff3b30 !important;
  background: #fff0f0 !important;
}
.apple-error {
  color: #ff3b30;
  font-size: 14px;
  margin: -0.8rem 0 0.7rem 98px;
  min-height: 18px;
}
.apple-radio-group {
  display: flex;
  gap: 18px;
}
.apple-radio {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 1.08rem;
  font-weight: 500;
  cursor: pointer;
}
.apple-radio-label {
  border-radius: 12px;
  padding: 4px 14px;
  background: #f1f2f6;
  color: #888;
  transition: background 0.18s, color 0.18s;
}
.apple-radio input[type="radio"]:checked + .apple-radio-label,
.apple-radio input[type="radio"]:focus + .apple-radio-label {
  background: #007aff;
  color: #fff;
}
.apple-radio-label.easy {
  background: #e6f9e6;
  color: #1bbf3a;
}
.apple-radio input[type="radio"]:checked + .apple-radio-label.easy {
  background: #1bbf3a;
  color: #fff;
}
.apple-radio-label.medium {
  background: #fffbe6;
  color: #e6a700;
}
.apple-radio input[type="radio"]:checked + .apple-radio-label.medium {
  background: #e6a700;
  color: #fff;
}
.apple-radio-label.hard {
  background: #ffeaea;
  color: #e64a19;
}
.apple-radio input[type="radio"]:checked + .apple-radio-label.hard {
  background: #e64a19;
  color: #fff;
}
.apple-btn {
  width: 100%;
  background: #111;
  color: #fff;
  border: none;
  border-radius: 18px;
  font-size: 1.13rem;
  font-weight: 700;
  padding: 14px 0;
  margin-top: 18px;
  margin-bottom: 8px;
  box-shadow: 0 2px 8px 0 rgba(60,60,60,0.07);
  transition: background 0.18s, box-shadow 0.18s;
  cursor: pointer;
}
.apple-btn:disabled {
  background: #d1d5db;
  color: #fff;
  cursor: not-allowed;
}
.apple-btn:not(:disabled):hover {
  background: #222;
}
@media (max-width: 700px) {
  .apple-modal-card {
    max-width: 98vw;
    padding: 18px 2vw 12px 2vw;
  }
  .form-row {
    flex-direction: column;
    align-items: stretch;
    gap: 4px;
  }
  .form-label {
    text-align: left;
    min-width: 0;
    margin-bottom: 2px;
  }
  .apple-error {
    margin-left: 0;
  }
}
</style> 
<template>
  <!-- 添加到题集模态框 -->
  <div 
    class="modal fade" 
    id="addToProblemSetModal" 
    tabindex="-1" 
    aria-labelledby="addToProblemSetModalLabel" 
    aria-hidden="true"
  >
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="addToProblemSetModalLabel">
            <i class="bi bi-plus-circle me-2"></i>
            添加题目到题集
          </h5>
          <button 
            type="button" 
            class="btn-close" 
            data-bs-dismiss="modal" 
            aria-label="Close"
          ></button>
        </div>
        
        <div class="modal-body">
          <div v-if="loading" class="text-center py-3">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2 text-muted">加载题集列表...</p>
          </div>

          <div v-else-if="availableProblemSets.length === 0" class="text-center py-3">
            <i class="bi bi-collection text-muted" style="font-size: 3rem;"></i>
            <h6 class="mt-3 text-muted">暂无可用题集</h6>
            <p class="text-muted">您还没有创建任何题集</p>
            <router-link to="/problemsets/create" class="btn btn-primary btn-sm">
              <i class="bi bi-plus-circle me-1"></i>
              创建题集
            </router-link>
          </div>

          <div v-else>
            <div class="mb-3">
              <label class="form-label">选择题集：</label>
              <div class="form-check-container" style="max-height: 300px; overflow-y: auto;">
                <div 
                  v-for="problemSet in availableProblemSets" 
                  :key="problemSet.id"
                  class="form-check mb-2"
                >
                  <input
                    :id="`problemSet_${problemSet.id}`"
                    v-model="selectedProblemSets"
                    type="checkbox"
                    :value="problemSet.id"
                    class="form-check-input"
                  >
                  <label :for="`problemSet_${problemSet.id}`" class="form-check-label">
                    <div class="d-flex justify-content-between align-items-center">
                      <div>
                        <div class="fw-medium">{{ problemSet.name }}</div>
                        <small class="text-muted">
                          {{ problemSet.problemCount }} 题目 · 
                          {{ problemSet.isPublic ? '公开' : '私有' }}
                        </small>
                      </div>
                      <span 
                        v-if="problemSet.isPublic" 
                        class="badge bg-success"
                      >
                        公开
                      </span>
                      <span 
                        v-else 
                        class="badge bg-secondary"
                      >
                        私有
                      </span>
                    </div>
                  </label>
                </div>
              </div>
            </div>

            <div v-if="selectedProblemSets.length > 0" class="alert alert-info">
              <i class="bi bi-info-circle me-2"></i>
              已选择 {{ selectedProblemSets.length }} 个题集
            </div>
          </div>
        </div>
        
        <div class="modal-footer">
          <button 
            type="button" 
            class="btn btn-secondary" 
            data-bs-dismiss="modal"
          >
            取消
          </button>
          <button 
            type="button" 
            class="btn btn-primary" 
            :disabled="selectedProblemSets.length === 0 || submitting"
            @click="handleAddToProblemSets"
          >
            <span v-if="submitting" class="spinner-border spinner-border-sm me-2"></span>
            <i v-else class="bi bi-check-circle me-2"></i>
            {{ submitting ? '添加中...' : '确认添加' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { showSuccess, showError } from '@/utils/message'

export default {
  name: 'AddToProblemSetModal',
  props: {
    problemId: {
      type: [String, Number],
      required: true
    },
    problemTitle: {
      type: String,
      default: ''
    }
  },
  emits: ['added'],
  setup(props, { emit }) {
    const store = useStore()
    
    const loading = ref(false)
    const submitting = ref(false)
    const selectedProblemSets = ref([])
    const availableProblemSets = ref([])

    const user = computed(() => store.getters['auth/user'])

    const loadAvailableProblemSets = async () => {
      loading.value = true
      try {
        // 获取当前用户可编辑的题集
        await store.dispatch('problemset/fetchProblemSets', {
          current: 1,
          size: 100
        })
        
        // 过滤出当前用户可编辑的题集
        if (user.value) {
          availableProblemSets.value = store.getters['problemset/problemSets'].filter(ps => 
            user.value.role === 'ADMIN' || ps.creatorId === user.value.id
          )
        }
      } catch (error) {
        showError('获取题集列表失败')
        console.error('获取题集列表失败:', error)
      } finally {
        loading.value = false
      }
    }

    const handleAddToProblemSets = async () => {
      if (selectedProblemSets.value.length === 0) {
        showError('请选择至少一个题集')
        return
      }

      submitting.value = true
      try {
        // 模拟添加题目到题集的API调用
        for (const problemSetId of selectedProblemSets.value) {
          await addProblemToProblemSet(props.problemId, problemSetId)
        }

        showSuccess(`题目已成功添加到 ${selectedProblemSets.value.length} 个题集`)
        
        // 关闭模态框
        const modal = document.getElementById('addToProblemSetModal')
        const bsModal = window.bootstrap.Modal.getInstance(modal)
        if (bsModal) {
          bsModal.hide()
        }

        // 重置选择
        selectedProblemSets.value = []
        
        // 触发事件
        emit('added', {
          problemId: props.problemId,
          problemSetIds: selectedProblemSets.value
        })
      } catch (error) {
        showError('添加失败：' + error.message)
      } finally {
        submitting.value = false
      }
    }

    const addProblemToProblemSet = async (problemId, problemSetId) => {
      // 模拟API调用
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          // 模拟成功
          resolve()
        }, 500)
      })
    }

    onMounted(() => {
      loadAvailableProblemSets()
    })

    return {
      loading,
      submitting,
      selectedProblemSets,
      availableProblemSets,
      handleAddToProblemSets
    }
  }
}
</script>

<style scoped>
.form-check {
  padding: 0.75rem;
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  transition: all 0.2s ease-in-out;
}

.form-check:hover {
  border-color: #007bff;
  background-color: #f8f9fa;
}

.form-check-input:checked + .form-check-label {
  color: #007bff;
}

.form-check-label {
  cursor: pointer;
  width: 100%;
}

.form-check-container {
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  padding: 0.5rem;
}

.badge {
  font-size: 0.75em;
}
</style>

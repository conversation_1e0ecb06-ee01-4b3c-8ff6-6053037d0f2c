<template>
  <div class="apple-modal-mask" @click.self="$emit('close')">
    <div class="apple-modal-card">
      <div class="apple-modal-header">
        <span>创建题集</span>
        <button class="apple-modal-close" @click="$emit('close')">
          <i class="bi bi-x-lg"></i>
        </button>
      </div>
      <form @submit.prevent="handleSubmit">
        <div class="form-row">
          <label class="form-label">题集名称<span class="text-danger">*</span></label>
          <input v-model="form.name" type="text" class="apple-input" :class="{ 'is-invalid': errors.name }" placeholder="请输入题集名称" maxlength="100" required>
        </div>
        <div v-if="errors.name" class="apple-error">{{ errors.name }}</div>

        <div class="form-row">
          <label class="form-label">题集描述</label>
          <textarea v-model="form.description" class="apple-input" placeholder="题集简介（可选）" maxlength="500"></textarea>
        </div>

        <div class="form-row">
          <label class="form-label">封面图片</label>
          <input v-model="form.coverImage" type="url" class="apple-input" placeholder="图片链接（可选）">
        </div>

        <div class="form-row">
          <label class="form-label">可见性<span class="text-danger">*</span></label>
          <div class="apple-radio-group">
            <label class="apple-radio">
              <input type="radio" v-model="form.isPublic" :value="1" required>
              <span class="apple-radio-label">公开</span>
            </label>
            <label class="apple-radio">
              <input type="radio" v-model="form.isPublic" :value="0">
              <span class="apple-radio-label">私有</span>
            </label>
          </div>
        </div>
        <div v-if="errors.isPublic" class="apple-error">{{ errors.isPublic }}</div>

        <div class="form-row">
          <label class="form-label">标签</label>
          <input v-model="form.tags" type="text" class="apple-input" placeholder="标签，用逗号分隔">
        </div>

        <button type="submit" class="apple-btn mt-3" :disabled="loading">
          <span v-if="loading" class="spinner-border spinner-border-sm me-2"></span>
          创建题集
        </button>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import { useStore } from 'vuex'
import { showSuccess, showError } from '@/utils/message'

const store = useStore()
const loading = ref(false)
const form = reactive({
  name: '',
  description: '',
  coverImage: '',
  isPublic: 1,
  tags: ''
})
const errors = reactive<any>({})

const validateForm = () => {
  Object.keys(errors).forEach(key => delete errors[key])
  if (!form.name.trim()) errors.name = '请输入题集名称'
  if (form.isPublic === null || form.isPublic === undefined) errors.isPublic = '请选择可见性'
  return Object.keys(errors).length === 0
}

const emit = defineEmits(['close', 'created'])

const handleSubmit = async () => {
  if (!validateForm()) return
  loading.value = true
  try {
    await store.dispatch('problemset/createProblemSet', { ...form })
    showSuccess('题集创建成功！')
    emit('created')
  } catch (e: any) {
    showError(e.message || '创建失败')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
/* 复用 Apple 风格 modal 样式，可参考 CreateProblemModal.vue */
.apple-modal-mask {
  position: fixed;
  z-index: 2000;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.18);
  display: flex;
  align-items: center;
  justify-content: center;
}
.apple-modal-card {
  background: #fff;
  border-radius: 28px;
  box-shadow: 0 8px 32px 0 rgba(60,60,60,0.13);
  width: 98vw;
  max-width: 480px;
  min-width: 320px;
  padding: 38px 32px 28px 32px;
  position: relative;
  animation: modalIn 0.18s cubic-bezier(.4,1.6,.6,1) both;
}
@keyframes modalIn {
  0% { transform: translateY(40px) scale(0.98); opacity: 0; }
  100% { transform: none; opacity: 1; }
}
.apple-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 1.25rem;
  font-weight: 700;
  color: #111;
  margin-bottom: 18px;
}
.apple-modal-close {
  background: none;
  border: none;
  font-size: 1.3rem;
  color: #888;
  cursor: pointer;
  border-radius: 50%;
  padding: 2px 8px;
  transition: background 0.18s;
}
.apple-modal-close:hover {
  background: #f1f2f6;
  color: #007aff;
}
.form-row {
  display: flex;
  align-items: center;
  gap: 18px;
  margin-bottom: 1.2rem;
}
.form-label {
  min-width: 80px;
  text-align: right;
  font-weight: 500;
  color: #222;
  font-size: 1rem;
  letter-spacing: 0.01em;
}
.apple-input {
  flex: 1 1 0%;
  background: #f6f7fa;
  border: none;
  border-radius: 16px;
  padding: 13px 16px;
  font-size: 1.08rem;
  color: #222;
  outline: none;
  box-shadow: 0 1px 4px 0 rgba(60,60,60,0.04);
  transition: box-shadow 0.18s, background 0.18s;
}
.apple-input:focus {
  background: #fff;
  box-shadow: 0 0 0 2px #007aff33;
}
.apple-input::placeholder {
  color: #b0b3b8;
  font-weight: 400;
}
.is-invalid {
  box-shadow: 0 0 0 2px #ff3b30 !important;
  background: #fff0f0 !important;
}
.apple-error {
  color: #ff3b30;
  font-size: 14px;
  margin: -0.8rem 0 0.7rem 98px;
  min-height: 18px;
}
.apple-radio-group {
  display: flex;
  gap: 18px;
}
.apple-radio {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 1.08rem;
  font-weight: 500;
  cursor: pointer;
}
.apple-radio-label {
  border-radius: 12px;
  padding: 4px 14px;
  background: #f1f2f6;
  color: #888;
  transition: background 0.18s, color 0.18s;
}
.apple-radio input[type="radio"]:checked + .apple-radio-label,
.apple-radio input[type="radio"]:focus + .apple-radio-label {
  background: #007aff;
  color: #fff;
}
.apple-btn {
  width: 100%;
  background: #111;
  color: #fff;
  border: none;
  border-radius: 18px;
  font-size: 1.13rem;
  font-weight: 700;
  padding: 14px 0;
  margin-top: 18px;
  margin-bottom: 8px;
  box-shadow: 0 2px 8px 0 rgba(60,60,60,0.07);
  transition: background 0.18s, box-shadow 0.18s;
  cursor: pointer;
}
.apple-btn:disabled {
  background: #d1d5db;
  color: #fff;
  cursor: not-allowed;
}
.apple-btn:not(:disabled):hover {
  background: #222;
}
@media (max-width: 700px) {
  .apple-modal-card {
    max-width: 98vw;
    padding: 18px 2vw 12px 2vw;
  }
  .form-row {
    flex-direction: column;
    align-items: stretch;
    gap: 4px;
  }
  .form-label {
    text-align: left;
    min-width: 0;
    margin-bottom: 2px;
  }
  .apple-error {
    margin-left: 0;
  }
}
</style> 
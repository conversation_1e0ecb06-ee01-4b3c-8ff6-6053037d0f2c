{"name": "code-combined-frontend", "version": "1.0.0", "description": "Code-Combined算法题集网站前端", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@popperjs/core": "^2.11.8", "axios": "^1.4.0", "bootstrap": "^5.3.0", "bootstrap-icons": "^1.10.5", "nprogress": "^0.2.0", "sweetalert2": "^11.7.20", "vue": "^3.3.4", "vue-router": "^4.2.4", "vuex": "^4.1.0"}, "devDependencies": {"@types/node": "^20.3.1", "@types/nprogress": "^0.2.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-vue": "^4.2.3", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^11.0.3", "axios-mock-adapter": "^2.1.0", "eslint": "^8.45.0", "eslint-plugin-vue": "^9.15.1", "prettier": "^3.0.0", "rollup-plugin-visualizer": "^6.0.3", "typescript": "^5.0.2", "vite": "^4.4.5", "vite-plugin-compression": "^0.5.1", "vue-tsc": "^1.4.2"}, "keywords": ["vue3", "bootstrap", "algorithm", "leetcode", "problem-set"], "author": "Code-Combined Team", "license": "MIT"}
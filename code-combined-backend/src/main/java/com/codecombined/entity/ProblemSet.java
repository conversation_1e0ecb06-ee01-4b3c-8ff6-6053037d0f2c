package com.codecombined.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 题集实体类
 * 
 * <AUTHOR> Team
 * @since 2025-06-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("problem_sets")
public class ProblemSet {

    /**
     * 题集ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 题集名称
     */
    private String name;

    /**
     * 题集描述
     */
    private String description;

    /**
     * 封面图片URL
     */
    private String coverImage;

    /**
     * 创建者ID
     */
    private Long creatorId;

    /**
     * 是否公开
     */
    private Boolean isPublic;

    /**
     * 题目数量
     */
    private Integer problemCount;

    /**
     * 浏览次数
     */
    private Integer viewCount;

    /**
     * 点赞数
     */
    private Integer likeCount;

    /**
     * 状态：DRAFT/PUBLISHED/ARCHIVED
     */
    private String status;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    /**
     * 逻辑删除：0-未删除，1-已删除
     */
    @TableLogic
    private Integer deleted;

    /**
     * 创建者用户名（非数据库字段）
     */
    @TableField(exist = false)
    private String creatorName;

    /**
     * 题集状态枚举
     */
    public enum Status {
        DRAFT, PUBLISHED, ARCHIVED
    }
}

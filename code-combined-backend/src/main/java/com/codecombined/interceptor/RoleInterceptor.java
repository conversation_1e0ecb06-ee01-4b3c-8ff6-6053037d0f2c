package com.codecombined.interceptor;

import com.codecombined.annotation.RequireRole;
import com.codecombined.common.Result;
import com.codecombined.enums.UserRole;

import com.codecombined.util.UserContext;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;


import java.io.IOException;
import java.lang.reflect.Method;

/**
 * 角色权限拦截器
 * 检查用户是否有足够的权限访问接口
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class RoleInterceptor implements HandlerInterceptor {

    private final ObjectMapper objectMapper;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 只处理方法处理器
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;
        Method method = handlerMethod.getMethod();
        
        // 检查方法上的注解
        RequireRole methodAnnotation = method.getAnnotation(RequireRole.class);
        // 检查类上的注解
        RequireRole classAnnotation = handlerMethod.getBeanType().getAnnotation(RequireRole.class);
        
        RequireRole requireRole = methodAnnotation != null ? methodAnnotation : classAnnotation;
        
        // 如果没有权限注解，直接通过
        if (requireRole == null) {
            return true;
        }

        // 获取当前用户信息
        Long currentUserId = UserContext.getCurrentUserId();
        UserRole currentUserRole = UserContext.getCurrentUserRole();
        
        if (currentUserId == null || currentUserRole == null) {
            log.warn("用户未登录或用户信息不完整，拒绝访问: {}", request.getRequestURI());
            writeErrorResponse(response, "用户未登录", 401);
            return false;
        }

        // 检查角色权限
        UserRole requiredRole = requireRole.value();
        boolean allowHigher = requireRole.allowHigher();
        
        if (!hasPermission(currentUserRole, requiredRole, allowHigher)) {
            log.warn("用户权限不足，拒绝访问: userId={}, userRole={}, requiredRole={}, uri={}", 
                    currentUserId, currentUserRole, requiredRole, request.getRequestURI());
            writeErrorResponse(response, "权限不足", 403);
            return false;
        }

        log.debug("权限检查通过: userId={}, userRole={}, requiredRole={}, uri={}", 
                currentUserId, currentUserRole, requiredRole, request.getRequestURI());
        
        return true;
    }

    /**
     * 检查用户是否有权限
     */
    private boolean hasPermission(UserRole userRole, UserRole requiredRole, boolean allowHigher) {
        if (userRole == requiredRole) {
            return true;
        }
        
        if (!allowHigher) {
            return false;
        }
        
        // 检查是否是更高级别的角色
        return isHigherRole(userRole, requiredRole);
    }

    /**
     * 判断是否是更高级别的角色
     */
    private boolean isHigherRole(UserRole userRole, UserRole requiredRole) {
        // 角色等级：ADMIN > USER
        if (userRole == UserRole.ADMIN && requiredRole == UserRole.USER) {
            return true;
        }
        
        return false;
    }

    /**
     * 写入错误响应
     */
    private void writeErrorResponse(HttpServletResponse response, String message, int status) throws IOException {
        response.setStatus(status);
        response.setContentType("application/json;charset=UTF-8");
        
        Result<Object> result = Result.error(message);
        String jsonResponse = objectMapper.writeValueAsString(result);
        
        response.getWriter().write(jsonResponse);
    }
}

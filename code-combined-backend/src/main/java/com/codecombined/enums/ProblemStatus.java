package com.codecombined.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 题目状态枚举
 */
@Getter
public enum ProblemStatus {
    
    /**
     * 草稿
     */
    DRAFT("draft", "草稿", "#6c757d"),
    
    /**
     * 已发布
     */
    PUBLISHED("published", "已发布", "#28a745"),
    
    /**
     * 已归档
     */
    ARCHIVED("archived", "已归档", "#ffc107");
    
    /**
     * 状态代码
     */
    private final String code;
    
    /**
     * 状态名称
     */
    private final String name;
    
    /**
     * 显示颜色
     */
    private final String color;
    
    ProblemStatus(String code, String name, String color) {
        this.code = code;
        this.name = name;
        this.color = color;
    }
    
    /**
     * 获取状态代码（用于JSON序列化）
     */
    @JsonValue
    public String getCode() {
        return code;
    }
    
    /**
     * 根据代码获取状态
     */
    public static ProblemStatus fromCode(String code) {
        if (code == null) {
            return null;
        }
        
        for (ProblemStatus status : ProblemStatus.values()) {
            if (status.getCode().equalsIgnoreCase(code)) {
                return status;
            }
        }
        
        throw new IllegalArgumentException("未知的题目状态代码: " + code);
    }
    
    /**
     * 根据名称获取状态
     */
    public static ProblemStatus fromName(String name) {
        if (name == null) {
            return null;
        }
        
        for (ProblemStatus status : ProblemStatus.values()) {
            if (status.getName().equals(name)) {
                return status;
            }
        }
        
        throw new IllegalArgumentException("未知的题目状态名称: " + name);
    }
    
    /**
     * 检查是否是草稿状态
     */
    public boolean isDraft() {
        return this == DRAFT;
    }
    
    /**
     * 检查是否是已发布状态
     */
    public boolean isPublished() {
        return this == PUBLISHED;
    }
    
    /**
     * 检查是否是已归档状态
     */
    public boolean isArchived() {
        return this == ARCHIVED;
    }
    
    /**
     * 检查是否可以编辑
     */
    public boolean isEditable() {
        return this == DRAFT || this == PUBLISHED;
    }
    
    /**
     * 检查是否对用户可见
     */
    public boolean isVisible() {
        return this == PUBLISHED;
    }
    
    /**
     * 获取所有状态的代码列表
     */
    public static String[] getAllCodes() {
        ProblemStatus[] statuses = ProblemStatus.values();
        String[] codes = new String[statuses.length];
        for (int i = 0; i < statuses.length; i++) {
            codes[i] = statuses[i].getCode();
        }
        return codes;
    }
    
    /**
     * 获取所有状态的名称列表
     */
    public static String[] getAllNames() {
        ProblemStatus[] statuses = ProblemStatus.values();
        String[] names = new String[statuses.length];
        for (int i = 0; i < statuses.length; i++) {
            names[i] = statuses[i].getName();
        }
        return names;
    }
    
    @Override
    public String toString() {
        return this.code;
    }
}

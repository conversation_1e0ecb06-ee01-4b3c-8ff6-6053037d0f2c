package com.codecombined.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 用户角色枚举
 */
@Getter
public enum UserRole {
    
    /**
     * 普通用户
     */
    USER("user", "普通用户", 1),
    
    /**
     * 管理员
     */
    ADMIN("admin", "管理员", 2);
    
    /**
     * 角色代码
     */
    private final String code;
    
    /**
     * 角色名称
     */
    private final String name;
    
    /**
     * 角色等级（数字越大权限越高）
     */
    private final Integer level;
    
    UserRole(String code, String name, Integer level) {
        this.code = code;
        this.name = name;
        this.level = level;
    }
    
    /**
     * 获取角色代码（用于JSON序列化）
     */
    @JsonValue
    public String getCode() {
        return code;
    }
    
    /**
     * 根据代码获取角色
     */
    public static UserRole fromCode(String code) {
        if (code == null) {
            return null;
        }
        
        for (UserRole role : UserRole.values()) {
            if (role.getCode().equalsIgnoreCase(code)) {
                return role;
            }
        }
        
        throw new IllegalArgumentException("未知的用户角色代码: " + code);
    }
    
    /**
     * 根据名称获取角色
     */
    public static UserRole fromName(String name) {
        if (name == null) {
            return null;
        }
        
        for (UserRole role : UserRole.values()) {
            if (role.getName().equals(name)) {
                return role;
            }
        }
        
        throw new IllegalArgumentException("未知的用户角色名称: " + name);
    }
    
    /**
     * 检查当前角色是否有权限访问目标角色的资源
     * 
     * @param targetRole 目标角色
     * @return true表示有权限，false表示无权限
     */
    public boolean hasPermission(UserRole targetRole) {
        return this.level >= targetRole.level;
    }
    
    /**
     * 检查当前角色是否是管理员
     */
    public boolean isAdmin() {
        return this == ADMIN;
    }
    
    /**
     * 检查当前角色是否是普通用户
     */
    public boolean isUser() {
        return this == USER;
    }
    
    /**
     * 获取所有角色的代码列表
     */
    public static String[] getAllCodes() {
        UserRole[] roles = UserRole.values();
        String[] codes = new String[roles.length];
        for (int i = 0; i < roles.length; i++) {
            codes[i] = roles[i].getCode();
        }
        return codes;
    }
    
    /**
     * 获取所有角色的名称列表
     */
    public static String[] getAllNames() {
        UserRole[] roles = UserRole.values();
        String[] names = new String[roles.length];
        for (int i = 0; i < roles.length; i++) {
            names[i] = roles[i].getName();
        }
        return names;
    }
    
    @Override
    public String toString() {
        return this.code;
    }
}

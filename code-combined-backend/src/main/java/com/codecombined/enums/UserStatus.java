package com.codecombined.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 用户状态枚举
 */
@Getter
public enum UserStatus {
    
    /**
     * 禁用
     */
    DISABLED(0, "disabled", "禁用", "#dc3545"),
    
    /**
     * 启用
     */
    ENABLED(1, "enabled", "启用", "#28a745");
    
    /**
     * 状态值
     */
    private final Integer value;
    
    /**
     * 状态代码
     */
    private final String code;
    
    /**
     * 状态名称
     */
    private final String name;
    
    /**
     * 显示颜色
     */
    private final String color;
    
    UserStatus(Integer value, String code, String name, String color) {
        this.value = value;
        this.code = code;
        this.name = name;
        this.color = color;
    }
    
    /**
     * 获取状态值（用于数据库存储）
     */
    @JsonValue
    public Integer getValue() {
        return value;
    }
    
    /**
     * 根据值获取状态
     */
    public static UserStatus fromValue(Integer value) {
        if (value == null) {
            return null;
        }
        
        for (UserStatus status : UserStatus.values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        
        throw new IllegalArgumentException("未知的用户状态值: " + value);
    }
    
    /**
     * 根据代码获取状态
     */
    public static UserStatus fromCode(String code) {
        if (code == null) {
            return null;
        }
        
        for (UserStatus status : UserStatus.values()) {
            if (status.getCode().equalsIgnoreCase(code)) {
                return status;
            }
        }
        
        throw new IllegalArgumentException("未知的用户状态代码: " + code);
    }
    
    /**
     * 根据名称获取状态
     */
    public static UserStatus fromName(String name) {
        if (name == null) {
            return null;
        }
        
        for (UserStatus status : UserStatus.values()) {
            if (status.getName().equals(name)) {
                return status;
            }
        }
        
        throw new IllegalArgumentException("未知的用户状态名称: " + name);
    }
    
    /**
     * 检查是否是启用状态
     */
    public boolean isEnabled() {
        return this == ENABLED;
    }
    
    /**
     * 检查是否是禁用状态
     */
    public boolean isDisabled() {
        return this == DISABLED;
    }
    
    /**
     * 检查用户是否可以登录
     */
    public boolean canLogin() {
        return this == ENABLED;
    }
    
    /**
     * 检查用户是否可以操作
     */
    public boolean canOperate() {
        return this == ENABLED;
    }
    
    /**
     * 获取所有状态的值列表
     */
    public static Integer[] getAllValues() {
        UserStatus[] statuses = UserStatus.values();
        Integer[] values = new Integer[statuses.length];
        for (int i = 0; i < statuses.length; i++) {
            values[i] = statuses[i].getValue();
        }
        return values;
    }
    
    /**
     * 获取所有状态的代码列表
     */
    public static String[] getAllCodes() {
        UserStatus[] statuses = UserStatus.values();
        String[] codes = new String[statuses.length];
        for (int i = 0; i < statuses.length; i++) {
            codes[i] = statuses[i].getCode();
        }
        return codes;
    }
    
    /**
     * 获取所有状态的名称列表
     */
    public static String[] getAllNames() {
        UserStatus[] statuses = UserStatus.values();
        String[] names = new String[statuses.length];
        for (int i = 0; i < statuses.length; i++) {
            names[i] = statuses[i].getName();
        }
        return names;
    }
    
    @Override
    public String toString() {
        return this.code;
    }
}

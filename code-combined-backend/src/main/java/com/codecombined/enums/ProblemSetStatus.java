package com.codecombined.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 题集状态枚举
 */
@Getter
public enum ProblemSetStatus {
    
    /**
     * 草稿
     */
    DRAFT("draft", "草稿", "#6c757d"),
    
    /**
     * 已发布
     */
    PUBLISHED("published", "已发布", "#28a745"),
    
    /**
     * 已归档
     */
    ARCHIVED("archived", "已归档", "#ffc107");
    
    /**
     * 状态代码
     */
    private final String code;
    
    /**
     * 状态名称
     */
    private final String name;
    
    /**
     * 显示颜色
     */
    private final String color;
    
    ProblemSetStatus(String code, String name, String color) {
        this.code = code;
        this.name = name;
        this.color = color;
    }
    
    /**
     * 获取状态代码（用于JSON序列化）
     */
    @JsonValue
    public String getCode() {
        return code;
    }
    
    /**
     * 根据代码获取状态
     */
    public static ProblemSetStatus fromCode(String code) {
        if (code == null) {
            return null;
        }
        
        for (ProblemSetStatus status : ProblemSetStatus.values()) {
            if (status.getCode().equalsIgnoreCase(code)) {
                return status;
            }
        }
        
        throw new IllegalArgumentException("未知的题集状态代码: " + code);
    }
    
    /**
     * 根据名称获取状态
     */
    public static ProblemSetStatus fromName(String name) {
        if (name == null) {
            return null;
        }
        
        for (ProblemSetStatus status : ProblemSetStatus.values()) {
            if (status.getName().equals(name)) {
                return status;
            }
        }
        
        throw new IllegalArgumentException("未知的题集状态名称: " + name);
    }
    
    /**
     * 检查是否是草稿状态
     */
    public boolean isDraft() {
        return this == DRAFT;
    }
    
    /**
     * 检查是否是已发布状态
     */
    public boolean isPublished() {
        return this == PUBLISHED;
    }
    
    /**
     * 检查是否是已归档状态
     */
    public boolean isArchived() {
        return this == ARCHIVED;
    }
    
    /**
     * 检查是否可以编辑
     */
    public boolean isEditable() {
        return this == DRAFT || this == PUBLISHED;
    }
    
    /**
     * 检查是否对用户可见
     */
    public boolean isVisible() {
        return this == PUBLISHED;
    }
    
    /**
     * 获取所有状态的代码列表
     */
    public static String[] getAllCodes() {
        ProblemSetStatus[] statuses = ProblemSetStatus.values();
        String[] codes = new String[statuses.length];
        for (int i = 0; i < statuses.length; i++) {
            codes[i] = statuses[i].getCode();
        }
        return codes;
    }
    
    /**
     * 获取所有状态的名称列表
     */
    public static String[] getAllNames() {
        ProblemSetStatus[] statuses = ProblemSetStatus.values();
        String[] names = new String[statuses.length];
        for (int i = 0; i < statuses.length; i++) {
            names[i] = statuses[i].getName();
        }
        return names;
    }
    
    @Override
    public String toString() {
        return this.code;
    }
}

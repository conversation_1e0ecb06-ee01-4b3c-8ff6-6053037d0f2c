package com.codecombined.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 编程语言枚举
 */
@Getter
public enum ProgrammingLanguage {
    
    /**
     * Java
     */
    JAVA("java", "Java", ".java", "java", "#f89820"),
    
    /**
     * Python
     */
    PYTHON("python", "Python", ".py", "python3", "#3776ab"),
    
    /**
     * C++
     */
    CPP("cpp", "C++", ".cpp", "g++", "#00599c"),
    
    /**
     * C
     */
    C("c", "C", ".c", "gcc", "#a8b9cc"),
    
    /**
     * JavaScript
     */
    JAVASCRIPT("javascript", "JavaScript", ".js", "node", "#f7df1e"),
    
    /**
     * Go
     */
    GO("go", "Go", ".go", "go", "#00add8"),
    
    /**
     * Rust
     */
    RUST("rust", "Rust", ".rs", "rustc", "#000000"),
    
    /**
     * Kotlin
     */
    KOTLIN("kotlin", "Kotlin", ".kt", "kotlinc", "#7f52ff"),
    
    /**
     * TypeScript
     */
    TYPESCRIPT("typescript", "TypeScript", ".ts", "tsc", "#3178c6"),
    
    /**
     * C#
     */
    CSHARP("csharp", "C#", ".cs", "csc", "#239120"),
    
    /**
     * PHP
     */
    PHP("php", "PHP", ".php", "php", "#777bb4"),
    
    /**
     * Ruby
     */
    RUBY("ruby", "Ruby", ".rb", "ruby", "#cc342d");
    
    /**
     * 语言代码
     */
    private final String code;
    
    /**
     * 语言名称
     */
    private final String name;
    
    /**
     * 文件扩展名
     */
    private final String extension;
    
    /**
     * 编译器/解释器命令
     */
    private final String compiler;
    
    /**
     * 显示颜色
     */
    private final String color;
    
    ProgrammingLanguage(String code, String name, String extension, String compiler, String color) {
        this.code = code;
        this.name = name;
        this.extension = extension;
        this.compiler = compiler;
        this.color = color;
    }
    
    /**
     * 获取语言代码（用于JSON序列化）
     */
    @JsonValue
    public String getCode() {
        return code;
    }
    
    /**
     * 根据代码获取语言
     */
    public static ProgrammingLanguage fromCode(String code) {
        if (code == null) {
            return null;
        }
        
        for (ProgrammingLanguage language : ProgrammingLanguage.values()) {
            if (language.getCode().equalsIgnoreCase(code)) {
                return language;
            }
        }
        
        throw new IllegalArgumentException("未知的编程语言代码: " + code);
    }
    
    /**
     * 根据名称获取语言
     */
    public static ProgrammingLanguage fromName(String name) {
        if (name == null) {
            return null;
        }
        
        for (ProgrammingLanguage language : ProgrammingLanguage.values()) {
            if (language.getName().equalsIgnoreCase(name)) {
                return language;
            }
        }
        
        throw new IllegalArgumentException("未知的编程语言名称: " + name);
    }
    
    /**
     * 根据文件扩展名获取语言
     */
    public static ProgrammingLanguage fromExtension(String extension) {
        if (extension == null) {
            return null;
        }
        
        // 确保扩展名以点开头
        if (!extension.startsWith(".")) {
            extension = "." + extension;
        }
        
        for (ProgrammingLanguage language : ProgrammingLanguage.values()) {
            if (language.getExtension().equalsIgnoreCase(extension)) {
                return language;
            }
        }
        
        throw new IllegalArgumentException("未知的文件扩展名: " + extension);
    }
    
    /**
     * 检查是否是编译型语言
     */
    public boolean isCompiled() {
        return this == JAVA || this == CPP || this == C || this == RUST || 
               this == KOTLIN || this == CSHARP || this == GO;
    }
    
    /**
     * 检查是否是解释型语言
     */
    public boolean isInterpreted() {
        return !isCompiled();
    }
    
    /**
     * 检查是否支持面向对象
     */
    public boolean isObjectOriented() {
        return this == JAVA || this == CPP || this == PYTHON || this == JAVASCRIPT ||
               this == KOTLIN || this == TYPESCRIPT || this == CSHARP || this == PHP || this == RUBY;
    }
    
    /**
     * 获取默认的时间限制（毫秒）
     */
    public int getDefaultTimeLimit() {
        switch (this) {
            case JAVA:
                return 3000;
            case PYTHON:
                return 5000;
            case CPP:
            case C:
                return 1000;
            case JAVASCRIPT:
            case TYPESCRIPT:
                return 3000;
            case GO:
                return 2000;
            case RUST:
                return 1000;
            case KOTLIN:
                return 3000;
            case CSHARP:
                return 3000;
            case PHP:
                return 5000;
            case RUBY:
                return 5000;
            default:
                return 2000;
        }
    }
    
    /**
     * 获取默认的内存限制（MB）
     */
    public int getDefaultMemoryLimit() {
        switch (this) {
            case JAVA:
            case KOTLIN:
                return 512;
            case PYTHON:
                return 256;
            case CPP:
            case C:
            case RUST:
                return 128;
            case JAVASCRIPT:
            case TYPESCRIPT:
                return 256;
            case GO:
                return 256;
            case CSHARP:
                return 512;
            case PHP:
                return 256;
            case RUBY:
                return 256;
            default:
                return 256;
        }
    }
    
    /**
     * 获取所有语言的代码列表
     */
    public static String[] getAllCodes() {
        ProgrammingLanguage[] languages = ProgrammingLanguage.values();
        String[] codes = new String[languages.length];
        for (int i = 0; i < languages.length; i++) {
            codes[i] = languages[i].getCode();
        }
        return codes;
    }
    
    /**
     * 获取所有语言的名称列表
     */
    public static String[] getAllNames() {
        ProgrammingLanguage[] languages = ProgrammingLanguage.values();
        String[] names = new String[languages.length];
        for (int i = 0; i < languages.length; i++) {
            names[i] = languages[i].getName();
        }
        return names;
    }
    
    /**
     * 获取常用语言列表
     */
    public static ProgrammingLanguage[] getPopularLanguages() {
        return new ProgrammingLanguage[]{JAVA, PYTHON, CPP, C, JAVASCRIPT, GO};
    }
    
    @Override
    public String toString() {
        return this.code;
    }
}

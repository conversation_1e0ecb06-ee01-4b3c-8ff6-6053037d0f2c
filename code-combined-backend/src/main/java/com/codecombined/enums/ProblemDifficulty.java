package com.codecombined.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 题目难度枚举
 */
@Getter
public enum ProblemDifficulty {
    
    /**
     * 简单
     */
    EASY("easy", "简单", 1, "#28a745"),
    
    /**
     * 中等
     */
    MEDIUM("medium", "中等", 2, "#ffc107"),
    
    /**
     * 困难
     */
    HARD("hard", "困难", 3, "#dc3545");
    
    /**
     * 难度代码
     */
    private final String code;
    
    /**
     * 难度名称
     */
    private final String name;
    
    /**
     * 难度等级（数字越大难度越高）
     */
    private final Integer level;
    
    /**
     * 显示颜色
     */
    private final String color;
    
    ProblemDifficulty(String code, String name, Integer level, String color) {
        this.code = code;
        this.name = name;
        this.level = level;
        this.color = color;
    }
    
    /**
     * 获取难度代码（用于JSON序列化）
     */
    @JsonValue
    public String getCode() {
        return code;
    }
    
    /**
     * 根据代码获取难度
     */
    public static ProblemDifficulty fromCode(String code) {
        if (code == null) {
            return null;
        }
        
        for (ProblemDifficulty difficulty : ProblemDifficulty.values()) {
            if (difficulty.getCode().equalsIgnoreCase(code)) {
                return difficulty;
            }
        }
        
        throw new IllegalArgumentException("未知的题目难度代码: " + code);
    }
    
    /**
     * 根据名称获取难度
     */
    public static ProblemDifficulty fromName(String name) {
        if (name == null) {
            return null;
        }
        
        for (ProblemDifficulty difficulty : ProblemDifficulty.values()) {
            if (difficulty.getName().equals(name)) {
                return difficulty;
            }
        }
        
        throw new IllegalArgumentException("未知的题目难度名称: " + name);
    }
    
    /**
     * 检查是否是简单难度
     */
    public boolean isEasy() {
        return this == EASY;
    }
    
    /**
     * 检查是否是中等难度
     */
    public boolean isMedium() {
        return this == MEDIUM;
    }
    
    /**
     * 检查是否是困难难度
     */
    public boolean isHard() {
        return this == HARD;
    }
    
    /**
     * 获取所有难度的代码列表
     */
    public static String[] getAllCodes() {
        ProblemDifficulty[] difficulties = ProblemDifficulty.values();
        String[] codes = new String[difficulties.length];
        for (int i = 0; i < difficulties.length; i++) {
            codes[i] = difficulties[i].getCode();
        }
        return codes;
    }
    
    /**
     * 获取所有难度的名称列表
     */
    public static String[] getAllNames() {
        ProblemDifficulty[] difficulties = ProblemDifficulty.values();
        String[] names = new String[difficulties.length];
        for (int i = 0; i < difficulties.length; i++) {
            names[i] = difficulties[i].getName();
        }
        return names;
    }
    
    @Override
    public String toString() {
        return this.code;
    }
}

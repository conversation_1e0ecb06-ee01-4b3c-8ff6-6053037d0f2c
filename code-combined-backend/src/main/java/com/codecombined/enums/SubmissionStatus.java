package com.codecombined.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 提交状态枚举
 */
@Getter
public enum SubmissionStatus {
    
    /**
     * 等待判题
     */
    PENDING("pending", "等待判题", "#6c757d"),
    
    /**
     * 判题中
     */
    JUDGING("judging", "判题中", "#17a2b8"),
    
    /**
     * 通过
     */
    ACCEPTED("accepted", "通过", "#28a745"),
    
    /**
     * 答案错误
     */
    WRONG_ANSWER("wrong_answer", "答案错误", "#dc3545"),
    
    /**
     * 时间超限
     */
    TIME_LIMIT_EXCEEDED("time_limit_exceeded", "时间超限", "#fd7e14"),
    
    /**
     * 内存超限
     */
    MEMORY_LIMIT_EXCEEDED("memory_limit_exceeded", "内存超限", "#e83e8c"),
    
    /**
     * 运行时错误
     */
    RUNTIME_ERROR("runtime_error", "运行时错误", "#6f42c1"),
    
    /**
     * 编译错误
     */
    COMPILE_ERROR("compile_error", "编译错误", "#fd7e14"),
    
    /**
     * 系统错误
     */
    SYSTEM_ERROR("system_error", "系统错误", "#6c757d"),
    
    /**
     * 输出超限
     */
    OUTPUT_LIMIT_EXCEEDED("output_limit_exceeded", "输出超限", "#20c997"),
    
    /**
     * 格式错误
     */
    PRESENTATION_ERROR("presentation_error", "格式错误", "#ffc107");
    
    /**
     * 状态代码
     */
    private final String code;
    
    /**
     * 状态名称
     */
    private final String name;
    
    /**
     * 显示颜色
     */
    private final String color;
    
    SubmissionStatus(String code, String name, String color) {
        this.code = code;
        this.name = name;
        this.color = color;
    }
    
    /**
     * 获取状态代码（用于JSON序列化）
     */
    @JsonValue
    public String getCode() {
        return code;
    }
    
    /**
     * 根据代码获取状态
     */
    public static SubmissionStatus fromCode(String code) {
        if (code == null) {
            return null;
        }
        
        for (SubmissionStatus status : SubmissionStatus.values()) {
            if (status.getCode().equalsIgnoreCase(code)) {
                return status;
            }
        }
        
        throw new IllegalArgumentException("未知的提交状态代码: " + code);
    }
    
    /**
     * 根据名称获取状态
     */
    public static SubmissionStatus fromName(String name) {
        if (name == null) {
            return null;
        }
        
        for (SubmissionStatus status : SubmissionStatus.values()) {
            if (status.getName().equals(name)) {
                return status;
            }
        }
        
        throw new IllegalArgumentException("未知的提交状态名称: " + name);
    }
    
    /**
     * 检查是否是等待状态
     */
    public boolean isPending() {
        return this == PENDING;
    }
    
    /**
     * 检查是否是判题中状态
     */
    public boolean isJudging() {
        return this == JUDGING;
    }
    
    /**
     * 检查是否是通过状态
     */
    public boolean isAccepted() {
        return this == ACCEPTED;
    }
    
    /**
     * 检查是否是错误状态
     */
    public boolean isError() {
        return this != PENDING && this != JUDGING && this != ACCEPTED;
    }
    
    /**
     * 检查是否是最终状态（已完成判题）
     */
    public boolean isFinal() {
        return this != PENDING && this != JUDGING;
    }
    
    /**
     * 检查是否可以重新提交
     */
    public boolean canResubmit() {
        return isFinal();
    }
    
    /**
     * 获取所有状态的代码列表
     */
    public static String[] getAllCodes() {
        SubmissionStatus[] statuses = SubmissionStatus.values();
        String[] codes = new String[statuses.length];
        for (int i = 0; i < statuses.length; i++) {
            codes[i] = statuses[i].getCode();
        }
        return codes;
    }
    
    /**
     * 获取所有状态的名称列表
     */
    public static String[] getAllNames() {
        SubmissionStatus[] statuses = SubmissionStatus.values();
        String[] names = new String[statuses.length];
        for (int i = 0; i < statuses.length; i++) {
            names[i] = statuses[i].getName();
        }
        return names;
    }
    
    @Override
    public String toString() {
        return this.code;
    }
}

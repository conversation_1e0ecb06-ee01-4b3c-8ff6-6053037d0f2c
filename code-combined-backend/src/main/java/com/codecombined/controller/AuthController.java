package com.codecombined.controller;

import com.codecombined.common.Result;
import com.codecombined.dto.LoginRequest;
import com.codecombined.dto.RegisterRequest;
import com.codecombined.dto.request.EmailVo;
import com.codecombined.entity.User;
import com.codecombined.service.UserService;
import com.codecombined.util.IpUtil;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.constraints.Email;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


import java.util.HashMap;
import java.util.Map;

/**
 * 认证控制器
 * 
 * <AUTHOR> Team
 * @since 2025-06-22
 */
@Slf4j
@RestController
@RequestMapping("/api/auth")
@Tag(name = "认证管理", description = "用户认证相关接口")
public class AuthController {

    @Autowired
    private UserService userService;

    /**
     * 用户注册
     */
    @PostMapping("/register")
    @Operation(summary = "用户注册")
    public Result<String> register(@Validated @RequestBody RegisterRequest request) {
        try {
            userService.register(request);
            return Result.success("注册成功");
        } catch (Exception e) {
            log.error("用户注册失败: {}", e.getMessage());
            return Result.error(e.getMessage());
        }
    }

    /**
     * 用户登录 (无认证模式)
     */
    @PostMapping("/login")
    @Operation(summary = "用户登录")
    public Result<Map<String, Object>> login(@Validated @RequestBody LoginRequest request,
                                            HttpServletRequest httpRequest) {
        try {
            // 验证用户凭据
            User user = userService.authenticate(request.getEmail(), request.getPassword());
            if (user == null) {
                return Result.error("邮箱或密码错误");
            }

            // 更新最后登录信息
            String ip = IpUtil.getClientIp(httpRequest);
            // TODO: 更新用户最后登录时间和IP

            // 无认证模式：返回用户信息而不是Token
            Map<String, Object> data = new HashMap<>();
            data.put("user", user);
            data.put("message", "登录成功");

            return Result.success("登录成功", data);
        } catch (Exception e) {
            log.error("用户登录失败: {}", e.getMessage());
            return Result.error(e.getMessage());
        }
    }

    /**
     * 发送邮箱验证码
     */
    @PostMapping("/send-email-code")
    @Operation(summary = "发送邮箱验证码")
    public Result<String> sendEmailCode(@RequestBody EmailVo emailVo) {
        log.info("发送邮箱验证码: {}", emailVo.getEmail());
        try {
            userService.sendEmailCode(emailVo.getEmail());
            return Result.success("验证码已发送，请查收邮件");
        } catch (Exception e) {
            log.error("发送邮箱验证码失败: {}", e.getMessage());
            return Result.error(e.getMessage());
        }
    }

    /**
     * 检查用户名是否可用
     */
    @GetMapping("/check-username")
    @Operation(summary = "检查用户名是否可用")
    public Result<Map<String, Boolean>> checkUsername(@RequestParam String username) {
        boolean available = userService.findByUsername(username) == null;
        Map<String, Boolean> data = new HashMap<>();
        data.put("available", available);
        return Result.success(data);
    }

    /**
     * 检查邮箱是否可用
     */
    @GetMapping("/check-email")
    @Operation(summary = "检查邮箱是否可用")
    public Result<Map<String, Boolean>> checkEmail(@RequestParam @Email(message = "邮箱格式不正确") String email) {
        boolean available = userService.findByEmail(email) == null;
        Map<String, Boolean> data = new HashMap<>();
        data.put("available", available);
        return Result.success(data);
    }

    /**
     * 获取当前用户信息 (无认证模式 - 返回默认管理员用户)
     */
    @GetMapping("/me")
    @Operation(summary = "获取当前用户信息")
    public Result<Map<String, Object>> getCurrentUser() {
        try {
            // 无认证模式：返回默认管理员用户信息
            User user = userService.findByEmail("<EMAIL>");
            if (user == null) {
                // 如果管理员用户不存在，返回默认信息
                Map<String, Object> defaultUser = new HashMap<>();
                defaultUser.put("id", 1L);
                defaultUser.put("username", "admin");
                defaultUser.put("email", "<EMAIL>");
                defaultUser.put("nickname", "系统管理员");
                defaultUser.put("avatar", "https://api.dicebear.com/7.x/avataaars/svg?seed=admin");
                defaultUser.put("role", "ADMIN");
                defaultUser.put("status", 1);
                defaultUser.put("points", 1000);
                defaultUser.put("bio", "系统管理员账户");
                defaultUser.put("location", "北京");
                defaultUser.put("company", "CodeCombined");
                defaultUser.put("github", "https://github.com/admin");
                defaultUser.put("website", "https://codecombined.com");

                return Result.success("获取用户信息成功", defaultUser);
            }

            // 构建返回数据
            Map<String, Object> userData = new HashMap<>();
            userData.put("id", user.getId());
            userData.put("username", user.getUsername());
            userData.put("email", user.getEmail());
            userData.put("nickname", user.getNickname());
            userData.put("avatar", user.getAvatar());
            userData.put("role", user.getRole());
            userData.put("status", user.getStatus());
            userData.put("points", user.getPoints());
            userData.put("bio", user.getBio());
            userData.put("location", user.getLocation());
            userData.put("company", user.getCompany());
            userData.put("github", user.getGithub());
            userData.put("website", user.getWebsite());
            userData.put("createTime", user.getCreateTime());
            userData.put("lastLoginTime", user.getLastLoginTime());

            return Result.success("获取用户信息成功", userData);
        } catch (Exception e) {
            log.error("获取当前用户信息失败: {}", e.getMessage());
            return Result.error("获取用户信息失败");
        }
    }
}

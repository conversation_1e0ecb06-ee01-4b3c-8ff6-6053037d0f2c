package com.codecombined.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.codecombined.common.Result;
import com.codecombined.entity.Problem;
import com.codecombined.service.ProblemService;
import com.codecombined.util.JwtUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;



import java.util.List;
import java.util.Map;

/**
 * 题目控制器
 * 
 * <AUTHOR> Team
 * @since 2025-06-24
 */
@Tag(name = "题目管理", description = "题目相关接口")
@RestController
@RequestMapping("/api/problems")
@RequiredArgsConstructor
@Validated
public class ProblemController {

    private final ProblemService problemService;
    private final JwtUtils jwtUtils;

    @Operation(summary = "分页查询题目列表", description = "支持关键词搜索、难度筛选、创建者筛选的分页查询")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "400", description = "参数错误"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping
    public Result<IPage<Problem>> getProblems(
            @Parameter(description = "当前页码") @RequestParam(defaultValue = "1") @Min(1) Integer current,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") @Min(1) Integer size,
            @Parameter(description = "搜索关键词") @RequestParam(required = false) String keyword,
            @Parameter(description = "难度筛选") @RequestParam(required = false) String difficulty,
            @Parameter(description = "创建者ID") @RequestParam(required = false) Long creatorId) {
        
        IPage<Problem> page = problemService.getProblemsPage(current, size, keyword, difficulty, creatorId);
        return Result.success(page);
    }

    @Operation(summary = "根据ID获取题目详情", description = "获取指定题目的详细信息，包括题目描述、示例、约束等")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "404", description = "题目不存在"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/{id}")
    public Result<Problem> getProblemDetail(
            @Parameter(description = "题目ID", required = true) @PathVariable @NotNull Long id) {
        Problem problem = problemService.getProblemDetail(id);
        if (problem == null) {
            return Result.error("题目不存在");
        }
        return Result.success(problem);
    }

    @Operation(summary = "创建题目", description = "创建新的题目，需要用户登录认证")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "创建成功"),
        @ApiResponse(responseCode = "400", description = "参数验证失败"),
        @ApiResponse(responseCode = "401", description = "用户未登录"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping
    public Result<Problem> createProblem(
            @Parameter(description = "题目信息", required = true) @Valid @RequestBody Problem problem,
            HttpServletRequest request) {
//        Long userId = jwtUtils.getUserIdFromRequest(request);
//        if (userId == null) {
//            return Result.error("用户未登录");
//        }
        
        Problem createdProblem = problemService.createProblem(problem, 0L);
        return Result.success(createdProblem);
    }

    @Operation(summary = "更新题目")
    @PutMapping("/{id}")
    public Result<Problem> updateProblem(@PathVariable @NotNull Long id, 
                                       @Valid @RequestBody Problem problem, 
                                       HttpServletRequest request) {
        Long userId = jwtUtils.getUserIdFromRequest(request);
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            Problem updatedProblem = problemService.updateProblem(id, problem, userId);
            return Result.success(updatedProblem);
        } catch (RuntimeException e) {
            return Result.error(e.getMessage());
        }
    }

    @Operation(summary = "删除题目")
    @DeleteMapping("/{id}")
    public Result<Void> deleteProblem(@PathVariable @NotNull Long id, HttpServletRequest request) {
        Long userId = jwtUtils.getUserIdFromRequest(request);
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            boolean success = problemService.deleteProblem(id, userId);
            return success ? Result.success() : Result.error("删除失败");
        } catch (RuntimeException e) {
            return Result.error(e.getMessage());
        }
    }

    @Operation(summary = "发布题目")
    @PostMapping("/{id}/publish")
    public Result<Void> publishProblem(@PathVariable @NotNull Long id, HttpServletRequest request) {
        Long userId = jwtUtils.getUserIdFromRequest(request);
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            boolean success = problemService.publishProblem(id, userId);
            return success ? Result.success() : Result.error("发布失败");
        } catch (RuntimeException e) {
            return Result.error(e.getMessage());
        }
    }

    @Operation(summary = "归档题目")
    @PostMapping("/{id}/archive")
    public Result<Void> archiveProblem(@PathVariable @NotNull Long id, HttpServletRequest request) {
        Long userId = jwtUtils.getUserIdFromRequest(request);
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            boolean success = problemService.archiveProblem(id, userId);
            return success ? Result.success() : Result.error("归档失败");
        } catch (RuntimeException e) {
            return Result.error(e.getMessage());
        }
    }

    @Operation(summary = "搜索题目")
    @GetMapping("/search")
    public Result<List<Problem>> searchProblems(
            @Parameter(description = "搜索关键词") @RequestParam @NotNull String keyword) {
        List<Problem> problems = problemService.searchProblems(keyword);
        return Result.success(problems);
    }

    @Operation(summary = "根据难度查询题目")
    @GetMapping("/difficulty/{difficulty}")
    public Result<List<Problem>> getProblemsByDifficulty(
            @Parameter(description = "题目难度") @PathVariable @NotNull String difficulty) {
        List<Problem> problems = problemService.getProblemsByDifficulty(difficulty);
        return Result.success(problems);
    }

    @Operation(summary = "根据标签查询题目")
    @GetMapping("/tag/{tag}")
    public Result<List<Problem>> getProblemsByTag(
            @Parameter(description = "题目标签") @PathVariable @NotNull String tag) {
        List<Problem> problems = problemService.getProblemsByTag(tag);
        return Result.success(problems);
    }

    @Operation(summary = "获取热门题目")
    @GetMapping("/hot")
    public Result<List<Problem>> getHotProblems(
            @Parameter(description = "返回数量限制") @RequestParam(defaultValue = "10") @Min(1) Integer limit) {
        List<Problem> problems = problemService.getHotProblems(limit);
        return Result.success(problems);
    }

    @Operation(summary = "获取最新题目")
    @GetMapping("/latest")
    public Result<List<Problem>> getLatestProblems(@RequestParam(defaultValue = "10") @Min(1) Integer limit) {
        List<Problem> problems = problemService.getLatestProblems(limit);
        return Result.success(problems);
    }

    @Operation(summary = "点赞题目")
    @PostMapping("/{id}/like")
    public Result<Void> likeProblem(@PathVariable @NotNull Long id, HttpServletRequest request) {
        Long userId = jwtUtils.getUserIdFromRequest(request);
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        boolean success = problemService.likeProblem(id, userId);
        return success ? Result.success() : Result.error("点赞失败");
    }

    @Operation(summary = "取消点赞题目")
    @DeleteMapping("/{id}/like")
    public Result<Void> unlikeProblem(@PathVariable @NotNull Long id, HttpServletRequest request) {
        Long userId = jwtUtils.getUserIdFromRequest(request);
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        boolean success = problemService.unlikeProblem(id, userId);
        return success ? Result.success() : Result.error("取消点赞失败");
    }

    @Operation(summary = "收藏题目")
    @PostMapping("/{id}/favorite")
    public Result<Void> favoriteProblem(@PathVariable @NotNull Long id, HttpServletRequest request) {
        Long userId = jwtUtils.getUserIdFromRequest(request);
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        boolean success = problemService.favoriteProblem(id, userId);
        return success ? Result.success() : Result.error("收藏失败");
    }

    @Operation(summary = "取消收藏题目")
    @DeleteMapping("/{id}/favorite")
    public Result<Void> unfavoriteProblem(@PathVariable @NotNull Long id, HttpServletRequest request) {
        Long userId = jwtUtils.getUserIdFromRequest(request);
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        boolean success = problemService.unfavoriteProblem(id, userId);
        return success ? Result.success() : Result.error("取消收藏失败");
    }

    @Operation(summary = "提交题目解答")
    @PostMapping("/{id}/submit")
    public Result<Void> submitSolution(
            @Parameter(description = "题目ID") @PathVariable @NotNull Long id,
            @Parameter(description = "编程语言") @RequestParam @NotNull String language,
            @Parameter(description = "代码内容") @RequestParam @NotNull String code,
            HttpServletRequest request) {
        Long userId = jwtUtils.getUserIdFromRequest(request);
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        boolean success = problemService.submitSolution(id, userId, language, code);
        return success ? Result.success() : Result.error("提交失败");
    }

    @Operation(summary = "获取题目统计信息")
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getProblemStatistics() {
        Map<String, Object> statistics = problemService.getProblemStatistics();
        return Result.success(statistics);
    }

    @Operation(summary = "获取推荐题目")
    @GetMapping("/recommended")
    public Result<List<Problem>> getRecommendedProblems(@RequestParam(defaultValue = "10") @Min(1) Integer limit,
                                                       HttpServletRequest request) {
        Long userId = jwtUtils.getUserIdFromRequest(request);
        List<Problem> problems = problemService.getRecommendedProblems(userId, limit);
        return Result.success(problems);
    }

    @Operation(summary = "获取相似题目")
    @GetMapping("/{id}/similar")
    public Result<List<Problem>> getSimilarProblems(@PathVariable @NotNull Long id,
                                                   @RequestParam(defaultValue = "5") @Min(1) Integer limit) {
        List<Problem> problems = problemService.getSimilarProblems(id, limit);
        return Result.success(problems);
    }
}

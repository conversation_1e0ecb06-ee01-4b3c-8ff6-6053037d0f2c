package com.codecombined.dto.response;

import com.codecombined.enums.UserRole;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户详情响应DTO
 */
@Data
@Schema(description = "用户详情响应")
public class UserDetailResponse {

    @Schema(description = "用户ID", example = "1")
    private Long id;

    @Schema(description = "用户名", example = "testuser")
    private String username;

    @Schema(description = "邮箱", example = "<EMAIL>")
    private String email;

    @Schema(description = "昵称", example = "测试用户")
    private String nickname;

    @Schema(description = "用户角色", example = "USER")
    private UserRole role;

    @Schema(description = "用户状态：1-启用，0-禁用", example = "1")
    private Integer status;

    @Schema(description = "头像URL", example = "https://example.com/avatar.jpg")
    private String avatar;

    @Schema(description = "个人简介", example = "这是一个测试用户")
    private String bio;

    @Schema(description = "所在地", example = "北京市")
    private String location;

    @Schema(description = "公司", example = "某科技公司")
    private String company;

    @Schema(description = "GitHub链接", example = "https://github.com/username")
    private String github;

    @Schema(description = "个人网站", example = "https://example.com")
    private String website;

    @Schema(description = "积分", example = "100")
    private Integer points;

    @Schema(description = "是否邮箱已验证", example = "true")
    private Boolean emailVerified;

    @Schema(description = "创建时间", example = "2024-01-01 12:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "更新时间", example = "2024-01-01 12:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @Schema(description = "最后登录时间", example = "2024-01-01 12:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastLoginTime;

    @Schema(description = "最后登录IP", example = "***********")
    private String lastLoginIp;

    @Schema(description = "用户统计信息")
    private UserStatistics statistics;

    @Schema(description = "最近活动记录")
    private List<UserActivity> recentActivities;

    @Schema(description = "最近提交记录")
    private List<UserSubmission> recentSubmissions;

    /**
     * 用户统计信息
     */
    @Data
    @Schema(description = "用户统计信息")
    public static class UserStatistics {
        @Schema(description = "总提交数", example = "50")
        private Integer totalSubmissions;

        @Schema(description = "通过提交数", example = "30")
        private Integer acceptedSubmissions;

        @Schema(description = "解题数量", example = "25")
        private Integer solvedCount;

        @Schema(description = "创建题目数量", example = "5")
        private Integer createdProblemsCount;

        @Schema(description = "创建题集数量", example = "3")
        private Integer createdProblemSetsCount;

        @Schema(description = "登录天数", example = "30")
        private Integer loginDays;

        @Schema(description = "连续登录天数", example = "7")
        private Integer consecutiveLoginDays;
    }

    /**
     * 用户活动记录
     */
    @Data
    @Schema(description = "用户活动记录")
    public static class UserActivity {
        @Schema(description = "活动ID", example = "1")
        private Long id;

        @Schema(description = "活动类型", example = "LOGIN")
        private String type;

        @Schema(description = "活动描述", example = "用户登录")
        private String description;

        @Schema(description = "活动时间", example = "2024-01-01 12:00:00")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime createTime;

        @Schema(description = "IP地址", example = "***********")
        private String ipAddress;
    }

    /**
     * 用户提交记录
     */
    @Data
    @Schema(description = "用户提交记录")
    public static class UserSubmission {
        @Schema(description = "提交ID", example = "1")
        private Long id;

        @Schema(description = "题目ID", example = "1")
        private Long problemId;

        @Schema(description = "题目标题", example = "两数之和")
        private String problemTitle;

        @Schema(description = "提交状态", example = "ACCEPTED")
        private String status;

        @Schema(description = "编程语言", example = "Java")
        private String language;

        @Schema(description = "提交时间", example = "2024-01-01 12:00:00")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime submitTime;

        @Schema(description = "执行时间（毫秒）", example = "100")
        private Integer executeTime;

        @Schema(description = "内存使用（KB）", example = "1024")
        private Integer memoryUsage;
    }
}

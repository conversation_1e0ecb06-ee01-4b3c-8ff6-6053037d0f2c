package com.codecombined.dto.response;

import com.codecombined.enums.UserRole;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户列表响应DTO
 */
@Data
@Schema(description = "用户列表响应")
public class UserListResponse {

    @Schema(description = "用户ID", example = "1")
    private Long id;

    @Schema(description = "用户名", example = "testuser")
    private String username;

    @Schema(description = "邮箱", example = "<EMAIL>")
    private String email;

    @Schema(description = "昵称", example = "测试用户")
    private String nickname;

    @Schema(description = "用户角色", example = "USER")
    private UserRole role;

    @Schema(description = "用户状态：1-启用，0-禁用", example = "1")
    private Integer status;

    @Schema(description = "头像URL", example = "https://example.com/avatar.jpg")
    private String avatar;

    @Schema(description = "积分", example = "100")
    private Integer points;

    @Schema(description = "是否邮箱已验证", example = "true")
    private Boolean emailVerified;

    @Schema(description = "创建时间", example = "2024-01-01 12:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "最后登录时间", example = "2024-01-01 12:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastLoginTime;

    @Schema(description = "解题数量", example = "10")
    private Integer solvedCount;

    @Schema(description = "创建题目数量", example = "5")
    private Integer createdProblemsCount;

    @Schema(description = "创建题集数量", example = "3")
    private Integer createdProblemSetsCount;
}

package com.codecombined.dto.request;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class EmailVo {

    @Email
    @NotBlank(message = "邮箱不能为空")
    private String email;

    @NotBlank(message = "邮件类型不能为空")
    private String type;
}

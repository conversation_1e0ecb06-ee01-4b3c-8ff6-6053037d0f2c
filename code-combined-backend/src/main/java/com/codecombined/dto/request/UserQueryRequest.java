package com.codecombined.dto.request;

import com.codecombined.enums.UserRole;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 * 用户查询请求DTO
 */
@Data
@Builder
@Schema(description = "用户查询请求")
public class UserQueryRequest {

    @Schema(description = "搜索关键词（用户名、邮箱、昵称）", example = "test")
    private String keyword;

    @Schema(description = "用户角色", example = "USER")
    private UserRole role;

    @Schema(description = "用户状态：1-启用，0-禁用", example = "1")
    private Integer status;

    @Schema(description = "开始时间", example = "2024-01-01 00:00:00")
    private String startTime;

    @Schema(description = "结束时间", example = "2024-12-31 23:59:59")
    private String endTime;

    @Schema(description = "排序字段", example = "createTime")
    private String sortField;

    @Schema(description = "排序方向：asc-升序，desc-降序", example = "desc")
    private String sortOrder;
}

package com.codecombined.util;

import com.codecombined.enums.UserRole;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * JWT工具类
 * 用于生成和解析JWT Token
 */
@Component
@Slf4j
public class JwtUtil {

    /**
     * JWT密钥
     */
    @Value("${jwt.secret:codecombined-secret-key-for-jwt-token-generation}")
    private String secret;

    /**
     * JWT过期时间（毫秒）
     */
    @Value("${jwt.expiration:86400000}")
    private Long expiration;

    /**
     * JWT刷新时间（毫秒）
     */
    @Value("${jwt.refresh:604800000}")
    private Long refreshExpiration;

    /**
     * 生成JWT Token
     * 
     * @param userId 用户ID
     * @param username 用户名
     * @param role 用户角色
     * @return JWT Token
     */
    public String generateToken(Long userId, String username, UserRole role) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", userId);
        claims.put("username", username);
        claims.put("role", role.getCode());
        
        return createToken(claims, username);
    }

    /**
     * 生成JWT Token
     * 
     * @param userId 用户ID
     * @param username 用户名
     * @param role 用户角色
     * @param email 邮箱
     * @return JWT Token
     */
    public String generateToken(Long userId, String username, UserRole role, String email) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", userId);
        claims.put("username", username);
        claims.put("role", role.getCode());
        claims.put("email", email);
        
        return createToken(claims, username);
    }

    /**
     * 生成刷新Token
     * 
     * @param userId 用户ID
     * @param username 用户名
     * @return 刷新Token
     */
    public String generateRefreshToken(Long userId, String username) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", userId);
        claims.put("username", username);
        claims.put("type", "refresh");
        
        return createRefreshToken(claims, username);
    }

    /**
     * 创建Token
     * 
     * @param claims 声明
     * @param subject 主题
     * @return Token
     */
    private String createToken(Map<String, Object> claims, String subject) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + expiration);
        
        return Jwts.builder()
                .setClaims(claims)
                .setSubject(subject)
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .signWith(getSigningKey(), SignatureAlgorithm.HS512)
                .compact();
    }

    /**
     * 创建刷新Token
     * 
     * @param claims 声明
     * @param subject 主题
     * @return 刷新Token
     */
    private String createRefreshToken(Map<String, Object> claims, String subject) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + refreshExpiration);
        
        return Jwts.builder()
                .setClaims(claims)
                .setSubject(subject)
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .signWith(getSigningKey(), SignatureAlgorithm.HS512)
                .compact();
    }

    /**
     * 从Token中获取用户名
     * 
     * @param token JWT Token
     * @return 用户名
     */
    public String getUsernameFromToken(String token) {
        return getClaimsFromToken(token).getSubject();
    }

    /**
     * 从Token中获取用户ID
     * 
     * @param token JWT Token
     * @return 用户ID
     */
    public Long getUserIdFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        Object userId = claims.get("userId");
        if (userId instanceof Integer) {
            return ((Integer) userId).longValue();
        } else if (userId instanceof Long) {
            return (Long) userId;
        }
        return null;
    }

    /**
     * 从Token中获取用户角色
     * 
     * @param token JWT Token
     * @return 用户角色
     */
    public UserRole getUserRoleFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        String roleCode = (String) claims.get("role");
        try {
            return UserRole.fromCode(roleCode);
        } catch (Exception e) {
            log.warn("无法解析用户角色: {}", roleCode);
            return null;
        }
    }

    /**
     * 从Token中获取邮箱
     * 
     * @param token JWT Token
     * @return 邮箱
     */
    public String getEmailFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return (String) claims.get("email");
    }

    /**
     * 从Token中获取过期时间
     * 
     * @param token JWT Token
     * @return 过期时间
     */
    public Date getExpirationDateFromToken(String token) {
        return getClaimsFromToken(token).getExpiration();
    }

    /**
     * 从Token中获取声明
     * 
     * @param token JWT Token
     * @return 声明
     */
    private Claims getClaimsFromToken(String token) {
        return Jwts.parserBuilder()
                .setSigningKey(getSigningKey())
                .build()
                .parseClaimsJws(token)
                .getBody();
    }

    /**
     * 检查Token是否过期
     * 
     * @param token JWT Token
     * @return true表示过期，false表示未过期
     */
    public Boolean isTokenExpired(String token) {
        try {
            Date expiration = getExpirationDateFromToken(token);
            return expiration.before(new Date());
        } catch (Exception e) {
            log.warn("检查Token过期时间失败: {}", e.getMessage());
            return true;
        }
    }

    /**
     * 验证Token
     * 
     * @param token JWT Token
     * @param username 用户名
     * @return true表示有效，false表示无效
     */
    public Boolean validateToken(String token, String username) {
        try {
            String tokenUsername = getUsernameFromToken(token);
            return (username.equals(tokenUsername) && !isTokenExpired(token));
        } catch (Exception e) {
            log.warn("验证Token失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 验证Token（不检查用户名）
     * 
     * @param token JWT Token
     * @return true表示有效，false表示无效
     */
    public Boolean validateToken(String token) {
        try {
            getClaimsFromToken(token);
            return !isTokenExpired(token);
        } catch (Exception e) {
            log.warn("验证Token失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 刷新Token
     * 
     * @param token 原Token
     * @return 新Token
     */
    public String refreshToken(String token) {
        try {
            Claims claims = getClaimsFromToken(token);
            
            Long userId = getUserIdFromToken(token);
            String username = getUsernameFromToken(token);
            UserRole role = getUserRoleFromToken(token);
            String email = getEmailFromToken(token);
            
            if (email != null) {
                return generateToken(userId, username, role, email);
            } else {
                return generateToken(userId, username, role);
            }
        } catch (Exception e) {
            log.error("刷新Token失败: {}", e.getMessage());
            throw new RuntimeException("Token刷新失败");
        }
    }

    /**
     * 检查Token是否需要刷新
     * 
     * @param token JWT Token
     * @return true表示需要刷新，false表示不需要
     */
    public Boolean shouldRefreshToken(String token) {
        try {
            Date expirationDate = getExpirationDateFromToken(token);
            Date now = new Date();
            
            // 如果Token在30分钟内过期，则需要刷新
            long refreshThreshold = 30 * 60 * 1000; // 30分钟
            return (expirationDate.getTime() - now.getTime()) < refreshThreshold;
        } catch (Exception e) {
            log.warn("检查Token刷新状态失败: {}", e.getMessage());
            return true;
        }
    }

    /**
     * 从Token中提取用户上下文信息
     * 
     * @param token JWT Token
     * @return 用户上下文信息
     */
    public UserContext.UserInfo extractUserInfo(String token) {
        try {
            Long userId = getUserIdFromToken(token);
            String username = getUsernameFromToken(token);
            UserRole role = getUserRoleFromToken(token);
            String email = getEmailFromToken(token);
            
            UserContext.UserInfo userInfo = new UserContext.UserInfo(userId, username, role, email);
            userInfo.setToken(token);
            
            return userInfo;
        } catch (Exception e) {
            log.error("提取用户信息失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 获取签名密钥
     * 
     * @return 签名密钥
     */
    private SecretKey getSigningKey() {
        byte[] keyBytes = secret.getBytes();
        return Keys.hmacShaKeyFor(keyBytes);
    }

    /**
     * 获取Token剩余有效时间（秒）
     * 
     * @param token JWT Token
     * @return 剩余有效时间（秒），如果Token无效则返回0
     */
    public long getTokenRemainingTime(String token) {
        try {
            Date expirationDate = getExpirationDateFromToken(token);
            Date now = new Date();
            
            long remainingTime = (expirationDate.getTime() - now.getTime()) / 1000;
            return Math.max(0, remainingTime);
        } catch (Exception e) {
            log.warn("获取Token剩余时间失败: {}", e.getMessage());
            return 0;
        }
    }

    /**
     * 检查Token是否是刷新Token
     * 
     * @param token JWT Token
     * @return true表示是刷新Token，false表示不是
     */
    public Boolean isRefreshToken(String token) {
        try {
            Claims claims = getClaimsFromToken(token);
            String type = (String) claims.get("type");
            return "refresh".equals(type);
        } catch (Exception e) {
            log.warn("检查刷新Token失败: {}", e.getMessage());
            return false;
        }
    }
}

package com.codecombined.util;

import com.codecombined.enums.UserRole;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 用户上下文工具类
 * 使用ThreadLocal存储当前线程的用户信息
 */
@Slf4j
public class UserContext {

    /**
     * 用户信息ThreadLocal
     */
    private static final ThreadLocal<UserInfo> USER_THREAD_LOCAL = new ThreadLocal<>();

    /**
     * 用户信息内部类
     */
    @Data
    public static class UserInfo {
        /**
         * 用户ID
         */
        private Long userId;
        
        /**
         * 用户名
         */
        private String username;
        
        /**
         * 用户角色
         */
        private UserRole role;
        
        /**
         * 邮箱
         */
        private String email;
        
        /**
         * 昵称
         */
        private String nickname;
        
        /**
         * 用户状态
         */
        private Integer status;
        
        /**
         * JWT Token
         */
        private String token;
        
        /**
         * 客户端IP地址
         */
        private String ipAddress;
        
        /**
         * 用户代理
         */
        private String userAgent;
        
        /**
         * 请求时间
         */
        private Long requestTime;

        public UserInfo() {
            this.requestTime = System.currentTimeMillis();
        }

        public UserInfo(Long userId, String username, UserRole role) {
            this();
            this.userId = userId;
            this.username = username;
            this.role = role;
        }

        public UserInfo(Long userId, String username, UserRole role, String email) {
            this(userId, username, role);
            this.email = email;
        }
    }

    /**
     * 设置当前用户信息
     * 
     * @param userInfo 用户信息
     */
    public static void setCurrentUser(UserInfo userInfo) {
        if (userInfo != null) {
            log.debug("设置用户上下文: userId={}, username={}, role={}", 
                    userInfo.getUserId(), userInfo.getUsername(), userInfo.getRole());
        }
        USER_THREAD_LOCAL.set(userInfo);
    }

    /**
     * 设置当前用户信息
     * 
     * @param userId 用户ID
     * @param username 用户名
     * @param role 用户角色
     */
    public static void setCurrentUser(Long userId, String username, UserRole role) {
        setCurrentUser(new UserInfo(userId, username, role));
    }

    /**
     * 设置当前用户信息
     * 
     * @param userId 用户ID
     * @param username 用户名
     * @param role 用户角色
     * @param email 邮箱
     */
    public static void setCurrentUser(Long userId, String username, UserRole role, String email) {
        setCurrentUser(new UserInfo(userId, username, role, email));
    }

    /**
     * 获取当前用户信息
     * 
     * @return 用户信息，如果未设置则返回null
     */
    public static UserInfo getCurrentUser() {
        return USER_THREAD_LOCAL.get();
    }

    /**
     * 获取当前用户ID
     * 
     * @return 用户ID，如果未设置则返回null
     */
    public static Long getCurrentUserId() {
        UserInfo userInfo = getCurrentUser();
        return userInfo != null ? userInfo.getUserId() : null;
    }

    /**
     * 获取当前用户名
     * 
     * @return 用户名，如果未设置则返回null
     */
    public static String getCurrentUsername() {
        UserInfo userInfo = getCurrentUser();
        return userInfo != null ? userInfo.getUsername() : null;
    }

    /**
     * 获取当前用户角色
     * 
     * @return 用户角色，如果未设置则返回null
     */
    public static UserRole getCurrentUserRole() {
        UserInfo userInfo = getCurrentUser();
        return userInfo != null ? userInfo.getRole() : null;
    }

    /**
     * 获取当前用户邮箱
     * 
     * @return 邮箱，如果未设置则返回null
     */
    public static String getCurrentUserEmail() {
        UserInfo userInfo = getCurrentUser();
        return userInfo != null ? userInfo.getEmail() : null;
    }

    /**
     * 获取当前用户昵称
     * 
     * @return 昵称，如果未设置则返回null
     */
    public static String getCurrentUserNickname() {
        UserInfo userInfo = getCurrentUser();
        return userInfo != null ? userInfo.getNickname() : null;
    }

    /**
     * 获取当前用户状态
     * 
     * @return 用户状态，如果未设置则返回null
     */
    public static Integer getCurrentUserStatus() {
        UserInfo userInfo = getCurrentUser();
        return userInfo != null ? userInfo.getStatus() : null;
    }

    /**
     * 获取当前用户Token
     * 
     * @return Token，如果未设置则返回null
     */
    public static String getCurrentUserToken() {
        UserInfo userInfo = getCurrentUser();
        return userInfo != null ? userInfo.getToken() : null;
    }

    /**
     * 获取当前用户IP地址
     * 
     * @return IP地址，如果未设置则返回null
     */
    public static String getCurrentUserIpAddress() {
        UserInfo userInfo = getCurrentUser();
        return userInfo != null ? userInfo.getIpAddress() : null;
    }

    /**
     * 检查当前用户是否已登录
     * 
     * @return true表示已登录，false表示未登录
     */
    public static boolean isLoggedIn() {
        return getCurrentUserId() != null;
    }

    /**
     * 检查当前用户是否是管理员
     * 
     * @return true表示是管理员，false表示不是
     */
    public static boolean isAdmin() {
        UserRole role = getCurrentUserRole();
        return role != null && role.isAdmin();
    }

    /**
     * 检查当前用户是否是普通用户
     * 
     * @return true表示是普通用户，false表示不是
     */
    public static boolean isUser() {
        UserRole role = getCurrentUserRole();
        return role != null && role.isUser();
    }

    /**
     * 检查当前用户是否有指定角色
     * 
     * @param targetRole 目标角色
     * @return true表示有该角色，false表示没有
     */
    public static boolean hasRole(UserRole targetRole) {
        UserRole currentRole = getCurrentUserRole();
        return currentRole != null && currentRole == targetRole;
    }

    /**
     * 检查当前用户是否有权限访问指定角色的资源
     * 
     * @param targetRole 目标角色
     * @return true表示有权限，false表示没有权限
     */
    public static boolean hasPermission(UserRole targetRole) {
        UserRole currentRole = getCurrentUserRole();
        return currentRole != null && currentRole.hasPermission(targetRole);
    }

    /**
     * 检查当前用户是否是指定用户
     * 
     * @param userId 用户ID
     * @return true表示是指定用户，false表示不是
     */
    public static boolean isCurrentUser(Long userId) {
        Long currentUserId = getCurrentUserId();
        return currentUserId != null && currentUserId.equals(userId);
    }

    /**
     * 检查当前用户是否可以访问指定用户的资源
     * 
     * @param targetUserId 目标用户ID
     * @return true表示可以访问，false表示不能访问
     */
    public static boolean canAccessUser(Long targetUserId) {
        // 管理员可以访问所有用户
        if (isAdmin()) {
            return true;
        }
        
        // 用户只能访问自己的资源
        return isCurrentUser(targetUserId);
    }

    /**
     * 更新当前用户的Token
     * 
     * @param token 新的Token
     */
    public static void updateToken(String token) {
        UserInfo userInfo = getCurrentUser();
        if (userInfo != null) {
            userInfo.setToken(token);
            log.debug("更新用户Token: userId={}", userInfo.getUserId());
        }
    }

    /**
     * 更新当前用户的IP地址
     * 
     * @param ipAddress IP地址
     */
    public static void updateIpAddress(String ipAddress) {
        UserInfo userInfo = getCurrentUser();
        if (userInfo != null) {
            userInfo.setIpAddress(ipAddress);
        }
    }

    /**
     * 更新当前用户的用户代理
     * 
     * @param userAgent 用户代理
     */
    public static void updateUserAgent(String userAgent) {
        UserInfo userInfo = getCurrentUser();
        if (userInfo != null) {
            userInfo.setUserAgent(userAgent);
        }
    }

    /**
     * 清除当前用户信息
     */
    public static void clear() {
        UserInfo userInfo = getCurrentUser();
        if (userInfo != null) {
            log.debug("清除用户上下文: userId={}, username={}", 
                    userInfo.getUserId(), userInfo.getUsername());
        }
        USER_THREAD_LOCAL.remove();
    }

    /**
     * 获取当前用户的请求持续时间（毫秒）
     * 
     * @return 请求持续时间，如果未设置则返回0
     */
    public static long getRequestDuration() {
        UserInfo userInfo = getCurrentUser();
        if (userInfo != null && userInfo.getRequestTime() != null) {
            return System.currentTimeMillis() - userInfo.getRequestTime();
        }
        return 0;
    }

    /**
     * 获取用户上下文的字符串表示（用于日志）
     * 
     * @return 用户上下文字符串
     */
    public static String getContextString() {
        UserInfo userInfo = getCurrentUser();
        if (userInfo == null) {
            return "未登录用户";
        }
        
        return String.format("用户[ID=%d, 用户名=%s, 角色=%s, IP=%s]", 
                userInfo.getUserId(), 
                userInfo.getUsername(), 
                userInfo.getRole(), 
                userInfo.getIpAddress());
    }

    /**
     * 复制当前用户上下文到新线程
     * 用于异步操作时传递用户上下文
     * 
     * @return 当前用户信息的副本
     */
    public static UserInfo copyCurrentUser() {
        UserInfo current = getCurrentUser();
        if (current == null) {
            return null;
        }
        
        UserInfo copy = new UserInfo();
        copy.setUserId(current.getUserId());
        copy.setUsername(current.getUsername());
        copy.setRole(current.getRole());
        copy.setEmail(current.getEmail());
        copy.setNickname(current.getNickname());
        copy.setStatus(current.getStatus());
        copy.setToken(current.getToken());
        copy.setIpAddress(current.getIpAddress());
        copy.setUserAgent(current.getUserAgent());
        copy.setRequestTime(current.getRequestTime());
        
        return copy;
    }

    /**
     * 在新线程中设置用户上下文
     * 用于异步操作时恢复用户上下文
     * 
     * @param userInfo 用户信息
     * @param task 要执行的任务
     */
    public static void runWithUser(UserInfo userInfo, Runnable task) {
        UserInfo originalUser = getCurrentUser();
        try {
            setCurrentUser(userInfo);
            task.run();
        } finally {
            if (originalUser != null) {
                setCurrentUser(originalUser);
            } else {
                clear();
            }
        }
    }
}

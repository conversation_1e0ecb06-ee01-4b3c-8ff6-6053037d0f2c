package com.codecombined.util;

import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.crypto.SecretKey;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;


/**
 * IP工具类
 * 
 * <AUTHOR> Team
 * @since 2025-06-22
 */
public class IpUtil {

    private static final String UNKNOWN = "unknown";
    private static final String LOCALHOST_IPV4 = "127.0.0.1";
    private static final String LOCALHOST_IPV6 = "0:0:0:0:0:0:0:1";

    /**
     * 获取客户端真实IP地址
     */
    public static String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        
        if (!isValidIp(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        
        if (!isValidIp(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        
        if (!isValidIp(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        
        if (!isValidIp(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        
        if (!isValidIp(ip)) {
            ip = request.getHeader("X-Real-IP");
        }
        
        if (!isValidIp(ip)) {
            ip = request.getRemoteAddr();
        }

        // 处理多个IP的情况，取第一个有效IP
        if (StringUtils.hasText(ip) && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }

        // 处理本地回环地址
        if (LOCALHOST_IPV6.equals(ip)) {
            ip = LOCALHOST_IPV4;
        }

        return ip;
    }

    /**
     * 验证IP是否有效
     */
    private static boolean isValidIp(String ip) {
        return StringUtils.hasText(ip) && !UNKNOWN.equalsIgnoreCase(ip);
    }

    /**
     * 判断是否为内网IP
     */
    public static boolean isInternalIp(String ip) {
        if (!StringUtils.hasText(ip)) {
            return false;
        }

        String[] parts = ip.split("\\.");
        if (parts.length != 4) {
            return false;
        }

        try {
            int first = Integer.parseInt(parts[0]);
            int second = Integer.parseInt(parts[1]);

            // 10.0.0.0 - **************
            if (first == 10) {
                return true;
            }

            // ********** - **************
            if (first == 172 && second >= 16 && second <= 31) {
                return true;
            }

            // *********** - ***************
            if (first == 192 && second == 168) {
                return true;
            }

            // ********* - ***************
            if (first == 127) {
                return true;
            }

            return false;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * JWT工具类
     *
     * <AUTHOR> Team
     * @since 2025-06-22
     */
    @Slf4j
    @Component
    public static class JwtUtil {

        @Value("${jwt.secret}")
        private String secret;

        @Value("${jwt.expiration}")
        private Long expiration;

        /**
         * 生成JWT Token
         */
        public String generateToken(String username, Long userId, String role) {
            Map<String, Object> claims = new HashMap<>();
            claims.put("userId", userId);
            claims.put("role", role);
            return createToken(claims, username);
        }

        /**
         * 创建Token
         */
        private String createToken(Map<String, Object> claims, String subject) {
            Date now = new Date();
            Date expiryDate = new Date(now.getTime() + expiration);

            return Jwts.builder()
                    .setClaims(claims)
                    .setSubject(subject)
                    .setIssuedAt(now)
                    .setExpiration(expiryDate)
                    .signWith(getSigningKey(), SignatureAlgorithm.HS512)
                    .compact();
        }

        /**
         * 从Token中获取用户名
         */
        public String getUsernameFromToken(String token) {
            return getClaimFromToken(token, Claims::getSubject);
        }

        /**
         * 从Token中获取用户ID
         */
        public Long getUserIdFromToken(String token) {
            Claims claims = getAllClaimsFromToken(token);
            return claims.get("userId", Long.class);
        }

        /**
         * 从Token中获取用户角色
         */
        public String getRoleFromToken(String token) {
            Claims claims = getAllClaimsFromToken(token);
            return claims.get("role", String.class);
        }

        /**
         * 从Token中获取过期时间
         */
        public Date getExpirationDateFromToken(String token) {
            return getClaimFromToken(token, Claims::getExpiration);
        }

        /**
         * 从Token中获取指定声明
         */
        public <T> T getClaimFromToken(String token, java.util.function.Function<Claims, T> claimsResolver) {
            final Claims claims = getAllClaimsFromToken(token);
            return claimsResolver.apply(claims);
        }

        /**
         * 从Token中获取所有声明
         */
        private Claims getAllClaimsFromToken(String token) {
            try {
                return Jwts.parserBuilder()
                        .setSigningKey(getSigningKey())
                        .build()
                        .parseClaimsJws(token)
                        .getBody();
            } catch (ExpiredJwtException e) {
                log.warn("JWT Token已过期: {}", e.getMessage());
                throw e;
            } catch (UnsupportedJwtException e) {
                log.warn("不支持的JWT Token: {}", e.getMessage());
                throw e;
            } catch (MalformedJwtException e) {
                log.warn("JWT Token格式错误: {}", e.getMessage());
                throw e;
            } catch (SecurityException e) {
                log.warn("JWT Token签名验证失败: {}", e.getMessage());
                throw e;
            } catch (IllegalArgumentException e) {
                log.warn("JWT Token参数错误: {}", e.getMessage());
                throw e;
            }
        }

        /**
         * 检查Token是否过期
         */
        public Boolean isTokenExpired(String token) {
            try {
                final Date expiration = getExpirationDateFromToken(token);
                return expiration.before(new Date());
            } catch (ExpiredJwtException e) {
                return true;
            }
        }

        /**
         * 验证Token
         */
        public Boolean validateToken(String token, String username) {
            try {
                final String tokenUsername = getUsernameFromToken(token);
                return (username.equals(tokenUsername) && !isTokenExpired(token));
            } catch (Exception e) {
                log.warn("Token验证失败: {}", e.getMessage());
                return false;
            }
        }

        /**
         * 获取签名密钥
         */
        private SecretKey getSigningKey() {
            byte[] keyBytes = secret.getBytes();
            return Keys.hmacShaKeyFor(keyBytes);
        }
    }
}

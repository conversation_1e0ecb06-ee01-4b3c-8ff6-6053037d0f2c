package com.codecombined.security;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.security.Principal;

/**
 * 用户主体信息
 * 用于在Spring Security上下文中存储用户信息
 * 
 * <AUTHOR> Team
 * @since 2025-06-28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserPrincipal implements Principal {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 用户角色
     */
    private String role;
    
    /**
     * 邮箱
     */
    private String email;
    
    /**
     * 构造函数（不包含邮箱）
     */
    public UserPrincipal(Long userId, String username, String role) {
        this.userId = userId;
        this.username = username;
        this.role = role;
    }
    
    @Override
    public String getName() {
        return username;
    }
    
    /**
     * 检查是否为管理员
     */
    public boolean isAdmin() {
        return "ADMIN".equals(role);
    }
    
    /**
     * 检查是否为普通用户
     */
    public boolean isUser() {
        return "USER".equals(role);
    }
    
    /**
     * 获取带ROLE_前缀的角色名
     */
    public String getRoleWithPrefix() {
        return role.startsWith("ROLE_") ? role : "ROLE_" + role;
    }
}

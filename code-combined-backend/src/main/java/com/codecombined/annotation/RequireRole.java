package com.codecombined.annotation;

import com.codecombined.enums.UserRole;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 角色权限注解
 * 用于标记需要特定角色才能访问的接口
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface RequireRole {
    
    /**
     * 需要的角色
     */
    UserRole value() default UserRole.USER;
    
    /**
     * 是否允许更高级别的角色访问
     * 例如：标记为USER角色，如果allowHigher=true，则ADMIN角色也可以访问
     */
    boolean allowHigher() default true;
}

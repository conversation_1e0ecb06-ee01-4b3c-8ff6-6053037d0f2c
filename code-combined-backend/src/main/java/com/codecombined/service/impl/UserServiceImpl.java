package com.codecombined.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.codecombined.dto.LoginRequest;
import com.codecombined.dto.RegisterRequest;
import com.codecombined.dto.request.UserUpdateRequest;
import com.codecombined.dto.request.UserQueryRequest;
import com.codecombined.dto.response.UserListResponse;
import com.codecombined.dto.response.UserDetailResponse;
import com.codecombined.entity.User;
import com.codecombined.mapper.UserMapper;
import com.codecombined.service.EmailService;
import com.codecombined.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 * 用户服务实现类
 * 
 * <AUTHOR> Team
 * @since 2025-06-22
 */
@Slf4j
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private PasswordEncoder passwordEncoder;



    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private EmailService emailService;

    private static final String EMAIL_CODE_PREFIX = "email_code:";
    private static final int EMAIL_CODE_EXPIRE_MINUTES = 5;

    @Override
    public void register(RegisterRequest request) {
        // 验证密码确认
        if (!request.getPassword().equals(request.getConfirmPassword())) {
            throw new RuntimeException("两次输入的密码不一致");
        }

        // 验证邮箱验证码
        String cacheKey = EMAIL_CODE_PREFIX + request.getEmail();
        String cachedCode = redisTemplate.opsForValue().get(cacheKey);
        if (!StringUtils.hasText(cachedCode) || !cachedCode.equals(request.getEmailCode())) {
            throw new RuntimeException("邮箱验证码错误或已过期");
        }

        // 检查用户名是否已存在
        if (findByUsername(request.getUsername()) != null) {
            throw new RuntimeException("用户名已存在");
        }

        // 检查邮箱是否已存在
        if (findByEmail(request.getEmail()) != null) {
            throw new RuntimeException("邮箱已被注册");
        }

        // 创建用户
        User user = new User();
        user.setUsername(request.getUsername());
        user.setEmail(request.getEmail());
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setNickname(StringUtils.hasText(request.getNickname()) ? request.getNickname() : request.getUsername());
        user.setRole(User.Role.USER.name());
        user.setStatus(User.Status.ENABLED.getValue());
        user.setEmailVerified(1); // 注册时已验证邮箱

        save(user);

        // 删除验证码
        redisTemplate.delete(cacheKey);

        log.info("用户注册成功: {}", request.getUsername());
    }

    @Override
    public String login(LoginRequest request) {
        // 无认证模式：直接验证用户凭据
        User user = authenticate(request.getEmail(), request.getPassword());
        if (user == null) {
            throw new RuntimeException("邮箱或密码错误");
        }

        log.info("用户登录成功: {}", user.getUsername());
        return "no-auth-mode"; // 返回标识，表示无认证模式
    }

    @Override
    public User authenticate(String email, String password) {
        // 查找用户
        User user = findByEmail(email);
        if (user == null) {
            return null;
        }

        // 检查用户状态
        if (user.getStatus() != User.Status.ENABLED.getValue()) {
            return null;
        }

        // 验证密码
        if (!passwordEncoder.matches(password, user.getPassword())) {
            return null;
        }

        return user;
    }

    @Override
    public void sendEmailCode(String email) {
        // 生成6位数字验证码
        String code = String.valueOf((int) ((Math.random() * 9 + 1) * 100000));

        // 存储到Redis，5分钟过期
        String cacheKey = EMAIL_CODE_PREFIX + email;
        redisTemplate.opsForValue().set(cacheKey, code, EMAIL_CODE_EXPIRE_MINUTES, TimeUnit.MINUTES);

        // 发送邮件
        String subject = "Code-Combined 邮箱验证码";
        String content = String.format(
            "您的验证码是：<b>%s</b><br/>验证码有效期为%d分钟，请及时使用。<br/>如果这不是您的操作，请忽略此邮件。",
            code, EMAIL_CODE_EXPIRE_MINUTES
        );
        log.info("发送邮件: {}, 验证码: {}", email, code);
        //emailService.sendHtmlEmail(email, subject, content);

        log.info("邮箱验证码已发送: {}", email);
    }

    @Override
    public User findByEmail(String email) {
        return userMapper.findByEmail(email);
    }

    @Override
    public User findByUsername(String username) {
        return userMapper.findByUsername(username);
    }

    @Override
    public void updateLastLogin(Long userId, String ip) {
        User user = new User();
        user.setId(userId);
        user.setLastLoginTime(LocalDateTime.now());
        user.setLastLoginIp(ip);
        updateById(user);
    }

    @Override
    public Long countTotalUsers() {
        return userMapper.countTotalUsers();
    }

    @Override
    public Long countActiveUsers() {
        return userMapper.countActiveUsers();
    }

    // ==================== 管理员专用方法实现 ====================

    @Override
    public IPage<UserListResponse> getUserListForAdmin(Page<User> page, UserQueryRequest queryRequest) {
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();

        // 搜索条件
        if (StringUtils.hasText(queryRequest.getKeyword())) {
            String keyword = queryRequest.getKeyword();
            queryWrapper.and(wrapper -> wrapper
                .like("username", keyword)
                .or().like("email", keyword)
                .or().like("nickname", keyword)
            );
        }

        // 角色筛选
        if (queryRequest.getRole() != null) {
            queryWrapper.eq("role", queryRequest.getRole().name());
        }

        // 状态筛选
        if (queryRequest.getStatus() != null) {
            queryWrapper.eq("status", queryRequest.getStatus());
        }

        // 时间范围筛选
        if (StringUtils.hasText(queryRequest.getStartTime())) {
            queryWrapper.ge("create_time", queryRequest.getStartTime());
        }
        if (StringUtils.hasText(queryRequest.getEndTime())) {
            queryWrapper.le("create_time", queryRequest.getEndTime());
        }

        // 排序
        String sortField = StringUtils.hasText(queryRequest.getSortField()) ? queryRequest.getSortField() : "create_time";
        boolean isAsc = "asc".equalsIgnoreCase(queryRequest.getSortOrder());
        queryWrapper.orderBy(true, isAsc, sortField);

        // 分页查询
        IPage<User> userPage = page(page, queryWrapper);

        // 转换为响应DTO
        IPage<UserListResponse> responsePage = userPage.convert(user -> {
            UserListResponse response = new UserListResponse();
            BeanUtils.copyProperties(user, response);

            // 设置统计数据（这里使用模拟数据，实际应该查询相关表）
            response.setSolvedCount((int) (Math.random() * 50));
            response.setCreatedProblemsCount((int) (Math.random() * 10));
            response.setCreatedProblemSetsCount((int) (Math.random() * 5));

            return response;
        });

        return responsePage;
    }

    @Override
    public UserDetailResponse getUserDetailForAdmin(Long userId) {
        User user = getById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        UserDetailResponse response = new UserDetailResponse();
        BeanUtils.copyProperties(user, response);

        // 设置统计信息（模拟数据）
        UserDetailResponse.UserStatistics statistics = new UserDetailResponse.UserStatistics();
        statistics.setTotalSubmissions((int) (Math.random() * 100));
        statistics.setAcceptedSubmissions((int) (Math.random() * 60));
        statistics.setSolvedCount((int) (Math.random() * 50));
        statistics.setCreatedProblemsCount((int) (Math.random() * 10));
        statistics.setCreatedProblemSetsCount((int) (Math.random() * 5));
        statistics.setLoginDays((int) (Math.random() * 100));
        statistics.setConsecutiveLoginDays((int) (Math.random() * 30));
        response.setStatistics(statistics);

        // 设置最近活动（模拟数据）
        List<UserDetailResponse.UserActivity> activities = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            UserDetailResponse.UserActivity activity = new UserDetailResponse.UserActivity();
            activity.setId((long) (i + 1));
            activity.setType("LOGIN");
            activity.setDescription("用户登录");
            activity.setCreateTime(LocalDateTime.now().minusHours(i));
            activity.setIpAddress("192.168.1." + (i + 1));
            activities.add(activity);
        }
        response.setRecentActivities(activities);

        // 设置最近提交（模拟数据）
        List<UserDetailResponse.UserSubmission> submissions = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            UserDetailResponse.UserSubmission submission = new UserDetailResponse.UserSubmission();
            submission.setId((long) (i + 1));
            submission.setProblemId((long) (i + 1));
            submission.setProblemTitle("题目 " + (i + 1));
            submission.setStatus(i % 2 == 0 ? "ACCEPTED" : "WRONG_ANSWER");
            submission.setLanguage("Java");
            submission.setSubmitTime(LocalDateTime.now().minusHours(i));
            submission.setExecuteTime(100 + i * 10);
            submission.setMemoryUsage(1024 + i * 100);
            submissions.add(submission);
        }
        response.setRecentSubmissions(submissions);

        return response;
    }

    @Override
    public Long createUserByAdmin(UserCreateRequest request) {
        // 检查用户名是否已存在
        if (findByUsername(request.getUsername()) != null) {
            throw new RuntimeException("用户名已存在");
        }

        // 检查邮箱是否已存在
        if (findByEmail(request.getEmail()) != null) {
            throw new RuntimeException("邮箱已被注册");
        }

        // 创建用户
        User user = new User();
        BeanUtils.copyProperties(request, user);
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setRole(request.getRole().name());
        user.setEmailVerified(request.getRequireEmailVerification() != null && !request.getRequireEmailVerification() ? 1 : 0);

        // 设置默认值
        if (user.getPoints() == null) {
            user.setPoints(request.getPoints() != null ? request.getPoints() : 100);
        }

        save(user);

        log.info("管理员创建用户成功: {}", request.getUsername());
        return user.getId();
    }

    @Override
    public void updateUserByAdmin(Long userId, UserUpdateRequest request) {
        User user = getById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 检查用户名是否已被其他用户使用
        if (StringUtils.hasText(request.getUsername()) && !request.getUsername().equals(user.getUsername())) {
            User existingUser = findByUsername(request.getUsername());
            if (existingUser != null && !existingUser.getId().equals(userId)) {
                throw new RuntimeException("用户名已存在");
            }
        }

        // 检查邮箱是否已被其他用户使用
        if (StringUtils.hasText(request.getEmail()) && !request.getEmail().equals(user.getEmail())) {
            User existingUser = findByEmail(request.getEmail());
            if (existingUser != null && !existingUser.getId().equals(userId)) {
                throw new RuntimeException("邮箱已被注册");
            }
        }

        // 更新用户信息
        BeanUtils.copyProperties(request, user, "id", "password", "createTime");
        if (request.getRole() != null) {
            user.setRole(request.getRole().name());
        }
        if (request.getEmailVerified() != null) {
            user.setEmailVerified(request.getEmailVerified() ? 1 : 0);
        }

        updateById(user);

        log.info("管理员更新用户成功: userId={}", userId);
    }

    @Override
    public void deleteUserByAdmin(Long userId) {
        User user = getById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 软删除：设置删除标记
        user.setStatus(0); // 0表示已删除
        updateById(user);

        log.info("管理员删除用户成功: userId={}", userId);
    }

    @Override
    public void batchDeleteUsersByAdmin(List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return;
        }

        List<User> users = listByIds(userIds);
        users.forEach(user -> user.setStatus(0));
        updateBatchById(users);

        log.info("管理员批量删除用户成功: userIds={}", userIds);
    }

    @Override
    public void toggleUserStatusByAdmin(Long userId, Integer status) {
        User user = getById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        user.setStatus(status);
        updateById(user);

        log.info("管理员切换用户状态成功: userId={}, status={}", userId, status);
    }

    @Override
    public void batchToggleUserStatusByAdmin(List<Long> userIds, Integer status) {
        if (userIds == null || userIds.isEmpty()) {
            return;
        }

        List<User> users = listByIds(userIds);
        users.forEach(user -> user.setStatus(status));
        updateBatchById(users);

        log.info("管理员批量切换用户状态成功: userIds={}, status={}", userIds, status);
    }

    @Override
    public String resetUserPasswordByAdmin(Long userId) {
        User user = getById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 生成随机密码
        String newPassword = generateRandomPassword();
        user.setPassword(passwordEncoder.encode(newPassword));
        updateById(user);

        log.info("管理员重置用户密码成功: userId={}", userId);
        return newPassword;
    }

    @Override
    public Object getUserStatisticsForAdmin() {
        Map<String, Object> statistics = new HashMap<>();

        // 用户总数
        statistics.put("totalUsers", count());

        // 活跃用户数（最近30天登录）
        statistics.put("activeUsers", countActiveUsers());

        // 按角色统计
        Map<String, Long> roleStats = new HashMap<>();
        roleStats.put("admin", lambdaQuery().eq(User::getRole, "ADMIN").count());
        roleStats.put("user", lambdaQuery().eq(User::getRole, "USER").count());
        statistics.put("roleStatistics", roleStats);

        // 按状态统计
        Map<String, Long> statusStats = new HashMap<>();
        statusStats.put("enabled", lambdaQuery().eq(User::getStatus, 1).count());
        statusStats.put("disabled", lambdaQuery().eq(User::getStatus, 0).count());
        statistics.put("statusStatistics", statusStats);

        // 最近7天注册用户数
        LocalDateTime sevenDaysAgo = LocalDateTime.now().minusDays(7);
        Long recentRegistrations = lambdaQuery()
            .ge(User::getCreateTime, sevenDaysAgo)
            .count();
        statistics.put("recentRegistrations", recentRegistrations);

        return statistics;
    }

    @Override
    public String exportUsersForAdmin(UserQueryRequest queryRequest) {
        // 这里应该实现Excel导出逻辑
        // 为了简化，返回一个模拟的下载链接
        String fileName = "users_export_" + System.currentTimeMillis() + ".xlsx";
        String downloadUrl = "/api/admin/files/download/" + fileName;

        log.info("管理员导出用户数据: fileName={}", fileName);
        return downloadUrl;
    }

    /**
     * 生成随机密码
     */
    private String generateRandomPassword() {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        Random random = new Random();
        StringBuilder password = new StringBuilder();

        for (int i = 0; i < 8; i++) {
            password.append(chars.charAt(random.nextInt(chars.length())));
        }

        return password.toString();
    }
}

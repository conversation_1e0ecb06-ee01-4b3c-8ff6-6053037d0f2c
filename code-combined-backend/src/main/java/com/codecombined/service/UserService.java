package com.codecombined.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.codecombined.dto.LoginRequest;
import com.codecombined.dto.RegisterRequest;
import com.codecombined.dto.request.UserUpdateRequest;
import com.codecombined.dto.request.UserQueryRequest;
import com.codecombined.dto.response.UserListResponse;
import com.codecombined.dto.response.UserDetailResponse;
import com.codecombined.entity.User;

import java.util.List;

/**
 * 用户服务接口
 * 
 * <AUTHOR> Team
 * @since 2025-06-22
 */
public interface UserService extends IService<User> {

    /**
     * 用户注册
     */
    void register(RegisterRequest request);

    /**
     * 用户登录
     */
    String login(LoginRequest request);

    /**
     * 用户认证（用于验证邮箱和密码）
     */
    User authenticate(String email, String password);

    /**
     * 发送邮箱验证码
     */
    void sendEmailCode(String email);

    /**
     * 根据邮箱查询用户
     */
    User findByEmail(String email);

    /**
     * 根据用户名查询用户
     */
    User findByUsername(String username);

    /**
     * 更新用户最后登录信息
     */
    void updateLastLogin(Long userId, String ip);

    /**
     * 统计总用户数
     */
    Long countTotalUsers();

    /**
     * 统计活跃用户数
     */
    Long countActiveUsers();

    // ==================== 管理员专用方法 ====================

    /**
     * 管理员分页查询用户列表
     */
    IPage<UserListResponse> getUserListForAdmin(Page<User> page, UserQueryRequest queryRequest);

    /**
     * 管理员获取用户详细信息
     */
    UserDetailResponse getUserDetailForAdmin(Long userId);

    /**
     * 管理员创建用户
     */
    Long createUserByAdmin(UserCreateRequest request);

    /**
     * 管理员更新用户信息
     */
    void updateUserByAdmin(Long userId, UserUpdateRequest request);

    /**
     * 管理员删除用户（软删除）
     */
    void deleteUserByAdmin(Long userId);

    /**
     * 管理员批量删除用户
     */
    void batchDeleteUsersByAdmin(List<Long> userIds);

    /**
     * 管理员启用/禁用用户
     */
    void toggleUserStatusByAdmin(Long userId, Integer status);

    /**
     * 管理员批量启用/禁用用户
     */
    void batchToggleUserStatusByAdmin(List<Long> userIds, Integer status);

    /**
     * 管理员重置用户密码
     */
    String resetUserPasswordByAdmin(Long userId);

    /**
     * 管理员获取用户统计信息
     */
    Object getUserStatisticsForAdmin();

    /**
     * 管理员导出用户数据
     */
    String exportUsersForAdmin(UserQueryRequest queryRequest);
}

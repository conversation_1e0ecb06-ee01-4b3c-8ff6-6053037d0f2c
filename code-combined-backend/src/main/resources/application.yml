server:
  port: 8080

spring:
  application:
    name: code-combined

  profiles:
    active: dev

  # 邮件配置
  mail:
    host: smtp.qq.com
    port: 587
    username: <EMAIL>
    password: your-email-password
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true


# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    cache-enabled: false
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.codecombined.entity

# JWT配置
jwt:
  secret: code-combined-secret-key-2025-must-be-at-least-32-characters-long
  expiration: 86400  # 24小时（秒）

# 日志配置
logging:
  level:
    com.codecombined: debug
    org.springframework.security: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Knife4j配置
knife4j:
  enable: true
  setting:
    language: zh_cn
    enable-version: true
    enable-swagger-models: true
    enable-after-script: true
    enable-filter-multipart-api-method-type: POST
    enable-filter-multipart-apis: false
    enable-request-cache: true
    enable-host: false

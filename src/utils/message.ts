import Swal from 'sweetalert2'
import 'sweetalert2/dist/sweetalert2.min.css'

// 创建一个可复用的Toast实例
const Toast = Swal.mixin({
  toast: true,
  position: 'top-end',
  showConfirmButton: false,
  timer: 3000,
  timerProgressBar: true,
  didOpen: (toast) => {
    toast.addEventListener('mouseenter', Swal.stopTimer)
    toast.addEventListener('mouseleave', Swal.resumeTimer)
  }
})

/**
 * 显示成功消息
 * @param message 消息内容
 */
export function showSuccess(message: string): void {
  Toast.fire({
    icon: 'success',
    title: '成功',
    text: message,
  })
}

/**
 * 显示错误消息
 * @param message 消息内容
 */
export function showError(message: string): void {
  Toast.fire({
    icon: 'error',
    title: '错误',
    text: message,
  })
}

/**
 * 显示警告消息
 * @param message 消息内容
 */
export function showWarning(message: string): void {
  Toast.fire({
    icon: 'warning',
    title: '警告',
    text: message,
  })
}

/**
 * 显示信息消息
 * @param message 消息内容
 */
export function showInfo(message: string): void {
  Toast.fire({
    icon: 'info',
    title: '信息',
    text: message,
  })
}

/**
 * 显示一个确认对话框
 * @param message 对话框内容
 * @param title 标题 (可选)
 * @returns Promise<boolean> 用户是否点击了确认
 */
export function showConfirm(message: string, title = '请确认'): Promise<boolean> {
  return Swal.fire({
    title: title,
    text: message,
    icon: 'question',
    showCancelButton: true,
    confirmButtonColor: '#3085d6',
    cancelButtonColor: '#d33',
    confirmButtonText: '确认',
    cancelButtonText: '取消'
  }).then((result) => {
    return result.isConfirmed
  })
}

/**
 * 显示一个带输入框的对话框
 * @param title 标题
 * @param label 输入框的标签
 * @param defaultValue 输入框的默认值 (可选)
 * @returns Promise<string | null> 用户输入的内容，如果取消则为null
 */
export function showPrompt(title: string, label: string, defaultValue = ''): Promise<string | null> {
  return Swal.fire({
    title: title,
    input: 'text',
    inputLabel: label,
    inputValue: defaultValue,
    showCancelButton: true,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    inputValidator: (value) => {
      if (!value) {
        return '内容不能为空！'
      }
    }
  }).then((result) => {
    if (result.isConfirmed) {
      return result.value
    }
    return null
  })
} 